package config

// WorkflowConcurrency 工作流并发控制配置
type WorkflowConcurrency struct {
	MaxGlobalWorkers        int  `mapstructure:"max-global-workers" json:"max-global-workers" yaml:"max-global-workers"`                      // 全局最大并发工作线程数
	DefaultBatchConcurrency int  `mapstructure:"default-batch-concurrency" json:"default-batch-concurrency" yaml:"default-batch-concurrency"` // 默认批量并发数
	EnableMetrics           bool `mapstructure:"enable-metrics" json:"enable-metrics" yaml:"enable-metrics"`                                  // 是否启用指标收集
	MetricsResetInterval    int  `mapstructure:"metrics-reset-interval" json:"metrics-reset-interval" yaml:"metrics-reset-interval"`          // 指标重置间隔(小时)
	QueueTimeoutMs          int  `mapstructure:"queue-timeout-ms" json:"queue-timeout-ms" yaml:"queue-timeout-ms"`                            // 队列等待超时时间(毫秒)
	EnableHealthCheck       bool `mapstructure:"enable-health-check" json:"enable-health-check" yaml:"enable-health-check"`                   // 是否启用健康检查
}

// GetDefaultWorkflowConcurrency 获取默认工作流并发控制配置
func GetDefaultWorkflowConcurrency() WorkflowConcurrency {
	return WorkflowConcurrency{
		MaxGlobalWorkers:        2,     // 默认最大2个全局并发
		DefaultBatchConcurrency: 2,     // 默认批量并发数2
		EnableMetrics:           true,  // 启用指标收集
		MetricsResetInterval:    24,    // 24小时重置一次指标
		QueueTimeoutMs:          30000, // 30秒队列超时
		EnableHealthCheck:       true,  // 启用健康检查
	}
}

// Validate 验证配置参数
func (w *WorkflowConcurrency) Validate() error {
	if w.MaxGlobalWorkers <= 0 || w.MaxGlobalWorkers > 10 {
		w.MaxGlobalWorkers = 2 // 设置为默认值
	}

	if w.DefaultBatchConcurrency <= 0 || w.DefaultBatchConcurrency > w.MaxGlobalWorkers {
		w.DefaultBatchConcurrency = w.MaxGlobalWorkers // 不能超过全局最大值
	}

	if w.QueueTimeoutMs <= 0 {
		w.QueueTimeoutMs = 30000 // 默认30秒
	}

	if w.MetricsResetInterval <= 0 {
		w.MetricsResetInterval = 24 // 默认24小时
	}

	return nil
}
