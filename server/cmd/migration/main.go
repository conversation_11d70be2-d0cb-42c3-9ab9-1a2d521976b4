package main

import (
	"flag"
	"fmt"
	"log"
	"os"

	"github.com/flipped-aurora/gin-vue-admin/server/core"
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/initialize"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
)

func main() {
	var action = flag.String("action", "migrate", "操作类型: migrate(迁移), validate(验证), rollback(回滚), status(状态)")
	flag.Parse()

	// 初始化配置
	global.GVA_VP = core.Viper()
	global.GVA_LOG = core.Zap()
	global.GVA_DB = initialize.Gorm()

	switch *action {
	case "migrate":
		fmt.Println("开始执行数据迁移...")
		result, err := utils.MigrateTenantUsersToGlobal()
		if err != nil {
			log.Fatalf("迁移失败: %v", err)
		}
		printMigrationResult(result)
		
	case "validate":
		fmt.Println("开始验证迁移数据...")
		result, err := utils.ValidateMigrationData()
		if err != nil {
			log.Fatalf("验证失败: %v", err)
		}
		printMigrationResult(result)
		
	case "rollback":
		fmt.Print("确定要回滚迁移数据吗？这将删除所有全局用户和关系数据！(y/N): ")
		var confirm string
		fmt.Scanln(&confirm)
		if confirm != "y" && confirm != "Y" {
			fmt.Println("取消回滚操作")
			return
		}
		
		fmt.Println("开始回滚迁移数据...")
		if err := utils.RollbackMigration(); err != nil {
			log.Fatalf("回滚失败: %v", err)
		}
		fmt.Println("回滚完成")
		
	case "status":
		fmt.Println("获取迁移状态...")
		status, err := utils.GetMigrationStatus()
		if err != nil {
			log.Fatalf("获取状态失败: %v", err)
		}
		printStatus(status)
		
	default:
		fmt.Printf("未知操作: %s\n", *action)
		fmt.Println("支持的操作: migrate, validate, rollback, status")
		os.Exit(1)
	}
}

func printMigrationResult(result *utils.MigrationResult) {
	fmt.Printf("\n=== 迁移结果 ===\n")
	fmt.Printf("总用户数: %d\n", result.TotalUsers)
	fmt.Printf("迁移用户数: %d\n", result.MigratedUsers)
	fmt.Printf("跳过用户数: %d\n", result.SkippedUsers)
	fmt.Printf("错误用户数: %d\n", result.ErrorUsers)
	fmt.Printf("总关系数: %d\n", result.TotalRelations)
	fmt.Printf("迁移关系数: %d\n", result.MigratedRelations)
	
	if len(result.Errors) > 0 {
		fmt.Printf("\n=== 错误信息 ===\n")
		for _, err := range result.Errors {
			fmt.Printf("- %s\n", err)
		}
	}
	fmt.Printf("\n")
}

func printStatus(status map[string]interface{}) {
	fmt.Printf("\n=== 迁移状态 ===\n")
	fmt.Printf("全局用户表存在: %v\n", status["global_users_table_exists"])
	fmt.Printf("用户租户关系表存在: %v\n", status["user_tenant_relations_table_exists"])
	fmt.Printf("全局用户数量: %v\n", status["global_users_count"])
	fmt.Printf("用户租户关系数量: %v\n", status["user_tenant_relations_count"])
	fmt.Printf("租户用户数量: %v\n", status["tenant_users_count"])
	
	if indexes, ok := status["indexes"].(map[string]bool); ok {
		fmt.Printf("\n=== 索引状态 ===\n")
		for indexName, exists := range indexes {
			fmt.Printf("%s: %v\n", indexName, exists)
		}
	}
	fmt.Printf("\n")
} 