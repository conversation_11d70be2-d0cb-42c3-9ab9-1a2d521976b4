package autoProcess

import (
	"time"

	"gorm.io/gorm"
)

// WorkflowBatchRuns 工作流批量执行记录表
type WorkflowBatchRuns struct {
	ID             uint           `json:"id" gorm:"primarykey"`
	WorkflowID     int            `json:"workflow_id" gorm:"not null;comment:工作流ID"`
	ExecutedBy     uint           `json:"executed_by" gorm:"not null;comment:执行用户ID"`
	TotalCount     int            `json:"total_count" gorm:"not null;comment:总任务数"`
	SuccessCount   int            `json:"success_count" gorm:"default:0;comment:成功任务数"`
	FailedCount    int            `json:"failed_count" gorm:"default:0;comment:失败任务数"`
	RunningCount   int            `json:"running_count" gorm:"default:0;comment:运行中任务数"`
	CancelledCount int            `json:"cancelled_count" gorm:"default:0;comment:取消任务数"`
	Status         string         `json:"status" gorm:"type:varchar(20);not null;default:'pending';comment:状态:pending,running,completed,failed,partial_failed,cancelled"`
	StartTime      *time.Time     `json:"start_time" gorm:"comment:开始时间"`
	EndTime        *time.Time     `json:"end_time" gorm:"comment:结束时间"`
	CallbackURL    string         `json:"callback_url" gorm:"type:text;comment:回调地址"`
	Priority       int            `json:"priority" gorm:"default:1;comment:优先级"`
	MaxConcurrent  int            `json:"max_concurrent" gorm:"default:6;comment:最大并发数"`
	SingleTimeout  int            `json:"single_timeout" gorm:"default:900;comment:单个工作流超时时间(秒)"`
	BatchTimeout   int            `json:"batch_timeout" gorm:"default:1800;comment:批量执行超时时间(秒)"`
	RetryAttempts  int            `json:"retry_attempts" gorm:"default:3;comment:重试次数"`
	IsCancelled    bool           `json:"is_cancelled" gorm:"default:false;comment:是否已取消"`
	CancelReason   string         `json:"cancel_reason" gorm:"type:text;comment:取消原因"`
	ErrorLogs      string         `json:"error_logs" gorm:"type:longtext;comment:错误日志"`
	SuccessLogs    string         `json:"success_logs" gorm:"type:longtext;comment:成功日志"`
	DetailLogs     string         `json:"detail_logs" gorm:"type:longtext;comment:详细执行日志"`
	CreatedAt      time.Time      `json:"created_at"`
	UpdatedAt      time.Time      `json:"updated_at"`
	DeletedAt      gorm.DeletedAt `json:"deleted_at" gorm:"index"`
}

// 注意：BatchExecutionItem 表已被删除，批量执行的详细信息现在通过扩展 WorkflowRuns 表来实现

// TableName 设置表名
func (WorkflowBatchRuns) TableName() string {
	return "workflow_batch_runs"
}

// BatchExecutionStatus 批量执行状态常量
const (
	BatchStatusPending       = "pending"        // 等待执行
	BatchStatusRunning       = "running"        // 执行中
	BatchStatusCompleted     = "completed"      // 全部完成
	BatchStatusFailed        = "failed"         // 全部失败
	BatchStatusPartialFailed = "partial_failed" // 部分失败
	BatchStatusCancelled     = "cancelled"      // 已取消
)

// BatchExecutionItemStatus 批量执行项状态常量
const (
	ItemStatusPending   = "pending"   // 等待执行
	ItemStatusRunning   = "running"   // 执行中
	ItemStatusCompleted = "completed" // 完成
	ItemStatusFailed    = "failed"    // 失败
	ItemStatusCancelled = "cancelled" // 取消
)

// BatchExecutionConfig 批量执行配置
type BatchExecutionConfig struct {
	MaxConcurrent int           `json:"max_concurrent"` // 最大并发数
	SingleTimeout time.Duration `json:"single_timeout"` // 单个工作流超时时间
	BatchTimeout  time.Duration `json:"batch_timeout"`  // 批量执行超时时间
	RetryAttempts int           `json:"retry_attempts"` // 重试次数
}

// DefaultBatchExecutionConfig 默认批量执行配置
func DefaultBatchExecutionConfig() BatchExecutionConfig {
	// 从配置读取默认批量并发数，如果没有配置则使用默认值2
	maxConcurrent := 2
	// 注意：由于这个包不能直接引用global包，所以保持默认值
	// 实际的配置会在调用方进行设置

	return BatchExecutionConfig{
		MaxConcurrent: maxConcurrent, // 降低单批次并发数，配合全局并发控制
		SingleTimeout: 15 * time.Minute,
		BatchTimeout:  30 * time.Minute,
		RetryAttempts: 0,
	}
}

// IsCompleted 判断批量执行是否完成
func (b *WorkflowBatchRuns) IsCompleted() bool {
	return b.Status == BatchStatusCompleted ||
		b.Status == BatchStatusFailed ||
		b.Status == BatchStatusPartialFailed ||
		b.Status == BatchStatusCancelled
}

// IsCancellable 判断是否可以取消
func (b *WorkflowBatchRuns) IsCancellable() bool {
	return b.Status == BatchStatusPending || b.Status == BatchStatusRunning
}

// GetCompletedCount 获取已完成的任务数（成功+失败+取消）
func (b *WorkflowBatchRuns) GetCompletedCount() int {
	return b.SuccessCount + b.FailedCount + b.CancelledCount
}

// GetPendingCount 获取待执行的任务数
func (b *WorkflowBatchRuns) GetPendingCount() int {
	return b.TotalCount - b.GetCompletedCount() - b.RunningCount
}

// UpdateStatus 更新批量执行状态
func (b *WorkflowBatchRuns) UpdateStatus() {
	if b.IsCancelled {
		b.Status = BatchStatusCancelled
		return
	}

	completedCount := b.GetCompletedCount()

	if completedCount == b.TotalCount {
		// 全部完成
		if b.FailedCount == 0 && b.CancelledCount == 0 {
			b.Status = BatchStatusCompleted
		} else if b.SuccessCount == 0 {
			b.Status = BatchStatusFailed
		} else {
			b.Status = BatchStatusPartialFailed
		}
	} else if b.RunningCount > 0 || completedCount > 0 {
		// 有任务在执行或已完成
		b.Status = BatchStatusRunning
	} else {
		// 等待执行
		b.Status = BatchStatusPending
	}
}
