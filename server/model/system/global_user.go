package system

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/google/uuid"
	"time"
)

// GlobalUser 全局用户表
type GlobalUser struct {
	global.GVA_MODEL
	UUID      uuid.UUID `json:"uuid" gorm:"index;comment:用户UUID"`
	Username  string    `json:"username" gorm:"uniqueIndex;size:191;not null;comment:全局唯一用户名"`
	Password  string    `json:"-" gorm:"not null;comment:加密密码"`
	Email     string    `json:"email" gorm:"uniqueIndex;size:255;comment:邮箱"`
	Phone     string    `json:"phone" gorm:"index;size:20;comment:手机号"`
	TenantId  string    `json:"tenant_id" gorm:"index;size:50;not null;comment:所属租户ID"`
	IsActive  bool      `json:"is_active" gorm:"default:true;comment:是否激活"`
}

func (GlobalUser) TableName() string {
	return "global_users"
}

// UserTenantRelation 用户租户关系表
type UserTenantRelation struct {
	ID             uint      `json:"id" gorm:"primaryKey"`
	GlobalUserId   uint      `json:"global_user_id" gorm:"not null;comment:全局用户ID"`
	TenantId       string    `json:"tenant_id" gorm:"size:50;not null;comment:租户ID"`
	TenantUserId   uint      `json:"tenant_user_id" gorm:"not null;comment:租户内用户ID"`
	IsPrimary      bool      `json:"is_primary" gorm:"default:false;comment:是否为主租户"`
	CreatedAt      time.Time `json:"created_at"`
	UpdatedAt      time.Time `json:"updated_at"`
	GlobalUser     GlobalUser `json:"global_user" gorm:"foreignKey:GlobalUserId;references:ID"`
}

func (UserTenantRelation) TableName() string {
	return "user_tenant_relations"
} 