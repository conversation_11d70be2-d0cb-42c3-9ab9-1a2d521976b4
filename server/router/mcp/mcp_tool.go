package mcp

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type MCPToolRouter struct{}

// InitMCPToolRouter 初始化MCP工具管理路由
func (r *MCPToolRouter) InitMCPToolRouter(PrivateGroup, PublicGroup *gin.RouterGroup) {
	mcpToolApi := v1.ApiGroupApp.MCPApiGroup.MCPToolApi

	// 私有路由组（租户认证已在路由组中处理）
	mcpPrivateRouter := PrivateGroup.Group("mcp").Use(
		middleware.MCPSecurityHeaders(),
		middleware.MCPRequestID(),
		middleware.MCPRequestValidator(),
	)

	// 查询路由组，不需要操作记录
	mcpQueryRouter := mcpPrivateRouter.Use()

	// 工具操作路由组，应用操作记录中间件
	mcpToolRouter := mcpPrivateRouter.Use(middleware.OperationRecord())

	// 查询路由（需要租户认证但不需要操作记录）
	{
		mcpQueryRouter.GET("tools", mcpToolApi.GetToolList)                     // 获取工具列表
		mcpQueryRouter.GET(":server/:tool/schema", mcpToolApi.GetToolSchema)    // 获取工具Schema
	}

	// 工具调用路由（需要租户认证和操作记录）
	{
		mcpToolRouter.POST(":server/:tool", mcpToolApi.CallTool) // 调用工具
	}
}
