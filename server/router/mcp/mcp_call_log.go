package mcp

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type MCPCallLogRouter struct{}

// InitMCPCallLogRouter 初始化MCP调用日志路由
func (r *MCPCallLogRouter) InitMCPCallLogRouter(PrivateGroup, PublicGroup *gin.RouterGroup) {
	mcpCallLogApi := v1.ApiGroupApp.MCPApiGroup.MCPCallLogApi

	// 私有路由组（租户认证已在路由组中处理）
	mcpPrivateRouter := PrivateGroup.Group("mcp").Use(
		middleware.MCPSecurityHeaders(),
		middleware.MCPRequestID(),
		middleware.MCPRequestValidator(),
	)

	// 查询路由组，不需要操作记录
	mcpQueryRouter := mcpPrivateRouter.Use()

	// 管理操作路由组，应用操作记录中间件
	mcpCallLogRouter := mcpPrivateRouter.Use(middleware.OperationRecord())

	// 查询路由（需要租户认证但不需要操作记录）
	{
		mcpQueryRouter.GET("call-logs", mcpCallLogApi.GetCallLogList)        // 获取调用日志列表
		mcpQueryRouter.GET("call-logs/:id", mcpCallLogApi.GetCallLogDetail)  // 获取调用日志详情
		mcpQueryRouter.GET("call-logs/stats", mcpCallLogApi.GetCallLogStats) // 获取调用日志统计
	}

	// 管理操作路由（需要租户认证和操作记录）
	{
		mcpCallLogRouter.DELETE("call-logs/:id", mcpCallLogApi.DeleteCallLog)              // 删除调用日志
		mcpCallLogRouter.POST("call-logs/batch-delete", mcpCallLogApi.BatchDeleteCallLogs) // 批量删除调用日志
		mcpCallLogRouter.POST("call-logs/clean", mcpCallLogApi.CleanOldLogs)               // 清理旧日志
	}
}
