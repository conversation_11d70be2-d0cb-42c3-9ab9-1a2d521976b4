package mcp

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type MCPHealthRouter struct{}

// InitMCPHealthRouter 初始化MCP健康检查路由
func (r *MCPHealthRouter) InitMCPHealthRouter(PrivateGroup, PublicGroup *gin.RouterGroup) {
	mcpHealthApi := v1.ApiGroupApp.MCPApiGroup.MCPHealthApi

	// 私有路由组（租户认证已在路由组中处理）
	mcpPrivateRouter := PrivateGroup.Group("mcp").Use(
		middleware.MCPSecurityHeaders(),
		middleware.MCPRequestID(),
		middleware.MCPRequestValidator(),
	)

	// 查询路由组，不需要操作记录
	mcpQueryRouter := mcpPrivateRouter.Use()

	// 健康检查操作路由组，应用操作记录中间件
	mcpHealthRouter := mcpPrivateRouter.Use(middleware.OperationRecord())

	// 查询路由（需要租户认证但不需要操作记录）
	{
		mcpQueryRouter.GET("services/:server/health", mcpHealthApi.CheckServiceHealth) // 检查服务健康状态
	}

	// 健康检查操作路由（需要租户认证和操作记录）
	{
		mcpHealthRouter.POST("services/health/check-all", mcpHealthApi.CheckAllServicesHealth) // 检查所有服务健康状态
	}
}
