package mcp

import (
	v1 "github.com/flipped-aurora/gin-vue-admin/server/api/v1"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type MCPServiceRouter struct{}

// InitMCPServiceRouter 初始化MCP服务管理路由
func (r *MCPServiceRouter) InitMCPServiceRouter(PrivateGroup, PublicGroup *gin.RouterGroup) {
	mcpServiceApi := v1.ApiGroupApp.MCPApiGroup.MCPServiceApi

	// 私有路由组（租户认证已在路由组中处理）
	mcpPrivateRouter := PrivateGroup.Group("mcp").Use(
		middleware.MCPSecurityHeaders(),
		middleware.MCPRequestID(),
		middleware.MCPRequestValidator(),
	)

	// 管理操作路由组，应用操作记录中间件
	mcpServiceRouter := mcpPrivateRouter.Use(middleware.OperationRecord())

	// 查询路由组，不需要操作记录
	mcpQueryRouter := mcpPrivateRouter.Use()

	// 私有路由（需要租户认证）
	{
		mcpServiceRouter.POST("services/register", mcpServiceApi.RegisterService)          // 注册MCP服务
		mcpServiceRouter.PUT("services", mcpServiceApi.UpdateService)                      // 更新服务
		mcpServiceRouter.DELETE("services/:id", mcpServiceApi.DeleteService)               // 删除服务
		mcpServiceRouter.POST("services/toggle-status", mcpServiceApi.ToggleServiceStatus) // 切换服务状态
		mcpServiceRouter.POST("services/batch-update", mcpServiceApi.BatchUpdateServices)  // 批量更新服务
		mcpServiceRouter.POST("services/batch-delete", mcpServiceApi.BatchDeleteServices)  // 批量删除服务
		mcpServiceRouter.POST("services/sync-tools", mcpServiceApi.SyncTools)              // 同步单个服务工具
		mcpServiceRouter.POST("services/batch-sync-tools", mcpServiceApi.BatchSyncTools)   // 批量同步服务工具
		mcpServiceRouter.POST("services/sync-all-tools", mcpServiceApi.SyncAllTools)       // 全量同步工具
	}

	// 查询路由（需要租户认证但不需要操作记录）
	{
		mcpQueryRouter.GET("services", mcpServiceApi.GetServiceList) // 获取服务列表
	}
}
