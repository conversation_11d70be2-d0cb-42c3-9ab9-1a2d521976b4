package autoProcess

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type WorkflowRunsRouter struct{}

// InitWorkflowRunsRouter 初始化 WorkflowRuns 路由信息
func (s *WorkflowRunsRouter) InitWorkflowRunsRouter(Router *gin.RouterGroup, PublicRouter *gin.RouterGroup) {
	workflowRunsRouter := Router.Group("workflowRuns").Use(middleware.OperationRecord())
	workflowRunsRouterWithoutRecord := Router.Group("workflowRuns")
	{
		workflowRunsRouter.DELETE("deleteWorkflowRuns", workflowRunsApi.DeleteWorkflowRuns)           // 删除工作流执行记录
		workflowRunsRouter.DELETE("deleteWorkflowRunsByIds", workflowRunsApi.DeleteWorkflowRunsByIds) // 批量删除工作流执行记录
	}
	{
		workflowRunsRouterWithoutRecord.GET("findWorkflowRuns", workflowRunsApi.FindWorkflowRuns)       // 根据ID获取工作流执行记录
		workflowRunsRouterWithoutRecord.GET("getWorkflowRunsList", workflowRunsApi.GetWorkflowRunsList) // 获取工作流执行记录列表
	}
}
