package mcp

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"
	mcpReq "github.com/flipped-aurora/gin-vue-admin/server/model/mcp/request"
	mcpRes "github.com/flipped-aurora/gin-vue-admin/server/model/mcp/response"
	"github.com/flipped-aurora/gin-vue-admin/server/pkg/einoext/components/lambda"
	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type MCPToolService struct{}

// GetToolList 获取工具列表
func (s *MCPToolService) GetToolList(ctx context.Context, req mcpReq.MCPToolSearch) (mcpRes.ToolListResponse, error) {
	var response mcpRes.ToolListResponse
	var tools []mcp.MCPTool
	var total int64

	db := global.GVA_DB.Model(&mcp.MCPTool{}).Where("is_deleted = 0")

	// 添加搜索条件
	if req.ServerName != "" {
		db = db.Where("server_name = ?", req.ServerName)
	}
	if req.ToolName != "" {
		db = db.Where("tool_name LIKE ?", "%"+req.ToolName+"%")
	}
	if req.Category != "" {
		db = db.Where("category = ?", req.Category)
	}
	if req.Status != nil {
		db = db.Where("status = ?", *req.Status)
	}

	// 如果需要强制刷新，则同步工具信息
	if req.Refresh {
		if err := s.refreshAllTools(ctx); err != nil {
			global.GVA_LOG.Error(fmt.Sprintf("刷新工具信息失败: %v", err))
		}
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return response, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := db.Offset(offset).Limit(req.PageSize).Order("synced_at DESC").Find(&tools).Error; err != nil {
		return response, err
	}

	// 构建工具信息
	var toolInfos []mcpRes.ToolInfo
	for _, tool := range tools {
		// 解析Schema
		var inputSchema map[string]interface{}
		if len(tool.SchemaJSON) > 0 {
			json.Unmarshal(tool.SchemaJSON, &inputSchema)
		}

		// 解析Examples
		var examples []map[string]interface{}
		if len(tool.Examples) > 0 {
			json.Unmarshal(tool.Examples, &examples)
		}

		// 计算成功率
		successRate := float64(0)
		if tool.CallCount > 0 {
			successRate = float64(tool.SuccessCount) / float64(tool.CallCount) * 100
		}

		// 获取参数信息（从Schema中提取）
		parameters := make(map[string]interface{})
		if inputSchema != nil {
			if props, ok := inputSchema["properties"].(map[string]interface{}); ok {
				parameters = props
			}
		}

		toolInfos = append(toolInfos, mcpRes.ToolInfo{
			Name:             tool.ToolName,
			DisplayName:      tool.DisplayName,
			Description:      tool.Description,
			Category:         tool.Category,
			Version:          tool.Version,
			Status:           s.getStatusString(tool.Status),
			ServerName:       tool.ServerName,
			APIEndpoint:      tool.APIEndpoint,
			HTTPMethod:       tool.HTTPMethod,
			Parameters:       parameters,
			InputSchema:      inputSchema,
			CallCount:        tool.CallCount,
			SuccessCount:     tool.SuccessCount,
			SuccessRate:      successRate,
			AvgExecutionTime: tool.AvgExecutionTime,
		})
	}

	// 获取服务器信息
	var servers []mcpRes.ServerInfo
	var services []mcp.MCPService
	global.GVA_DB.Where("is_deleted = 0").Find(&services)

	for _, service := range services {
		var toolsCount int64
		global.GVA_DB.Model(&mcp.MCPTool{}).Where("server_id = ? AND is_deleted = 0", service.ID).Count(&toolsCount)

		servers = append(servers, mcpRes.ServerInfo{
			ServerName:      service.ServerName,
			DisplayName:     service.DisplayName,
			Status:          s.getStatusString(service.Status),
			ToolsCount:      int(toolsCount),
			LastHealthCheck: service.LastHealthCheck,
		})
	}

	response.Tools = toolInfos
	response.Total = total
	response.Servers = servers

	return response, nil
}

// GetToolSchema 获取工具Schema信息
func (s *MCPToolService) GetToolSchema(ctx context.Context, serverName, toolName string) (mcpRes.ToolSchemaResponse, error) {
	var response mcpRes.ToolSchemaResponse
	var tool mcp.MCPTool

	if err := global.GVA_DB.Where("server_name = ? AND tool_name = ? AND is_deleted = 0", serverName, toolName).First(&tool).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return response, fmt.Errorf("工具 '%s/%s' 不存在", serverName, toolName)
		}
		return response, err
	}

	// 解析Schema
	var inputSchema map[string]interface{}
	if len(tool.SchemaJSON) > 0 {
		json.Unmarshal(tool.SchemaJSON, &inputSchema)
	}

	// 解析Examples
	var examples []map[string]interface{}
	if len(tool.Examples) > 0 {
		json.Unmarshal(tool.Examples, &examples)
	}

	// 获取参数信息
	parameters := make(map[string]interface{})
	if inputSchema != nil {
		if props, ok := inputSchema["properties"].(map[string]interface{}); ok {
			parameters = props
		}
	}

	// 计算成功率
	successRate := float64(0)
	if tool.CallCount > 0 {
		successRate = float64(tool.SuccessCount) / float64(tool.CallCount) * 100
	}

	response = mcpRes.ToolSchemaResponse{
		ToolName:         tool.ToolName,
		ServerName:       tool.ServerName,
		Description:      tool.Description,
		InputSchema:      inputSchema,
		Parameters:       parameters,
		Examples:         parameters,
		APIEndpoint:      tool.APIEndpoint,
		HTTPMethod:       tool.HTTPMethod,
		LastUpdated:      tool.SyncedAt,
		CallCount:        tool.CallCount,
		SuccessCount:     tool.SuccessCount,
		SuccessRate:      successRate,
		AvgExecutionTime: tool.AvgExecutionTime,
	}

	return response, nil
}

// CallTool 调用MCP工具
func (s *MCPToolService) CallTool(ctx context.Context, serverName, toolName string, req mcpReq.ToolCallRequest) (mcpRes.ToolCallResponse, error) {
	var response mcpRes.ToolCallResponse

	// 获取工具信息
	var tool mcp.MCPTool
	if err := global.GVA_DB.Where("server_name = ? AND tool_name = ? AND is_deleted = 0", serverName, toolName).First(&tool).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return response, fmt.Errorf("工具 '%s/%s' 不存在", serverName, toolName)
		}
		return response, err
	}

	// 获取服务信息
	var service mcp.MCPService
	if err := global.GVA_DB.Where("server_name = ? AND is_deleted = 0", serverName).First(&service).Error; err != nil {
		return response, fmt.Errorf("服务 '%s' 不存在或已禁用", serverName)
	}

	// 添加调试日志 - 服务配置
	global.GVA_LOG.Info(fmt.Sprintf("MCP服务配置 - 服务名: %s, 端点: %s, 超时时间: %d秒, 重试次数: %d, 状态: %d",
		service.ServerName, service.Endpoint, service.Timeout, service.RetryCount, service.Status))

	// 检查服务状态
	if service.Status != 1 {
		return response, fmt.Errorf("服务 '%s' 当前不可用", serverName)
	}

	// 构建MCP调用参数
	input := map[string]interface{}{
		"req": map[string]interface{}{
			"serverURL": service.Endpoint,
			"toolParams": map[string]interface{}{
				"method": "tools/call",
				"params": map[string]interface{}{
					"name":      toolName,
					"arguments": req.Arguments,
				},
			},
		},
	}

	// 创建执行器配置
	config := &lambda.ToolExecutorConfig{
		ServerURL:       service.Endpoint,
		Timeout:         service.Timeout,
		RetryCount:      service.RetryCount,
		ValidateParams:  true,
		EnableProgress:  true,
		CacheToolList:   true,
		CacheExpiration: 300,
	}

	// 添加调试日志
	global.GVA_LOG.Info(fmt.Sprintf("MCP工具调用配置 - 服务: %s, 工具: %s, 超时时间: %d秒, 重试次数: %d",
		serverName, toolName, service.Timeout, service.RetryCount))

	// 执行工具调用
	startTime := time.Now()
	result, err := lambda.UnifiedToolCall(ctx, input, config)
	executionTime := time.Since(startTime).Seconds()

	// 判断是否为API调用：检查上下文中是否有gin.Context
	// 如果不是API调用（如内部服务调用、定时任务等），则需要记录日志
	isAPICall := s.isAPICall(ctx)
	if !isAPICall {
		global.GVA_LOG.Info(fmt.Sprintf("检测到非API调用，将记录调用日志 - 服务: %s, 工具: %s", serverName, toolName))
	} else {
		global.GVA_LOG.Info(fmt.Sprintf("检测到API调用，日志已由中间件记录 - 服务: %s, 工具: %s", serverName, toolName))
	}

	// 准备调用日志（如果需要记录）
	var callLog *mcp.MCPCallLog
	if !isAPICall {
		callLog = &mcp.MCPCallLog{
			ServerID:      service.ID,
			ServerName:    serverName,
			ToolID:        &tool.ID,
			ToolName:      toolName,
			APIEndpoint:   fmt.Sprintf("internal://%s/%s", serverName, toolName), // 标记为内部调用
			RequestID:     fmt.Sprintf("internal-%d-%d", time.Now().Unix(), tool.ID),
			ExecutionTime: executionTime,
			Status:        0, // 默认失败
		}

		// 序列化请求参数
		if reqBytes, err := json.Marshal(req.Arguments); err == nil {
			callLog.RequestParams = reqBytes
		}
	}

	if err != nil {
		// 调用失败，更新服务错误计数
		global.GVA_DB.Model(&service).Updates(map[string]interface{}{
			"error_count":    gorm.Expr("error_count + 1"),
			"last_error_msg": err.Error(),
		})

		// 记录错误日志（如果需要）
		if callLog != nil {
			callLog.ErrorMessage = err.Error()
			callLog.ErrorCode = "CALL_FAILED"
		}

		response = mcpRes.ToolCallResponse{
			Success:       false,
			ToolName:      toolName,
			Result:        nil,
			ExecutionTime: executionTime,
			Timestamp:     time.Now(),
			ServerName:    serverName,
			InputParams:   req.Arguments,
			Metadata: map[string]interface{}{
				"error": err.Error(),
			},
			RawMCPResponse: nil, // 调用失败时没有原始响应
		}
	} else {
		// 调用成功
		if result.Success {
			// 记录成功日志（如果需要）
			if callLog != nil {
				callLog.Status = 1
				if respBytes, err := json.Marshal(result.Result); err == nil {
					callLog.ResponseData = respBytes
				}
			}

			response = mcpRes.ToolCallResponse{
				Success:        true,
				ToolName:       toolName,
				Result:         result.Result,
				ExecutionTime:  executionTime,
				Timestamp:      result.Timestamp,
				ServerName:     serverName,
				InputParams:    req.Arguments,
				Metadata:       result.Metadata,
				RawMCPResponse: result.RawResponse, // 添加原始MCP响应数据
			}

			// 更新工具统计信息
			s.updateToolStats(tool.ID, executionTime, true)
		} else {
			// 记录执行失败日志（如果需要）
			if callLog != nil {
				callLog.Status = 0
				callLog.ErrorMessage = result.Error
				callLog.ErrorCode = "EXECUTION_FAILED"
			}

			response = mcpRes.ToolCallResponse{
				Success:       false,
				ToolName:      toolName,
				Result:        result.Result,
				ExecutionTime: executionTime,
				Timestamp:     result.Timestamp,
				ServerName:    serverName,
				InputParams:   req.Arguments,
				Metadata: map[string]interface{}{
					"error": result.Error,
				},
				RawMCPResponse: result.RawResponse, // 即使执行失败也返回原始响应数据
			}

			// 更新工具统计信息
			s.updateToolStats(tool.ID, executionTime, false)
		}
	}

	// 保存调用日志（如果需要）
	if callLog != nil {
		global.GVA_DB.Create(callLog)
	}

	return response, nil
}

// refreshAllTools 刷新所有工具信息
func (s *MCPToolService) refreshAllTools(ctx context.Context) error {
	var services []mcp.MCPService
	if err := global.GVA_DB.Where("status = 1 AND is_deleted = 0").Find(&services).Error; err != nil {
		return err
	}

	serviceService := &MCPServiceService{}
	for _, service := range services {
		if err := serviceService.syncToolsFromMCP(ctx, service.ID); err != nil {
			global.GVA_LOG.Error(fmt.Sprintf("同步服务 %s 的工具失败: %v", service.ServerName, err))
		}
	}

	return nil
}

// updateToolStats 更新工具统计信息
func (s *MCPToolService) updateToolStats(toolID uint, executionTime float64, success bool) {
	updates := map[string]interface{}{
		"call_count": gorm.Expr("call_count + 1"),
	}

	if success {
		updates["success_count"] = gorm.Expr("success_count + 1")
	}

	// 更新平均执行时间（简化算法）
	var tool mcp.MCPTool
	if err := global.GVA_DB.First(&tool, toolID).Error; err == nil {
		newAvg := (tool.AvgExecutionTime*float64(tool.CallCount) + executionTime) / float64(tool.CallCount+1)
		updates["avg_execution_time"] = newAvg
	}

	global.GVA_DB.Model(&mcp.MCPTool{}).Where("id = ?", toolID).Updates(updates)
}

// isAPICall 判断当前调用是否为API调用
// 通过检查上下文中是否包含gin.Context来判断
func (s *MCPToolService) isAPICall(ctx context.Context) bool {
	// 尝试从上下文中获取gin.Context
	if ginCtx, ok := ctx.(*gin.Context); ok && ginCtx != nil {
		return true
	}

	// 检查上下文中是否有gin相关的值
	if ctx.Value("gin.context") != nil {
		return true
	}

	// 检查是否有HTTP相关的标识
	if ctx.Value("http.request") != nil {
		return true
	}

	return false
}

// getStatusString 获取状态字符串
func (s *MCPToolService) getStatusString(status int) string {
	switch status {
	case 0:
		return "disabled"
	case 1:
		return "active"
	case 2:
		return "error"
	default:
		return "unknown"
	}
}
