package mcp

import (
	"context"
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"
	mcpReq "github.com/flipped-aurora/gin-vue-admin/server/model/mcp/request"
	mcpRes "github.com/flipped-aurora/gin-vue-admin/server/model/mcp/response"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type MCPCallLogService struct{}

// GetCallLogList 获取调用日志列表
func (s *MCPCallLogService) GetCallLogList(TenantDB *gorm.DB, ctx context.Context, req mcpReq.CallLogSearchRequest) (mcpRes.CallLogListResponse, error) {
	var response mcpRes.CallLogListResponse
	var callLogs []mcp.MCPCallLog
	var total int64

	// 构建查询条件
	db := TenantDB.WithContext(ctx).Model(&mcp.MCPCallLog{})

	// 添加查询条件
	if req.ServerName != "" {
		db = db.Where("server_name LIKE ?", "%"+req.ServerName+"%")
	}
	if req.ToolName != "" {
		db = db.Where("tool_name LIKE ?", "%"+req.ToolName+"%")
	}
	if req.APIEndpoint != "" {
		db = db.Where("api_endpoint LIKE ?", "%"+req.APIEndpoint+"%")
	}
	if req.Status != nil {
		db = db.Where("status = ?", *req.Status)
	}
	if req.RequestID != "" {
		db = db.Where("request_id = ?", req.RequestID)
	}
	if req.TraceID != "" {
		db = db.Where("trace_id = ?", req.TraceID)
	}
	if req.UserID != nil {
		db = db.Where("user_id = ?", *req.UserID)
	}
	if req.ErrorCode != "" {
		db = db.Where("error_code = ?", req.ErrorCode)
	}

	// 时间范围查询
	if req.StartTime != "" {
		if startTime, err := time.Parse("2006-01-02 15:04:05", req.StartTime); err == nil {
			db = db.Where("created_at >= ?", startTime)
		}
	}
	if req.EndTime != "" {
		if endTime, err := time.Parse("2006-01-02 15:04:05", req.EndTime); err == nil {
			db = db.Where("created_at <= ?", endTime)
		}
	}

	// 执行时间范围查询
	if req.ExecutionTimeMin != nil {
		db = db.Where("execution_time >= ?", *req.ExecutionTimeMin)
	}
	if req.ExecutionTimeMax != nil {
		db = db.Where("execution_time <= ?", *req.ExecutionTimeMax)
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		global.GVA_LOG.Error("获取调用日志总数失败", zap.Error(err))
		return response, fmt.Errorf("获取调用日志总数失败: %w", err)
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := db.Order("created_at DESC").Limit(req.PageSize).Offset(offset).Find(&callLogs).Error; err != nil {
		global.GVA_LOG.Error("获取调用日志列表失败", zap.Error(err))
		return response, fmt.Errorf("获取调用日志列表失败: %w", err)
	}

	// 转换为响应格式
	var list []mcpRes.CallLogItem
	for _, log := range callLogs {
		list = append(list, mcpRes.CallLogItem{
			ID:            log.ID,
			ServerID:      log.ServerID,
			ServerName:    log.ServerName,
			ToolID:        log.ToolID,
			ToolName:      log.ToolName,
			APIEndpoint:   log.APIEndpoint,
			RequestID:     log.RequestID,
			ExecutionTime: log.ExecutionTime,
			Status:        log.Status,
			StatusText:    s.getStatusText(log.Status),
			ErrorCode:     log.ErrorCode,
			ErrorMessage:  log.ErrorMessage,
			ClientIP:      log.ClientIP,
			UserID:        log.UserID,
			TraceID:       log.TraceID,
			CreatedAt:     log.CreatedAt,
		})
	}

	response = mcpRes.CallLogListResponse{
		List:     list,
		Total:    total,
		Page:     req.Page,
		PageSize: req.PageSize,
	}

	return response, nil
}

// GetCallLogDetail 获取调用日志详情
func (s *MCPCallLogService) GetCallLogDetail(TenantDB *gorm.DB, ctx context.Context, id uint) (mcpRes.CallLogDetailResponse, error) {
	var response mcpRes.CallLogDetailResponse
	var callLog mcp.MCPCallLog

	if err := TenantDB.WithContext(ctx).First(&callLog, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return response, fmt.Errorf("调用日志不存在")
		}
		global.GVA_LOG.Error("获取调用日志详情失败", zap.Error(err), zap.Uint("id", id))
		return response, fmt.Errorf("获取调用日志详情失败: %w", err)
	}

	response = mcpRes.CallLogDetailResponse{
		ID:            callLog.ID,
		ServerID:      callLog.ServerID,
		ServerName:    callLog.ServerName,
		ToolID:        callLog.ToolID,
		ToolName:      callLog.ToolName,
		APIEndpoint:   callLog.APIEndpoint,
		RequestID:     callLog.RequestID,
		RequestParams: callLog.RequestParams,
		ResponseData:  callLog.ResponseData,
		ExecutionTime: callLog.ExecutionTime,
		Status:        callLog.Status,
		StatusText:    s.getStatusText(callLog.Status),
		ErrorCode:     callLog.ErrorCode,
		ErrorMessage:  callLog.ErrorMessage,
		ClientIP:      callLog.ClientIP,
		UserAgent:     callLog.UserAgent,
		UserID:        callLog.UserID,
		SessionID:     callLog.SessionID,
		TraceID:       callLog.TraceID,
		CreatedAt:     callLog.CreatedAt,
		UpdatedAt:     callLog.UpdatedAt,
	}

	return response, nil
}

// GetCallLogStats 获取调用日志统计信息
func (s *MCPCallLogService) GetCallLogStats(TenantDB *gorm.DB, ctx context.Context, req mcpReq.CallLogSearchRequest) (mcpRes.CallLogStatsResponse, error) {
	var response mcpRes.CallLogStatsResponse

	// 构建基础查询条件
	db := TenantDB.WithContext(ctx).Model(&mcp.MCPCallLog{})
	db = s.buildSearchConditions(db, req)

	// 基础统计
	var stats struct {
		TotalCalls       int64   `gorm:"column:total_calls"`
		SuccessCalls     int64   `gorm:"column:success_calls"`
		FailedCalls      int64   `gorm:"column:failed_calls"`
		AvgExecutionTime float64 `gorm:"column:avg_execution_time"`
		TotalServices    int64   `gorm:"column:total_services"`
		TotalTools       int64   `gorm:"column:total_tools"`
	}

	if err := db.Select(`
		COUNT(*) as total_calls,
		SUM(CASE WHEN status = 1 THEN 1 ELSE 0 END) as success_calls,
		SUM(CASE WHEN status = 0 THEN 1 ELSE 0 END) as failed_calls,
		AVG(execution_time) as avg_execution_time,
		COUNT(DISTINCT server_name) as total_services,
		COUNT(DISTINCT tool_name) as total_tools
	`).Scan(&stats).Error; err != nil {
		global.GVA_LOG.Error("获取调用日志统计失败", zap.Error(err))
		return response, fmt.Errorf("获取调用日志统计失败: %w", err)
	}

	// 计算成功率
	successRate := float64(0)
	if stats.TotalCalls > 0 {
		successRate = float64(stats.SuccessCalls) / float64(stats.TotalCalls) * 100
	}

	response = mcpRes.CallLogStatsResponse{
		TotalCalls:       stats.TotalCalls,
		SuccessCalls:     stats.SuccessCalls,
		FailedCalls:      stats.FailedCalls,
		SuccessRate:      successRate,
		AvgExecutionTime: stats.AvgExecutionTime,
		TotalServices:    stats.TotalServices,
		TotalTools:       stats.TotalTools,
	}

	// 获取高频错误代码统计
	topErrorCodes, err := s.getTopErrorCodes(ctx, db)
	if err != nil {
		global.GVA_LOG.Warn("获取错误代码统计失败", zap.Error(err))
	} else {
		response.TopErrorCodes = topErrorCodes
	}

	// 获取慢调用统计
	topSlowCalls, err := s.getTopSlowCalls(ctx, db)
	if err != nil {
		global.GVA_LOG.Warn("获取慢调用统计失败", zap.Error(err))
	} else {
		response.TopSlowCalls = topSlowCalls
	}

	return response, nil
}

// buildSearchConditions 构建搜索条件
func (s *MCPCallLogService) buildSearchConditions(db *gorm.DB, req mcpReq.CallLogSearchRequest) *gorm.DB {
	if req.ServerName != "" {
		db = db.Where("server_name LIKE ?", "%"+req.ServerName+"%")
	}
	if req.ToolName != "" {
		db = db.Where("tool_name LIKE ?", "%"+req.ToolName+"%")
	}
	if req.APIEndpoint != "" {
		db = db.Where("api_endpoint LIKE ?", "%"+req.APIEndpoint+"%")
	}
	if req.Status != nil {
		db = db.Where("status = ?", *req.Status)
	}
	if req.StartTime != "" {
		if startTime, err := time.Parse("2006-01-02 15:04:05", req.StartTime); err == nil {
			db = db.Where("created_at >= ?", startTime)
		}
	}
	if req.EndTime != "" {
		if endTime, err := time.Parse("2006-01-02 15:04:05", req.EndTime); err == nil {
			db = db.Where("created_at <= ?", endTime)
		}
	}
	if req.ExecutionTimeMin != nil {
		db = db.Where("execution_time >= ?", *req.ExecutionTimeMin)
	}
	if req.ExecutionTimeMax != nil {
		db = db.Where("execution_time <= ?", *req.ExecutionTimeMax)
	}
	return db
}

// getTopErrorCodes 获取高频错误代码
func (s *MCPCallLogService) getTopErrorCodes(ctx context.Context, db *gorm.DB) ([]mcpRes.ErrorCodeStats, error) {
	var results []struct {
		ErrorCode string `gorm:"column:error_code"`
		Count     int64  `gorm:"column:count"`
	}

	err := db.Select("error_code, COUNT(*) as count").
		Where("status = 0 AND error_code != ''").
		Group("error_code").
		Order("count DESC").
		Limit(10).
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	// 获取总失败次数用于计算比率
	var totalFailed int64
	db.Where("status = 0").Count(&totalFailed)

	var errorCodeStats []mcpRes.ErrorCodeStats
	for _, result := range results {
		rate := float64(0)
		if totalFailed > 0 {
			rate = float64(result.Count) / float64(totalFailed) * 100
		}

		errorCodeStats = append(errorCodeStats, mcpRes.ErrorCodeStats{
			ErrorCode: result.ErrorCode,
			Count:     result.Count,
			Rate:      rate,
		})
	}

	return errorCodeStats, nil
}

// getTopSlowCalls 获取慢调用统计
func (s *MCPCallLogService) getTopSlowCalls(ctx context.Context, db *gorm.DB) ([]mcpRes.SlowCallStats, error) {
	var results []struct {
		ServerName  string  `gorm:"column:server_name"`
		ToolName    string  `gorm:"column:tool_name"`
		APIEndpoint string  `gorm:"column:api_endpoint"`
		AvgTime     float64 `gorm:"column:avg_time"`
		CallCount   int64   `gorm:"column:call_count"`
	}

	err := db.Select(`
		server_name, tool_name, api_endpoint,
		AVG(execution_time) as avg_time,
		COUNT(*) as call_count
	`).
		Group("server_name, tool_name, api_endpoint").
		Having("avg_time > 1.0"). // 只显示平均执行时间超过1秒的
		Order("avg_time DESC").
		Limit(10).
		Scan(&results).Error

	if err != nil {
		return nil, err
	}

	var slowCallStats []mcpRes.SlowCallStats
	for _, result := range results {
		slowCallStats = append(slowCallStats, mcpRes.SlowCallStats{
			ServerName:  result.ServerName,
			ToolName:    result.ToolName,
			APIEndpoint: result.APIEndpoint,
			AvgTime:     result.AvgTime,
			CallCount:   result.CallCount,
		})
	}

	return slowCallStats, nil
}

// getStatusText 获取状态文本
func (s *MCPCallLogService) getStatusText(status int) string {
	switch status {
	case 0:
		return "失败"
	case 1:
		return "成功"
	default:
		return "未知"
	}
}

// DeleteCallLog 删除调用日志
func (s *MCPCallLogService) DeleteCallLog(TenantDB *gorm.DB, ctx context.Context, id uint) error {
	result := TenantDB.WithContext(ctx).Delete(&mcp.MCPCallLog{}, id)
	if result.Error != nil {
		global.GVA_LOG.Error("删除调用日志失败", zap.Error(result.Error), zap.Uint("id", id))
		return fmt.Errorf("删除调用日志失败: %w", result.Error)
	}

	if result.RowsAffected == 0 {
		return fmt.Errorf("调用日志不存在")
	}

	return nil
}

// BatchDeleteCallLogs 批量删除调用日志
func (s *MCPCallLogService) BatchDeleteCallLogs(TenantDB *gorm.DB, ctx context.Context, ids []uint) error {
	if len(ids) == 0 {
		return fmt.Errorf("删除ID列表不能为空")
	}

	result := TenantDB.WithContext(ctx).Delete(&mcp.MCPCallLog{}, ids)
	if result.Error != nil {
		global.GVA_LOG.Error("批量删除调用日志失败", zap.Error(result.Error), zap.Any("ids", ids))
		return fmt.Errorf("批量删除调用日志失败: %w", result.Error)
	}

	global.GVA_LOG.Info("批量删除调用日志成功",
		zap.Int64("affected_rows", result.RowsAffected),
		zap.Any("ids", ids))

	return nil
}

// CleanOldLogs 清理旧日志
func (s *MCPCallLogService) CleanOldLogs(TenantDB *gorm.DB, ctx context.Context, days int) error {
	if days <= 0 {
		return fmt.Errorf("保留天数必须大于0")
	}

	cutoffTime := time.Now().AddDate(0, 0, -days)

	result := TenantDB.WithContext(ctx).
		Where("created_at < ?", cutoffTime).
		Delete(&mcp.MCPCallLog{})

	if result.Error != nil {
		global.GVA_LOG.Error("清理旧日志失败", zap.Error(result.Error), zap.Int("days", days))
		return fmt.Errorf("清理旧日志失败: %w", result.Error)
	}

	global.GVA_LOG.Info("清理旧日志成功",
		zap.Int64("deleted_rows", result.RowsAffected),
		zap.Int("days", days))

	return nil
}
