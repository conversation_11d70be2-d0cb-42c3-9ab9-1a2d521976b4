package mcp

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"
	mcpReq "github.com/flipped-aurora/gin-vue-admin/server/model/mcp/request"
	mcpRes "github.com/flipped-aurora/gin-vue-admin/server/model/mcp/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/flipped-aurora/gin-vue-admin/server/pkg/einoext/components/lambda"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type MCPServiceService struct{}

// RegisterService 注册MCP服务
func (s *MCPServiceService) RegisterService(TenantDB *gorm.DB, ctx context.Context, req mcpReq.RegisterServiceRequest) (mcpRes.RegisterServiceResponse, error) {
	var response mcpRes.RegisterServiceResponse

	// 检查服务名是否已存在
	var existingService mcp.MCPService
	if err := TenantDB.Where("server_name = ? AND is_deleted = 0", req.ServerName).First(&existingService).Error; err == nil {
		return response, fmt.Errorf("服务名称 '%s' 已存在", req.ServerName)
	}

	// 创建MCPToolExecutor配置
	config := &lambda.ToolExecutorConfig{
		ServerURL:       req.Endpoint,
		Timeout:         req.Timeout,
		RetryCount:      req.RetryCount,
		ValidateParams:  true,
		EnableProgress:  true,
		CacheToolList:   true,
		CacheExpiration: 300,
	}

	// 创建执行器并测试连接
	executor := lambda.NewMCPToolExecutor(config)
	if err := executor.Initialize(ctx); err != nil {
		return response, fmt.Errorf("无法连接到MCP服务: %w", err)
	}

	// 发现工具列表
	tools, err := executor.ListTools(ctx, true)
	if err != nil {
		return response, fmt.Errorf("获取工具列表失败: %w", err)
	}

	// 解析认证配置
	authType := "none"
	authToken := ""
	if req.AuthConfig != nil {
		if at, ok := req.AuthConfig["type"].(string); ok {
			authType = at
		}
		if token, ok := req.AuthConfig["token"].(string); ok {
			authToken = token
		}
	}

	// 设置默认值
	if req.Timeout == 0 {
		req.Timeout = 30
	}
	if req.RetryCount == 0 {
		req.RetryCount = 3
	}
	if req.HealthCheckInterval == 0 {
		req.HealthCheckInterval = 60
	}

	// 开始事务
	tx := TenantDB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 创建服务记录
	now := time.Now()
	// 创建MCP服务实体对象，用于数据库存储
	service := mcp.MCPService{
		// 服务唯一标识名称
		ServerName: req.ServerName,
		// 服务显示名称，用于UI展示
		DisplayName: req.DisplayName,
		// 服务端点URL
		Endpoint: req.Endpoint,
		// 服务描述信息
		Description: req.Description,
		// 认证类型(none/token/basic等)
		AuthType: authType,
		// 认证令牌/凭证
		AuthToken: authToken,
		// 健康检查间隔(秒)
		HealthCheckInterval: req.HealthCheckInterval,
		// 请求超时时间(秒)
		Timeout: req.Timeout,
		// 失败重试次数
		RetryCount: req.RetryCount,
		// 负载均衡权重(默认1)
		Weight: 1,
		// 服务状态(1-启用, 0-禁用)
		Status: 1,
		// 最后健康检查时间
		LastHealthCheck: &now,
		// 工具最后同步时间
		ToolsSyncedAt: &now,
		// 错误计数(用于健康状态判断)
		ErrorCount: 0,
	}

	if err := tx.Create(&service).Error; err != nil {
		tx.Rollback()
		return response, fmt.Errorf("保存服务信息失败: %w", err)
	}

	// 保存工具信息
	var discoveredTools []mcpRes.DiscoveredToolInfo
	var generatedAPIs []string

	for toolName, toolInfo := range tools {
		apiEndpoint := fmt.Sprintf("/mcp/%s/%s", req.ServerName, toolName)

		// 序列化Schema和Examples
		var schemaJSON []byte
		var examplesJSON []byte

		if toolInfo.InputSchema != nil {
			schemaJSON, _ = json.Marshal(toolInfo.InputSchema)
		}

		// 创建示例数据 - 转换Parameters为interface{}
		parametersInterface := make(map[string]interface{})
		for k, v := range toolInfo.Parameters {
			parametersInterface[k] = v
		}
		examples := []map[string]interface{}{
			parametersInterface,
		}
		examplesJSON, _ = json.Marshal(examples)

		tool := mcp.MCPTool{
			ServerID:    service.ID,
			ServerName:  req.ServerName,
			ToolName:    toolName,
			DisplayName: toolInfo.Name,
			Description: toolInfo.Description,
			Category:    "general", // 默认分类
			Version:     "1.0.0",   // 默认版本
			SchemaJSON:  schemaJSON,
			APIEndpoint: apiEndpoint,
			HTTPMethod:  "POST",
			Examples:    examplesJSON,
			Status:      1, // 启用状态
			SyncedAt:    now,
		}

		if err := tx.Create(&tool).Error; err != nil {
			tx.Rollback()
			return response, fmt.Errorf("保存工具信息失败: %w", err)
		}

		discoveredTools = append(discoveredTools, mcpRes.DiscoveredToolInfo{
			Name:        toolName,
			APIEndpoint: apiEndpoint,
		})
		generatedAPIs = append(generatedAPIs, apiEndpoint)
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		return response, fmt.Errorf("提交事务失败: %w", err)
	}

	// 新增：注册工具API到sys_apis表
	global.GVA_LOG.Info("开始注册MCP API到sys_apis表",
		zap.String("serverName", service.ServerName),
		zap.String("displayName", service.DisplayName),
		zap.Int("toolsCount", len(tools)))

	if err := s.RegisterMCPApisToSysTable(TenantDB, service.ID, tools, service); err != nil {
		// 注意：这里是否回滚取决于业务需求
		global.GVA_LOG.Error("注册MCP API到sys_apis失败", zap.Error(err))
	} else {
		global.GVA_LOG.Info("成功注册MCP API到sys_apis表",
			zap.String("serverName", service.ServerName))
	}

	// 构建响应
	response = mcpRes.RegisterServiceResponse{
		ServiceID:       service.ID,
		ServerName:      req.ServerName,
		Status:          "active",
		DiscoveredTools: discoveredTools,
		ToolsCount:      len(tools),
		GeneratedAPIs:   generatedAPIs,
	}

	return response, nil
}

// GetServiceList 获取服务列表
func (s *MCPServiceService) GetServiceList(TenantDB *gorm.DB, ctx context.Context, req mcpReq.MCPServiceSearch) (mcpRes.ServiceListResponse, error) {
	var response mcpRes.ServiceListResponse
	var services []mcp.MCPService
	var total int64

	db := TenantDB.Model(&mcp.MCPService{}).Where("is_deleted = 0")

	// 添加搜索条件
	if req.ServerName != "" {
		db = db.Where("server_name LIKE ?", "%"+req.ServerName+"%")
	}
	if req.DisplayName != "" {
		db = db.Where("display_name LIKE ?", "%"+req.DisplayName+"%")
	}
	if req.Status != nil {
		db = db.Where("status = ?", *req.Status)
	}
	if req.AuthType != "" {
		db = db.Where("auth_type = ?", req.AuthType)
	}

	// 获取总数
	if err := db.Count(&total).Error; err != nil {
		return response, err
	}

	// 分页查询
	offset := (req.Page - 1) * req.PageSize
	if err := db.Offset(offset).Limit(req.PageSize).Order("created_at DESC").Find(&services).Error; err != nil {
		return response, err
	}

	// 构建响应数据
	var serviceInfos []mcpRes.ServiceInfo
	for _, service := range services {
		// 获取工具数量
		var toolsCount int64
		TenantDB.Model(&mcp.MCPTool{}).Where("server_id = ? AND is_deleted = 0", service.ID).Count(&toolsCount)

		// 获取生成的API列表
		var tools []mcp.MCPTool
		TenantDB.Where("server_id = ? AND is_deleted = 0", service.ID).Find(&tools)

		var generatedAPIs []string
		for _, tool := range tools {
			generatedAPIs = append(generatedAPIs, tool.APIEndpoint)
		}

		serviceInfos = append(serviceInfos, mcpRes.ServiceInfo{
			MCPService:    service,
			ToolsCount:    int(toolsCount),
			GeneratedAPIs: generatedAPIs,
		})
	}

	response.Services = serviceInfos
	response.Total = total

	return response, nil
}

// UpdateService 更新服务
func (s *MCPServiceService) UpdateService(TenantDB *gorm.DB, ctx context.Context, req mcpReq.UpdateServiceRequest) error {
	var service mcp.MCPService
	if err := TenantDB.Where("id = ? AND is_deleted = 0", req.ID).First(&service).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("服务不存在")
		}
		return err
	}

	// 解析认证配置
	authType := service.AuthType
	authToken := service.AuthToken
	if req.AuthConfig != nil {
		if at, ok := req.AuthConfig["type"].(string); ok {
			authType = at
		}
		if token, ok := req.AuthConfig["token"].(string); ok {
			authToken = token
		}
	}

	// 更新服务信息
	updates := map[string]interface{}{
		"display_name":          req.DisplayName,
		"endpoint":              req.Endpoint,
		"description":           req.Description,
		"auth_type":             authType,
		"auth_token":            authToken,
		"timeout":               req.Timeout,
		"retry_count":           req.RetryCount,
		"health_check_interval": req.HealthCheckInterval,
		"status":                req.Status,
	}

	if err := TenantDB.Model(&service).Updates(updates).Error; err != nil {
		return fmt.Errorf("更新服务失败: %w", err)
	}

	// 如果端点发生变化，重新同步工具
	if service.Endpoint != req.Endpoint {
		if err := s.syncToolsFromMCP(TenantDB, ctx, service.ID); err != nil {
			// 记录错误但不阻止更新
			global.GVA_LOG.Error(fmt.Sprintf("同步工具失败: %v", err))
		}
	}

	return nil
}

// DeleteService 删除服务
func (s *MCPServiceService) DeleteService(TenantDB *gorm.DB, ctx context.Context, id uint) error {
	// 先获取服务信息，用于清理API
	var service mcp.MCPService
	if err := TenantDB.Where("id = ? AND is_deleted = 0", id).First(&service).Error; err != nil {
		return fmt.Errorf("服务不存在: %w", err)
	}

	tx := TenantDB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	// 软删除服务
	if err := tx.Model(&mcp.MCPService{}).Where("id = ?", id).Update("is_deleted", 1).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("删除服务失败: %w", err)
	}

	// 软删除相关工具
	if err := tx.Model(&mcp.MCPTool{}).Where("server_id = ?", id).Update("is_deleted", 1).Error; err != nil {
		tx.Rollback()
		return fmt.Errorf("删除相关工具失败: %w", err)
	}

	if err := tx.Commit().Error; err != nil {
		return err
	}

	// 清理sys_apis中的API记录
	if err := s.cleanMCPApisFromSysTable(TenantDB, ctx, service); err != nil {
		global.GVA_LOG.Error("清理MCP API记录失败", zap.Error(err))
	}

	return nil
}

// syncToolsFromMCP 从MCP服务同步工具信息
func (s *MCPServiceService) syncToolsFromMCP(TenantDB *gorm.DB, ctx context.Context, serviceID uint) error {
	var service mcp.MCPService
	if err := TenantDB.First(&service, serviceID).Error; err != nil {
		return err
	}

	// 创建执行器配置
	config := &lambda.ToolExecutorConfig{
		ServerURL:       service.Endpoint,
		Timeout:         service.Timeout,
		RetryCount:      service.RetryCount,
		ValidateParams:  true,
		EnableProgress:  true,
		CacheToolList:   true,
		CacheExpiration: 300,
	}

	executor := lambda.NewMCPToolExecutor(config)
	if err := executor.Initialize(ctx); err != nil {
		return fmt.Errorf("初始化执行器失败: %w", err)
	}

	// 强制刷新工具列表
	tools, err := executor.ListTools(ctx, true)
	if err != nil {
		return fmt.Errorf("获取工具列表失败: %w", err)
	}

	// 更新工具信息
	now := time.Now()
	tx := TenantDB.Begin()

	// 先标记所有现有工具为已删除
	if err := tx.Model(&mcp.MCPTool{}).Where("server_id = ?", serviceID).Update("is_deleted", 1).Error; err != nil {
		tx.Rollback()
		return err
	}

	// 重新创建工具记录
	for toolName, toolInfo := range tools {
		apiEndpoint := fmt.Sprintf("/mcp/%s/%s", service.ServerName, toolName)

		var schemaJSON []byte
		var examplesJSON []byte

		if toolInfo.InputSchema != nil {
			schemaJSON, _ = json.Marshal(toolInfo.InputSchema)
		}

		// 转换Parameters为interface{}
		parametersInterface := make(map[string]interface{})
		for k, v := range toolInfo.Parameters {
			parametersInterface[k] = v
		}
		examples := []map[string]interface{}{
			parametersInterface,
		}
		examplesJSON, _ = json.Marshal(examples)

		tool := mcp.MCPTool{
			ServerID:    serviceID,
			ServerName:  service.ServerName,
			ToolName:    toolName,
			DisplayName: toolInfo.Name,
			Description: toolInfo.Description,
			Category:    "general",
			Version:     "1.0.0",
			SchemaJSON:  schemaJSON,
			APIEndpoint: apiEndpoint,
			HTTPMethod:  "POST",
			Examples:    examplesJSON,
			Status:      1,
			SyncedAt:    now,
			IsDeleted:   0,
		}

		if err := tx.Create(&tool).Error; err != nil {
			tx.Rollback()
			return err
		}
	}

	// 更新服务的同步时间
	if err := tx.Model(&service).Update("tools_synced_at", now).Error; err != nil {
		tx.Rollback()
		return err
	}

	return tx.Commit().Error
}

// ToggleServiceStatus 切换服务状态
func (s *MCPServiceService) ToggleServiceStatus(TenantDB *gorm.DB, ctx context.Context, req mcpReq.ToggleServiceStatusRequest) error {
	var service mcp.MCPService
	if err := TenantDB.Where("id = ? AND is_deleted = 0", req.ID).First(&service).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return fmt.Errorf("服务不存在")
		}
		return err
	}

	// 更新状态
	if err := TenantDB.Model(&service).Update("status", req.Status).Error; err != nil {
		return fmt.Errorf("更新服务状态失败: %w", err)
	}

	// 如果禁用服务，同时禁用相关工具
	if req.Status == 0 {
		if err := TenantDB.Model(&mcp.MCPTool{}).Where("server_id = ?", req.ID).Update("status", 0).Error; err != nil {
			// 记录错误但不阻止状态更新
			global.GVA_LOG.Error(fmt.Sprintf("禁用相关工具失败: %v", err))
		}
	}

	return nil
}

// BatchUpdateServices 批量更新服务
func (s *MCPServiceService) BatchUpdateServices(TenantDB *gorm.DB, ctx context.Context, req mcpReq.BatchUpdateServicesRequest) (mcpRes.BatchOperationResponse, error) {
	var result mcpRes.BatchOperationResponse
	result.TotalCount = len(req.IDs)

	for _, id := range req.IDs {
		// 检查服务是否存在
		var service mcp.MCPService
		if err := TenantDB.Where("id = ? AND is_deleted = 0", id).First(&service).Error; err != nil {
			result.FailedCount++
			result.FailedIDs = append(result.FailedIDs, id)
			result.Errors = append(result.Errors, fmt.Sprintf("服务ID %d 不存在", id))
			continue
		}

		// 构建更新数据
		updates := make(map[string]interface{})
		if req.Updates.Status != nil {
			updates["status"] = *req.Updates.Status
		}
		if req.Updates.HealthCheckInterval != nil {
			updates["health_check_interval"] = *req.Updates.HealthCheckInterval
		}
		if req.Updates.Timeout != nil {
			updates["timeout"] = *req.Updates.Timeout
		}
		if req.Updates.RetryCount != nil {
			updates["retry_count"] = *req.Updates.RetryCount
		}
		if req.Updates.Description != "" {
			updates["description"] = req.Updates.Description
		}

		// 执行更新
		if err := TenantDB.Model(&service).Updates(updates).Error; err != nil {
			result.FailedCount++
			result.FailedIDs = append(result.FailedIDs, id)
			result.Errors = append(result.Errors, fmt.Sprintf("更新服务ID %d 失败: %v", id, err))
			continue
		}

		// 如果禁用服务，同时禁用相关工具
		if req.Updates.Status != nil && *req.Updates.Status == 0 {
			if err := TenantDB.Model(&mcp.MCPTool{}).Where("server_id = ?", id).Update("status", 0).Error; err != nil {
				global.GVA_LOG.Error(fmt.Sprintf("禁用服务ID %d 的相关工具失败: %v", id, err))
			}
		}

		result.SuccessCount++
		result.SuccessIDs = append(result.SuccessIDs, id)
	}

	return result, nil
}

// BatchDeleteServices 批量删除服务
func (s *MCPServiceService) BatchDeleteServices(TenantDB *gorm.DB, ctx context.Context, req mcpReq.BatchDeleteServicesRequest) (mcpRes.BatchOperationResponse, error) {
	var result mcpRes.BatchOperationResponse
	result.TotalCount = len(req.IDs)

	for _, id := range req.IDs {
		// 检查服务是否存在
		var service mcp.MCPService
		if err := TenantDB.Where("id = ? AND is_deleted = 0", id).First(&service).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				result.FailedCount++
				result.FailedIDs = append(result.FailedIDs, id)
				result.Errors = append(result.Errors, fmt.Sprintf("服务ID %d 不存在", id))
				continue
			}
			result.FailedCount++
			result.FailedIDs = append(result.FailedIDs, id)
			result.Errors = append(result.Errors, fmt.Sprintf("查询服务ID %d 失败: %v", id, err))
			continue
		}

		// 开始事务删除
		tx := TenantDB.Begin()

		// 软删除服务
		if err := tx.Model(&mcp.MCPService{}).Where("id = ?", id).Update("is_deleted", 1).Error; err != nil {
			tx.Rollback()
			result.FailedCount++
			result.FailedIDs = append(result.FailedIDs, id)
			result.Errors = append(result.Errors, fmt.Sprintf("删除服务ID %d 失败: %v", id, err))
			continue
		}

		// 软删除相关工具
		if err := tx.Model(&mcp.MCPTool{}).Where("server_id = ?", id).Update("is_deleted", 1).Error; err != nil {
			tx.Rollback()
			result.FailedCount++
			result.FailedIDs = append(result.FailedIDs, id)
			result.Errors = append(result.Errors, fmt.Sprintf("删除服务ID %d 的相关工具失败: %v", id, err))
			continue
		}

		if err := tx.Commit().Error; err != nil {
			result.FailedCount++
			result.FailedIDs = append(result.FailedIDs, id)
			result.Errors = append(result.Errors, fmt.Sprintf("提交删除服务ID %d 的事务失败: %v", id, err))
			continue
		}

		result.SuccessCount++
		result.SuccessIDs = append(result.SuccessIDs, id)
	}

	return result, nil
}

// SyncToolsFromMCPEnhanced 增强版工具同步方法
func (s *MCPServiceService) SyncToolsFromMCPEnhanced(TenantDB *gorm.DB, ctx context.Context, req mcpReq.SyncToolsRequest) (mcpRes.SyncToolsResponse, error) {
	startTime := time.Now()
	var response mcpRes.SyncToolsResponse

	// 获取服务信息
	var service mcp.MCPService
	if err := TenantDB.Where("id = ? AND is_deleted = 0", req.ServiceID).First(&service).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			errorMsg := "服务不存在"
			response.ErrorMessage = &errorMsg
			return response, fmt.Errorf("服务不存在")
		}
		errorMsg := fmt.Sprintf("查询服务失败: %v", err)
		response.ErrorMessage = &errorMsg
		return response, err
	}

	// 检查服务状态
	if service.Status != 1 {
		errorMsg := fmt.Sprintf("服务 '%s' 当前不可用", service.ServerName)
		response.ErrorMessage = &errorMsg
		return response, fmt.Errorf(errorMsg)
	}

	response.ServiceID = service.ID
	response.ServerName = service.ServerName
	response.SyncedAt = time.Now()

	// 创建执行器配置
	config := &lambda.ToolExecutorConfig{
		ServerURL:       service.Endpoint,
		Timeout:         service.Timeout,
		RetryCount:      service.RetryCount,
		ValidateParams:  true,
		EnableProgress:  true,
		CacheToolList:   true,
		CacheExpiration: 300,
	}

	executor := lambda.NewMCPToolExecutor(config)
	if err := executor.Initialize(ctx); err != nil {
		errorMsg := fmt.Sprintf("初始化执行器失败: %v", err)
		response.ErrorMessage = &errorMsg
		return response, fmt.Errorf("初始化执行器失败: %w", err)
	}

	// 获取远程工具列表
	remoteTools, err := executor.ListTools(ctx, req.Force)
	if err != nil {
		errorMsg := fmt.Sprintf("获取工具列表失败: %v", err)
		response.ErrorMessage = &errorMsg
		return response, fmt.Errorf("获取工具列表失败: %w", err)
	}

	// 获取本地工具列表
	var localTools []mcp.MCPTool
	if err := TenantDB.Where("server_id = ? AND is_deleted = 0", service.ID).Find(&localTools).Error; err != nil {
		errorMsg := fmt.Sprintf("查询本地工具失败: %v", err)
		response.ErrorMessage = &errorMsg
		return response, err
	}

	// 创建本地工具映射
	localToolMap := make(map[string]mcp.MCPTool)
	for _, tool := range localTools {
		localToolMap[tool.ToolName] = tool
	}

	// 开始事务
	tx := TenantDB.Begin()
	defer func() {
		if r := recover(); r != nil {
			tx.Rollback()
		}
	}()

	now := time.Now()
	var toolsAdded []mcpRes.DiscoveredToolInfo
	var toolsUpdated []mcpRes.DiscoveredToolInfo
	var toolsRemoved []string

	// 处理远程工具（新增和更新）
	for toolName, toolInfo := range remoteTools {
		apiEndpoint := fmt.Sprintf("/mcp/%s/%s", service.ServerName, toolName)

		// 序列化Schema和Examples
		var schemaJSON []byte
		var examplesJSON []byte

		if toolInfo.InputSchema != nil {
			schemaJSON, _ = json.Marshal(toolInfo.InputSchema)
		}

		// 创建示例数据
		parametersInterface := make(map[string]interface{})
		for k, v := range toolInfo.Parameters {
			parametersInterface[k] = v
		}
		examples := []map[string]interface{}{
			parametersInterface,
		}
		examplesJSON, _ = json.Marshal(examples)

		if existingTool, exists := localToolMap[toolName]; exists {
			// 更新现有工具
			updates := map[string]interface{}{
				"display_name": toolInfo.Name,
				"description":  toolInfo.Description,
				"schema_json":  schemaJSON,
				"examples":     examplesJSON,
				"synced_at":    now,
				"is_deleted":   0, // 确保未删除
			}

			if err := tx.Model(&existingTool).Updates(updates).Error; err != nil {
				tx.Rollback()
				errorMsg := fmt.Sprintf("更新工具 %s 失败: %v", toolName, err)
				response.ErrorMessage = &errorMsg
				return response, err
			}

			toolsUpdated = append(toolsUpdated, mcpRes.DiscoveredToolInfo{
				Name:        toolName,
				APIEndpoint: apiEndpoint,
			})
		} else {
			// 新增工具
			tool := mcp.MCPTool{
				ServerID:    service.ID,
				ServerName:  service.ServerName,
				ToolName:    toolName,
				DisplayName: toolInfo.Name,
				Description: toolInfo.Description,
				Category:    "general",
				Version:     "1.0.0",
				SchemaJSON:  schemaJSON,
				APIEndpoint: apiEndpoint,
				HTTPMethod:  "POST",
				Examples:    examplesJSON,
				Status:      1,
				SyncedAt:    now,
				IsDeleted:   0,
			}

			if err := tx.Create(&tool).Error; err != nil {
				tx.Rollback()
				errorMsg := fmt.Sprintf("创建工具 %s 失败: %v", toolName, err)
				response.ErrorMessage = &errorMsg
				return response, err
			}

			toolsAdded = append(toolsAdded, mcpRes.DiscoveredToolInfo{
				Name:        toolName,
				APIEndpoint: apiEndpoint,
			})
		}

		// 从本地映射中删除，剩下的就是需要删除的
		delete(localToolMap, toolName)
	}

	// 处理需要删除的工具
	if req.DeleteOld {
		for toolName, tool := range localToolMap {
			if err := tx.Model(&tool).Update("is_deleted", 1).Error; err != nil {
				tx.Rollback()
				errorMsg := fmt.Sprintf("删除工具 %s 失败: %v", toolName, err)
				response.ErrorMessage = &errorMsg
				return response, err
			}
			toolsRemoved = append(toolsRemoved, toolName)
		}
	}

	// 更新服务的同步时间
	if err := tx.Model(&service).Update("tools_synced_at", now).Error; err != nil {
		tx.Rollback()
		errorMsg := fmt.Sprintf("更新服务同步时间失败: %v", err)
		response.ErrorMessage = &errorMsg
		return response, err
	}

	// 提交事务
	if err := tx.Commit().Error; err != nil {
		errorMsg := fmt.Sprintf("提交事务失败: %v", err)
		response.ErrorMessage = &errorMsg
		return response, err
	}

	// 构建响应
	response.ToolsAdded = toolsAdded
	response.ToolsUpdated = toolsUpdated
	response.ToolsRemoved = toolsRemoved
	response.TotalTools = len(remoteTools)
	response.ExecutionTime = time.Since(startTime).Seconds()
	response.Success = true

	return response, nil
}

// BatchSyncToolsFromMCP 批量同步工具
func (s *MCPServiceService) BatchSyncToolsFromMCP(TenantDB *gorm.DB, ctx context.Context, req mcpReq.BatchSyncToolsRequest) (mcpRes.BatchSyncToolsResponse, error) {
	startTime := time.Now()
	var response mcpRes.BatchSyncToolsResponse

	response.TotalServices = len(req.ServiceIDs)

	var results []mcpRes.SyncToolsResponse
	var summary mcpRes.SyncSummary

	for _, serviceID := range req.ServiceIDs {
		syncReq := mcpReq.SyncToolsRequest{
			ServiceID: serviceID,
			Force:     req.Force,
			DeleteOld: req.DeleteOld,
		}

		result, err := s.SyncToolsFromMCPEnhanced(TenantDB, ctx, syncReq)
		results = append(results, result)

		if err != nil {
			response.FailedServices++
			global.GVA_LOG.Error(fmt.Sprintf("同步服务 %d 失败: %v", serviceID, err))
		} else {
			response.SuccessServices++
			summary.TotalToolsAdded += len(result.ToolsAdded)
			summary.TotalToolsUpdated += len(result.ToolsUpdated)
			summary.TotalToolsRemoved += len(result.ToolsRemoved)
		}
	}

	// 计算最终工具总数
	var totalTools int64
	TenantDB.Model(&mcp.MCPTool{}).Where("is_deleted = 0").Count(&totalTools)
	summary.FinalToolsCount = int(totalTools)

	response.Results = results
	response.Summary = summary
	response.ExecutionTime = time.Since(startTime).Seconds()

	return response, nil
}

// SyncAllToolsFromMCP 全量同步工具
func (s *MCPServiceService) SyncAllToolsFromMCP(TenantDB *gorm.DB, ctx context.Context, req mcpReq.SyncAllToolsRequest) (mcpRes.SyncAllToolsResponse, error) {
	startTime := time.Now()
	var response mcpRes.SyncAllToolsResponse

	// 获取需要同步的服务列表
	var services []mcp.MCPService
	db := TenantDB.Where("is_deleted = 0")
	if req.OnlyActive {
		db = db.Where("status = 1")
	}

	if err := db.Find(&services).Error; err != nil {
		return response, fmt.Errorf("查询服务列表失败: %w", err)
	}

	response.TotalServices = len(services)

	var results []mcpRes.SyncToolsResponse
	var summary mcpRes.SyncSummary

	for _, service := range services {
		syncReq := mcpReq.SyncToolsRequest{
			ServiceID: service.ID,
			Force:     req.Force,
			DeleteOld: req.DeleteOld,
		}

		result, err := s.SyncToolsFromMCPEnhanced(TenantDB, ctx, syncReq)
		results = append(results, result)

		if err != nil {
			response.FailedServices++
			global.GVA_LOG.Error(fmt.Sprintf("同步服务 %s 失败: %v", service.ServerName, err))
		} else {
			response.SuccessServices++
			summary.TotalToolsAdded += len(result.ToolsAdded)
			summary.TotalToolsUpdated += len(result.ToolsUpdated)
			summary.TotalToolsRemoved += len(result.ToolsRemoved)
		}
	}

	// 计算最终工具总数
	var totalTools int64
	TenantDB.Model(&mcp.MCPTool{}).Where("is_deleted = 0").Count(&totalTools)
	summary.FinalToolsCount = int(totalTools)

	response.Results = results
	response.Summary = summary
	response.ExecutionTime = time.Since(startTime).Seconds()

	return response, nil
}

// RegisterMCPApisToSysTable 注册MCP API到sys_apis表
func (s *MCPServiceService) RegisterMCPApisToSysTable(TenantDB *gorm.DB, serviceID uint, tools map[string]lambda.ToolInfo, service mcp.MCPService) error {
	apiGroup := s.generateAPIGroupName(service.DisplayName)
	global.GVA_LOG.Info("生成API分组名称", zap.String("apiGroup", apiGroup))

	var sysApis []system.SysApi

	// 1. 注册固定的服务管理API
	managementApis := []system.SysApi{
		{
			Path:        "/mcp/services/register",
			Description: fmt.Sprintf("[MCP] 注册MCP服务"),
			ApiGroup:    "MCP-服务管理",
			Method:      "POST",
			SourceType:  "mcp",
			SourceID:    &serviceID,
			ApiCategory: "服务管理",
		},
		{
			Path:        "/mcp/services",
			Description: fmt.Sprintf("[MCP] 获取MCP服务列表"),
			ApiGroup:    "MCP-服务管理",
			Method:      "GET",
			SourceType:  "mcp",
			SourceID:    &serviceID,
			ApiCategory: "服务管理",
		},
		{
			Path:        "/mcp/services",
			Description: fmt.Sprintf("[MCP] 更新MCP服务"),
			ApiGroup:    "MCP-服务管理",
			Method:      "PUT",
			SourceType:  "mcp",
			SourceID:    &serviceID,
			ApiCategory: "服务管理",
		},
		{
			Path:        "/mcp/services/:id",
			Description: fmt.Sprintf("[MCP] 删除MCP服务"),
			ApiGroup:    "MCP-服务管理",
			Method:      "DELETE",
			SourceType:  "mcp",
			SourceID:    &serviceID,
			ApiCategory: "服务管理",
		},
		{
			Path:        "/mcp/tools",
			Description: fmt.Sprintf("[MCP] 获取MCP工具列表"),
			ApiGroup:    "MCP-工具管理",
			Method:      "GET",
			SourceType:  "mcp",
			SourceID:    &serviceID,
			ApiCategory: "工具管理",
		},
		{
			Path:        "/mcp/tools/sync",
			Description: fmt.Sprintf("[MCP] 同步MCP工具"),
			ApiGroup:    "MCP-工具管理",
			Method:      "GET",
			SourceType:  "mcp",
			SourceID:    &serviceID,
			ApiCategory: "工具管理",
		},
		{
			Path:        "/mcp/tools/:server",
			Description: fmt.Sprintf("[MCP] 获取指定服务的工具"),
			ApiGroup:    "MCP-工具管理",
			Method:      "GET",
			SourceType:  "mcp",
			SourceID:    &serviceID,
			ApiCategory: "工具管理",
		},
		{
			Path:        "/mcp/health",
			Description: fmt.Sprintf("[MCP] 健康检查"),
			ApiGroup:    "MCP-健康检查",
			Method:      "GET",
			SourceType:  "mcp",
			SourceID:    &serviceID,
			ApiCategory: "健康检查",
		},
		{
			Path:        "/mcp/health/:server",
			Description: fmt.Sprintf("[MCP] 指定服务健康检查"),
			ApiGroup:    "MCP-健康检查",
			Method:      "GET",
			SourceType:  "mcp",
			SourceID:    &serviceID,
			ApiCategory: "健康检查",
		},
		{
			Path:        "/mcp/status",
			Description: fmt.Sprintf("[MCP] 获取系统状态"),
			ApiGroup:    "MCP-健康检查",
			Method:      "GET",
			SourceType:  "mcp",
			SourceID:    &serviceID,
			ApiCategory: "健康检查",
		},
	}

	// 2. 注册动态工具API
	for toolName, toolInfo := range tools {
		apiEndpoint := fmt.Sprintf("/mcp/%s/%s", service.ServerName, toolName)
		sysApi := system.SysApi{
			Path:        apiEndpoint,
			Description: fmt.Sprintf("[MCP] %s - %s", service.DisplayName, toolInfo.Description),
			ApiGroup:    apiGroup,
			Method:      "POST",
			SourceType:  "mcp",
			SourceID:    &serviceID,
			ApiCategory: "工具调用",
		}
		sysApis = append(sysApis, sysApi)

		// Schema查询API
		schemaApi := system.SysApi{
			Path:        fmt.Sprintf("/mcp/%s/%s/schema", service.ServerName, toolName),
			Description: fmt.Sprintf("[MCP] %s - %s Schema", service.DisplayName, toolInfo.Description),
			ApiGroup:    apiGroup,
			Method:      "GET",
			SourceType:  "mcp",
			SourceID:    &serviceID,
			ApiCategory: "工具查询",
		}
		sysApis = append(sysApis, schemaApi)
	}

	// 合并所有API
	allApis := append(managementApis, sysApis...)

	// 批量创建，忽略重复的API
	createdCount := 0
	for _, api := range allApis {
		var existingApi system.SysApi
		if err := TenantDB.Where("path = ? AND method = ?", api.Path, api.Method).First(&existingApi).Error; err != nil {
			if err == gorm.ErrRecordNotFound {
				// API不存在，创建新的
				if err := TenantDB.Create(&api).Error; err != nil {
					global.GVA_LOG.Error("创建API记录失败", zap.Error(err), zap.String("path", api.Path))
				} else {
					createdCount++
					global.GVA_LOG.Info("成功创建API记录",
						zap.String("path", api.Path),
						zap.String("method", api.Method),
						zap.String("apiGroup", api.ApiGroup))
				}
			}
		}
	}

	global.GVA_LOG.Info("API注册完成",
		zap.Int("totalApis", len(allApis)),
		zap.Int("createdApis", createdCount))

	return nil
}

// generateAPIGroupName 生成API分组名称
func (s *MCPServiceService) generateAPIGroupName(displayName string) string {
	return fmt.Sprintf("MCP-%s", displayName)
}

// cleanMCPApisFromSysTable 清理MCP API (在删除服务时调用)
func (s *MCPServiceService) cleanMCPApisFromSysTable(TenantDB *gorm.DB, ctx context.Context, service mcp.MCPService) error {
	apiGroup := s.generateAPIGroupName(service.DisplayName)

	// 删除该服务相关的API记录
	if err := TenantDB.Where("api_group = ?", apiGroup).Delete(&system.SysApi{}).Error; err != nil {
		return fmt.Errorf("清理API记录失败: %w", err)
	}

	// 同时清理该服务的工具API
	// 构建该服务的工具API路径前缀
	pathPrefix := fmt.Sprintf("/mcp/%s/", service.ServerName)
	if err := TenantDB.Where("path LIKE ?", pathPrefix+"%").Delete(&system.SysApi{}).Error; err != nil {
		return fmt.Errorf("清理工具API记录失败: %w", err)
	}

	return nil
}
