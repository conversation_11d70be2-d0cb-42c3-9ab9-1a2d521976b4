package mcp

import (
	"context"
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"
	mcpRes "github.com/flipped-aurora/gin-vue-admin/server/model/mcp/response"
	"github.com/flipped-aurora/gin-vue-admin/server/pkg/einoext/components/lambda"
	"gorm.io/gorm"
)

type MCPHealthService struct{}

// CheckServiceHealth 检查单个服务健康状态
func (s *MCPHealthService) CheckServiceHealth(TenantDB *gorm.DB, ctx context.Context, serverName string) (mcpRes.HealthCheckResponse, error) {
	var response mcpRes.HealthCheckResponse
	var service mcp.MCPService

	// 获取服务信息
	if err := TenantDB.Where("server_name = ? AND is_deleted = 0", serverName).First(&service).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return response, fmt.Errorf("服务 '%s' 不存在", serverName)
		}
		return response, err
	}

	// 执行健康检查
	startTime := time.Now()
	isHealthy, toolsCount, errorMsg := s.performHealthCheck(ctx, &service)
	responseTime := time.Since(startTime).Seconds()

	// 记录健康检查结果
	healthRecord := mcp.MCPServiceHealth{
		ServerID:     service.ID,
		ServerName:   serverName,
		CheckTime:    time.Now(),
		Status:       0,
		ResponseTime: responseTime,
		ErrorMessage: errorMsg,
		ToolsCount:   toolsCount,
	}

	if isHealthy {
		healthRecord.Status = 1
	}

	// 保存健康检查记录
	TenantDB.Create(&healthRecord)

	// 更新服务状态
	updates := map[string]interface{}{
		"last_health_check": time.Now(),
	}

	if isHealthy {
		updates["status"] = 1
		updates["error_count"] = 0
		updates["last_error_msg"] = ""
	} else {
		updates["status"] = 2
		updates["error_count"] = gorm.Expr("error_count + 1")
		updates["last_error_msg"] = errorMsg
	}

	TenantDB.Model(&service).Updates(updates)

	// 获取健康检查历史
	var healthHistory []mcp.MCPServiceHealth
	TenantDB.Where("server_id = ?", service.ID).
		Order("check_time DESC").
		Limit(10).
		Find(&healthHistory)

	var historyItems []mcpRes.HealthHistoryItem
	for _, record := range healthHistory {
		historyItems = append(historyItems, mcpRes.HealthHistoryItem{
			CheckTime:    record.CheckTime,
			Status:       s.getHealthStatusString(record.Status),
			ResponseTime: record.ResponseTime,
		})
	}

	// 构建响应
	var errorMsgPtr *string
	if errorMsg != "" {
		errorMsgPtr = &errorMsg
	}

	response = mcpRes.HealthCheckResponse{
		ServerName:   serverName,
		Status:       s.getHealthStatusString(healthRecord.Status),
		ResponseTime: responseTime,
		ToolsCount:   toolsCount,
		LastCheck:    healthRecord.CheckTime,
		ErrorMessage: errorMsgPtr,
	}

	return response, nil
}

// CheckAllServicesHealth 检查所有服务健康状态
func (s *MCPHealthService) CheckAllServicesHealth(TenantDB *gorm.DB, ctx context.Context) error {
	var services []mcp.MCPService
	if err := TenantDB.Where("is_deleted = 0").Find(&services).Error; err != nil {
		return err
	}

	for _, service := range services {
		// 检查是否需要进行健康检查
		if s.shouldPerformHealthCheck(&service) {
			go func(srv mcp.MCPService) {
				_, err := s.CheckServiceHealth(TenantDB, ctx, srv.ServerName)
				if err != nil {
					global.GVA_LOG.Error(fmt.Sprintf("健康检查失败 - 服务: %s, 错误: %v", srv.ServerName, err))
				}
			}(service)
		}
	}

	return nil
}

// performHealthCheck 执行实际的健康检查
func (s *MCPHealthService) performHealthCheck(ctx context.Context, service *mcp.MCPService) (bool, int, string) {
	// 创建执行器配置
	config := &lambda.ToolExecutorConfig{
		ServerURL:       service.Endpoint,
		Timeout:         service.Timeout,
		RetryCount:      1, // 健康检查只重试一次
		ValidateParams:  false,
		EnableProgress:  false,
		CacheToolList:   false,
		CacheExpiration: 0,
	}

	executor := lambda.NewMCPToolExecutor(config)

	// 尝试初始化连接
	if err := executor.Initialize(ctx); err != nil {
		return false, 0, fmt.Sprintf("连接失败: %v", err)
	}

	// 尝试获取工具列表
	tools, err := executor.ListTools(ctx, false)
	if err != nil {
		return false, 0, fmt.Sprintf("获取工具列表失败: %v", err)
	}

	return true, len(tools), ""
}

// shouldPerformHealthCheck 判断是否需要进行健康检查
func (s *MCPHealthService) shouldPerformHealthCheck(service *mcp.MCPService) bool {
	if service.LastHealthCheck == nil {
		return true
	}

	// 检查是否超过健康检查间隔
	interval := time.Duration(service.HealthCheckInterval) * time.Second
	return time.Since(*service.LastHealthCheck) >= interval
}

// getHealthStatusString 获取健康状态字符串
func (s *MCPHealthService) getHealthStatusString(status int) string {
	switch status {
	case 0:
		return "unhealthy"
	case 1:
		return "healthy"
	default:
		return "unknown"
	}
}

// StartHealthCheckScheduler 启动健康检查调度器
func (s *MCPHealthService) StartHealthCheckScheduler(ctx context.Context) {
	ticker := time.NewTicker(30 * time.Second) // 每30秒检查一次
	defer ticker.Stop()

	global.GVA_LOG.Info("MCP健康检查调度器已启动")

	for {
		select {
		case <-ctx.Done():
			global.GVA_LOG.Info("MCP健康检查调度器已停止")
			return
		case <-ticker.C:
			// TODO: 多租户环境下需要重新设计健康检查调度器
			// 现在暂时注释掉，需要为每个租户单独进行健康检查
			// if err := s.CheckAllServicesHealth(ctx); err != nil {
			// 	global.GVA_LOG.Error(fmt.Sprintf("健康检查调度失败: %v", err))
			// }
		}
	}
}
