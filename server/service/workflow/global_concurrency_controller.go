package workflow

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// GlobalConcurrencyController 全局并发控制器
// 使用单例模式，确保整个系统只有一个并发控制器实例
// 控制所有工作流执行的全局并发数，防止底层服务过载
type GlobalConcurrencyController struct {
	semaphore   chan struct{}       // 全局并发控制信号量
	maxWorkers  int                 // 最大并发工作线程数
	activeCount int                 // 当前活跃的工作流数量
	mu          sync.RWMutex        // 读写锁，保护统计数据
	metrics     *ConcurrencyMetrics // 并发控制指标
}

// ConcurrencyMetrics 并发控制指标
type ConcurrencyMetrics struct {
	TotalRequests     int64         // 总请求数
	ActiveWorkers     int           // 当前活跃工作线程数
	QueuedRequests    int           // 排队等待的请求数
	ProcessedRequests int64         // 已处理请求数
	FailedRequests    int64         // 失败请求数
	AverageWaitTime   time.Duration // 平均等待时间
	MaxWaitTime       time.Duration // 最大等待时间
	LastResetTime     time.Time     // 最后重置时间
}

var (
	globalController *GlobalConcurrencyController
	once             sync.Once
)

// GetGlobalConcurrencyController 获取全局并发控制器实例（单例模式）
func GetGlobalConcurrencyController() *GlobalConcurrencyController {
	once.Do(func() {
		// 从配置读取最大并发数，如果配置不存在则使用默认值
		maxWorkers := 2
		if global.GVA_CONFIG.WorkflowConcurrency.MaxGlobalWorkers > 0 {
			maxWorkers = global.GVA_CONFIG.WorkflowConcurrency.MaxGlobalWorkers
		}

		globalController = &GlobalConcurrencyController{
			semaphore:   make(chan struct{}, maxWorkers),
			maxWorkers:  maxWorkers,
			activeCount: 0,
			metrics: &ConcurrencyMetrics{
				LastResetTime: time.Now(),
			},
		}

		log.Printf("🔧 全局并发控制器初始化完成: MaxWorkers=%d", maxWorkers)
	})
	return globalController
}

// AcquireWorker 获取工作线程资源
// 该方法会阻塞直到有可用的工作线程资源
func (gc *GlobalConcurrencyController) AcquireWorker(ctx context.Context, workflowRunID uint) error {
	startTime := time.Now()

	// 更新指标
	gc.mu.Lock()
	gc.metrics.TotalRequests++
	gc.metrics.QueuedRequests++
	gc.mu.Unlock()

	log.Printf("🎯 请求获取工作线程: WorkflowRunID=%d, 当前排队=%d, 活跃工作线程=%d/%d",
		workflowRunID, gc.getQueuedCount(), gc.getActiveCount(), gc.maxWorkers)

	// 尝试获取信号量
	select {
	case gc.semaphore <- struct{}{}:
		// 成功获取资源
		waitTime := time.Since(startTime)

		gc.mu.Lock()
		gc.activeCount++
		gc.metrics.QueuedRequests--
		gc.metrics.ActiveWorkers = gc.activeCount

		// 更新等待时间统计
		if waitTime > gc.metrics.MaxWaitTime {
			gc.metrics.MaxWaitTime = waitTime
		}
		// 简单的移动平均
		if gc.metrics.AverageWaitTime == 0 {
			gc.metrics.AverageWaitTime = waitTime
		} else {
			gc.metrics.AverageWaitTime = (gc.metrics.AverageWaitTime + waitTime) / 2
		}
		gc.mu.Unlock()

		log.Printf("✅ 成功获取工作线程: WorkflowRunID=%d, 等待时间=%v, 活跃工作线程=%d/%d",
			workflowRunID, waitTime, gc.getActiveCount(), gc.maxWorkers)
		return nil

	case <-ctx.Done():
		// 上下文取消或超时
		gc.mu.Lock()
		gc.metrics.QueuedRequests--
		gc.metrics.FailedRequests++
		gc.mu.Unlock()

		log.Printf("❌ 获取工作线程失败(超时): WorkflowRunID=%d, 原因=%v", workflowRunID, ctx.Err())
		return fmt.Errorf("获取工作线程超时: %w", ctx.Err())
	}
}

// ReleaseWorker 释放工作线程资源
func (gc *GlobalConcurrencyController) ReleaseWorker(workflowRunID uint) {
	// 释放信号量
	<-gc.semaphore

	gc.mu.Lock()
	gc.activeCount--
	gc.metrics.ActiveWorkers = gc.activeCount
	gc.metrics.ProcessedRequests++
	gc.mu.Unlock()

	log.Printf("🔄 释放工作线程: WorkflowRunID=%d, 剩余活跃工作线程=%d/%d",
		workflowRunID, gc.getActiveCount(), gc.maxWorkers)
}

// GetMetrics 获取并发控制指标
func (gc *GlobalConcurrencyController) GetMetrics() ConcurrencyMetrics {
	gc.mu.RLock()
	defer gc.mu.RUnlock()

	// 返回指标副本
	return *gc.metrics
}

// ResetMetrics 重置指标统计
func (gc *GlobalConcurrencyController) ResetMetrics() {
	gc.mu.Lock()
	defer gc.mu.Unlock()

	gc.metrics = &ConcurrencyMetrics{
		LastResetTime: time.Now(),
		ActiveWorkers: gc.activeCount, // 保留当前活跃数
	}

	log.Printf("📊 并发控制指标已重置")
}

// getActiveCount 线程安全获取活跃工作线程数
func (gc *GlobalConcurrencyController) getActiveCount() int {
	gc.mu.RLock()
	defer gc.mu.RUnlock()
	return gc.activeCount
}

// getQueuedCount 线程安全获取排队请求数
func (gc *GlobalConcurrencyController) getQueuedCount() int {
	gc.mu.RLock()
	defer gc.mu.RUnlock()
	return gc.metrics.QueuedRequests
}

// GetAvailableWorkers 获取可用工作线程数
func (gc *GlobalConcurrencyController) GetAvailableWorkers() int {
	return gc.maxWorkers - gc.getActiveCount()
}

// IsAtCapacity 检查是否已达到最大容量
func (gc *GlobalConcurrencyController) IsAtCapacity() bool {
	return gc.getActiveCount() >= gc.maxWorkers
}

// UpdateMaxWorkers 动态更新最大工作线程数（谨慎使用）
func (gc *GlobalConcurrencyController) UpdateMaxWorkers(newMax int) error {
	if newMax <= 0 {
		return fmt.Errorf("最大工作线程数必须大于0")
	}

	gc.mu.Lock()
	defer gc.mu.Unlock()

	oldMax := gc.maxWorkers
	gc.maxWorkers = newMax

	// 重新创建信号量
	oldSemaphore := gc.semaphore
	gc.semaphore = make(chan struct{}, newMax)

	// 尝试迁移现有的工作线程
	currentActive := len(oldSemaphore)
	for i := 0; i < currentActive && i < newMax; i++ {
		gc.semaphore <- struct{}{}
	}

	log.Printf("🔧 更新最大工作线程数: %d -> %d", oldMax, newMax)
	return nil
}
