package workflow

import (
	"fmt"
	"log"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/autoProcess"
)

// ConcurrencyMetricsService 并发控制指标服务
type ConcurrencyMetricsService struct{}

// ConcurrencyStatusResp 并发状态响应
type ConcurrencyStatusResp struct {
	GlobalMetrics   ConcurrencyMetrics      `json:"global_metrics"`  // 全局并发指标
	SystemStatus    SystemConcurrencyStatus `json:"system_status"`   // 系统并发状态
	ActiveBatches   []ActiveBatchInfo       `json:"active_batches"`  // 活跃批量执行信息
	RecentRuns      []RecentWorkflowRun     `json:"recent_runs"`     // 最近工作流执行记录
	Recommendations []string                `json:"recommendations"` // 系统建议
}

// SystemConcurrencyStatus 系统并发状态
type SystemConcurrencyStatus struct {
	MaxGlobalWorkers    int     `json:"max_global_workers"`    // 全局最大工作线程数
	ActiveGlobalWorkers int     `json:"active_global_workers"` // 当前活跃全局工作线程数
	AvailableWorkers    int     `json:"available_workers"`     // 可用工作线程数
	CapacityUtilization float64 `json:"capacity_utilization"`  // 容量利用率(0-100%)
	IsAtCapacity        bool    `json:"is_at_capacity"`        // 是否已达到最大容量
	QueuedRequests      int     `json:"queued_requests"`       // 排队等待的请求数
	SystemLoad          string  `json:"system_load"`           // 系统负载状态: low/medium/high/critical
}

// ActiveBatchInfo 活跃批量执行信息
type ActiveBatchInfo struct {
	BatchID          uint       `json:"batch_id"`                     // 批量执行ID
	WorkflowID       int        `json:"workflow_id"`                  // 工作流ID
	TotalCount       int        `json:"total_count"`                  // 总任务数
	CompletedCount   int        `json:"completed_count"`              // 已完成数
	RunningCount     int        `json:"running_count"`                // 运行中数
	FailedCount      int        `json:"failed_count"`                 // 失败数
	Status           string     `json:"status"`                       // 状态
	StartTime        time.Time  `json:"start_time"`                   // 开始时间
	EstimatedEndTime *time.Time `json:"estimated_end_time,omitempty"` // 预计结束时间
	Progress         float64    `json:"progress"`                     // 进度百分比
}

// RecentWorkflowRun 最近工作流执行记录
type RecentWorkflowRun struct {
	WorkflowRunID uint      `json:"workflow_run_id"` // 工作流执行ID
	WorkflowID    int       `json:"workflow_id"`     // 工作流ID
	Status        int       `json:"status"`          // 状态
	StartTime     time.Time `json:"start_time"`      // 开始时间
	Duration      *int64    `json:"duration"`        // 执行时长(毫秒)
	BatchID       *uint     `json:"batch_id"`        // 批量执行ID(可为空)
}

// GetConcurrencyStatus 获取并发控制状态
func (s *ConcurrencyMetricsService) GetConcurrencyStatus() (*ConcurrencyStatusResp, error) {
	// 1. 获取全局并发指标
	globalController := GetGlobalConcurrencyController()
	globalMetrics := globalController.GetMetrics()

	// 2. 构建系统状态
	systemStatus := SystemConcurrencyStatus{
		MaxGlobalWorkers:    globalController.maxWorkers,
		ActiveGlobalWorkers: globalController.getActiveCount(),
		AvailableWorkers:    globalController.GetAvailableWorkers(),
		IsAtCapacity:        globalController.IsAtCapacity(),
		QueuedRequests:      globalController.getQueuedCount(),
	}

	// 计算容量利用率
	if systemStatus.MaxGlobalWorkers > 0 {
		systemStatus.CapacityUtilization = float64(systemStatus.ActiveGlobalWorkers) / float64(systemStatus.MaxGlobalWorkers) * 100
	}

	// 确定系统负载状态
	systemStatus.SystemLoad = s.determineSystemLoad(systemStatus.CapacityUtilization, systemStatus.QueuedRequests)

	// 3. 获取活跃批量执行信息
	activeBatches, err := s.getActiveBatches()
	if err != nil {
		log.Printf("⚠️ 获取活跃批量执行信息失败: %v", err)
		activeBatches = []ActiveBatchInfo{} // 使用空数组而不是返回错误
	}

	// 4. 获取最近工作流执行记录
	recentRuns, err := s.getRecentWorkflowRuns(10)
	if err != nil {
		log.Printf("⚠️ 获取最近工作流执行记录失败: %v", err)
		recentRuns = []RecentWorkflowRun{} // 使用空数组而不是返回错误
	}

	// 5. 生成系统建议
	recommendations := s.generateRecommendations(systemStatus, activeBatches)

	return &ConcurrencyStatusResp{
		GlobalMetrics:   globalMetrics,
		SystemStatus:    systemStatus,
		ActiveBatches:   activeBatches,
		RecentRuns:      recentRuns,
		Recommendations: recommendations,
	}, nil
}

// determineSystemLoad 确定系统负载状态
func (s *ConcurrencyMetricsService) determineSystemLoad(utilization float64, queuedRequests int) string {
	if queuedRequests > 10 || utilization >= 90 {
		return "critical"
	} else if queuedRequests > 5 || utilization >= 70 {
		return "high"
	} else if queuedRequests > 0 || utilization >= 50 {
		return "medium"
	}
	return "low"
}

// getActiveBatches 获取活跃批量执行信息
func (s *ConcurrencyMetricsService) getActiveBatches() ([]ActiveBatchInfo, error) {
	var batches []autoProcess.WorkflowBatchRuns

	// 查询运行中的批量执行
	err := global.GVA_DB.Where("status IN ?", []string{
		autoProcess.BatchStatusPending,
		autoProcess.BatchStatusRunning,
	}).Order("created_at DESC").Limit(10).Find(&batches).Error

	if err != nil {
		return nil, fmt.Errorf("查询活跃批量执行失败: %w", err)
	}

	activeBatches := make([]ActiveBatchInfo, 0, len(batches))
	for _, batch := range batches {
		progress := float64(0)
		if batch.TotalCount > 0 {
			completedCount := batch.SuccessCount + batch.FailedCount + batch.CancelledCount
			progress = float64(completedCount) / float64(batch.TotalCount) * 100
		}

		// 估算结束时间（简单算法）
		var estimatedEndTime *time.Time
		if batch.StartTime != nil && progress > 0 && progress < 100 {
			elapsed := time.Since(*batch.StartTime)
			estimatedTotal := time.Duration(float64(elapsed) / progress * 100)
			estimatedEnd := batch.StartTime.Add(estimatedTotal)
			estimatedEndTime = &estimatedEnd
		}

		activeBatches = append(activeBatches, ActiveBatchInfo{
			BatchID:          batch.ID,
			WorkflowID:       batch.WorkflowID,
			TotalCount:       batch.TotalCount,
			CompletedCount:   batch.SuccessCount + batch.FailedCount,
			RunningCount:     batch.RunningCount,
			FailedCount:      batch.FailedCount,
			Status:           batch.Status,
			StartTime:        batch.CreatedAt,
			EstimatedEndTime: estimatedEndTime,
			Progress:         progress,
		})
	}

	return activeBatches, nil
}

// getRecentWorkflowRuns 获取最近工作流执行记录
func (s *ConcurrencyMetricsService) getRecentWorkflowRuns(limit int) ([]RecentWorkflowRun, error) {
	var runs []autoProcess.WorkflowRuns

	err := global.GVA_DB.Select("id, workflow_id, status, created_at, elapsed_time, batch_id").
		Order("created_at DESC").Limit(limit).Find(&runs).Error

	if err != nil {
		return nil, fmt.Errorf("查询最近工作流执行记录失败: %w", err)
	}

	recentRuns := make([]RecentWorkflowRun, 0, len(runs))
	for _, run := range runs {
		recentRun := RecentWorkflowRun{
			WorkflowRunID: uint(run.ID),
			WorkflowID:    int(run.WorkflowID),
			Status:        run.Status,
			StartTime:     run.CreatedAt,
			BatchID:       run.BatchID,
		}

		// 转换执行时长
		if run.ElapsedTime > 0 {
			recentRun.Duration = &run.ElapsedTime
		}

		recentRuns = append(recentRuns, recentRun)
	}

	return recentRuns, nil
}

// generateRecommendations 生成系统建议
func (s *ConcurrencyMetricsService) generateRecommendations(status SystemConcurrencyStatus, batches []ActiveBatchInfo) []string {
	var recommendations []string

	// 基于系统负载的建议
	switch status.SystemLoad {
	case "critical":
		recommendations = append(recommendations, "⚠️ 系统负载过高，建议暂停新的批量任务，等待当前任务完成")
		recommendations = append(recommendations, "🔧 考虑优化工作流性能或增加系统资源")
	case "high":
		recommendations = append(recommendations, "⚡ 系统负载较高，建议减少新任务的提交频率")
		recommendations = append(recommendations, "📊 监控任务执行情况，注意是否有异常耗时的任务")
	case "medium":
		recommendations = append(recommendations, "✅ 系统负载正常，可以正常提交任务")
	case "low":
		recommendations = append(recommendations, "🚀 系统资源充足，可以考虑增加并发数以提高处理效率")
	}

	// 基于排队情况的建议
	if status.QueuedRequests > 0 {
		recommendations = append(recommendations, fmt.Sprintf("⏳ 当前有 %d 个请求在排队，预计等待时间较长", status.QueuedRequests))
	}

	// 基于活跃批量任务的建议
	if len(batches) > 5 {
		recommendations = append(recommendations, "📝 当前并发批量任务较多，建议合理安排任务执行时间")
	}

	// 基于容量利用率的建议
	if status.CapacityUtilization < 30 {
		recommendations = append(recommendations, "💡 系统利用率较低，可以考虑增加任务并发数")
	} else if status.CapacityUtilization > 85 {
		recommendations = append(recommendations, "⚠️ 系统利用率接近满载，建议谨慎添加新任务")
	}

	// 默认建议
	if len(recommendations) == 0 {
		recommendations = append(recommendations, "✅ 系统运行正常，建议定期监控并发状态")
	}

	return recommendations
}

// ResetGlobalMetrics 重置全局并发指标
func (s *ConcurrencyMetricsService) ResetGlobalMetrics() error {
	globalController := GetGlobalConcurrencyController()
	globalController.ResetMetrics()
	log.Printf("📊 全局并发指标已重置")
	return nil
}

// UpdateMaxWorkers 更新最大工作线程数
func (s *ConcurrencyMetricsService) UpdateMaxWorkers(newMax int) error {
	if newMax <= 0 || newMax > 10 {
		return fmt.Errorf("最大工作线程数必须在1-10之间")
	}

	globalController := GetGlobalConcurrencyController()
	err := globalController.UpdateMaxWorkers(newMax)
	if err != nil {
		return fmt.Errorf("更新最大工作线程数失败: %w", err)
	}

	log.Printf("🔧 全局最大工作线程数已更新为: %d", newMax)
	return nil
}

// GetSystemHealth 获取系统健康状态
func (s *ConcurrencyMetricsService) GetSystemHealth() map[string]interface{} {
	globalController := GetGlobalConcurrencyController()
	metrics := globalController.GetMetrics()

	health := map[string]interface{}{
		"status": "healthy",
		"concurrency": map[string]interface{}{
			"max_workers":     globalController.maxWorkers,
			"active_workers":  globalController.getActiveCount(),
			"queued_requests": globalController.getQueuedCount(),
			"utilization":     float64(globalController.getActiveCount()) / float64(globalController.maxWorkers) * 100,
		},
		"metrics": map[string]interface{}{
			"total_requests":     metrics.TotalRequests,
			"processed_requests": metrics.ProcessedRequests,
			"failed_requests":    metrics.FailedRequests,
			"average_wait_time":  metrics.AverageWaitTime.Milliseconds(),
			"max_wait_time":      metrics.MaxWaitTime.Milliseconds(),
		},
		"timestamp": time.Now().Unix(),
	}

	// 确定健康状态
	if globalController.getQueuedCount() > 10 {
		health["status"] = "degraded"
	}
	if globalController.IsAtCapacity() && globalController.getQueuedCount() > 5 {
		health["status"] = "unhealthy"
	}

	return health
}
