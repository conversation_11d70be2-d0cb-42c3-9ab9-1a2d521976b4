package workflow

import (
	"context"
	"fmt"
	"log"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/autoProcess"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
)

// BatchRunWorkflow 批量执行工作流的服务层实现（重构版本）
func BatchRunWorkflow(ctx context.Context, req *BatchWorkflowReq) (*BatchWorkflowResp, error) {

	// 1. 验证工作流是否存在且启用
	var workflow autoProcess.Workflow
	err := global.GVA_DB.Where("id = ? AND status = ?", req.WorkflowID, 1).First(&workflow).Error
	if err != nil {
		return nil, fmt.Errorf("工作流不存在或未启用: %d", req.WorkflowID)
	}

	// 2. 校验输入数据
	if len(req.Inputs) == 0 {
		return nil, fmt.Errorf("输入数据不能为空")
	}

	// 3. 确定任务分配用户（验证用户存在）
	if req.AssignedTo != nil && *req.AssignedTo > 0 {
		// 验证分配用户是否存在
		var user system.SysUser
		err := global.GVA_DB.Where("id = ?", *req.AssignedTo).First(&user).Error
		if err != nil {
			return nil, fmt.Errorf("指定的分配用户不存在: %d", *req.AssignedTo)
		}
	}

	// 4. 确定优先级
	priority := 1
	if req.Priority != nil && *req.Priority > 0 {
		priority = *req.Priority
	}

	// 5. 准备批量执行配置
	config := autoProcess.DefaultBatchExecutionConfig()
	if req.Config != nil {
		config = *req.Config
	}

	// 6. 创建批量执行记录
	callbackURL := ""
	if req.CallbackURL != nil {
		callbackURL = *req.CallbackURL
	}

	batchExecution := &autoProcess.WorkflowBatchRuns{
		WorkflowID:     req.WorkflowID,
		ExecutedBy:     req.ExecutedBy,
		TotalCount:     len(req.Inputs),
		SuccessCount:   0,
		FailedCount:    0,
		RunningCount:   len(req.Inputs), // 初始化为总数
		CancelledCount: 0,
		Status:         autoProcess.BatchStatusPending,
		CallbackURL:    callbackURL,
		Priority:       priority,
		MaxConcurrent:  config.MaxConcurrent,
		SingleTimeout:  int(config.SingleTimeout.Seconds()),
		BatchTimeout:   int(config.BatchTimeout.Seconds()),
		RetryAttempts:  config.RetryAttempts,
		IsCancelled:    false,
	}

	// 7. 保存批量执行记录到数据库
	err = global.GVA_DB.Create(batchExecution).Error
	if err != nil {
		return nil, fmt.Errorf("创建批量执行记录失败: %w", err)
	}

	// 8. 创建批量执行控制器
	controller := NewBatchExecutionController(batchExecution, config)
	if controller == nil {
		return nil, fmt.Errorf("创建批量执行控制器失败")
	}

	// 9. 异步执行批量工作流
	go func() {
		defer func() {
			if r := recover(); r != nil {
				log.Printf("❌ 批量执行发生panic: %v", r)
				// 更新状态为失败
				batchExecution.Status = autoProcess.BatchStatusFailed
				batchExecution.ErrorLogs = fmt.Sprintf("批量执行发生panic: %v", r)
				global.GVA_DB.Save(batchExecution)
			}
		}()

		_, err := controller.ExecuteBatch(req)
		if err != nil {
			log.Printf("❌ 批量执行失败: %v", err)
		}
	}()

	// 10. 构建响应
	resp := &BatchWorkflowResp{
		BatchID:      batchExecution.ID,
		Status:       batchExecution.Status,
		Message:      "批量执行已启动",
		TotalCount:   len(req.Inputs),
		SuccessCount: 0,
		FailedCount:  0,
		RunningCount: len(req.Inputs),
		PendingCount: 0,
	}

	return resp, nil
}

// isInterruptError 判断是否是正常的中断错误
func isInterruptError(err error) bool {
	// 这里可以根据具体的错误类型判断
	// 简化实现：包含"中断"或"interrupt"关键字的认为是正常中断
	errMsg := err.Error()
	return errMsg == "" || // 空错误认为是正常完成
		false // 暂时不做特殊判断，后续可以完善
}
