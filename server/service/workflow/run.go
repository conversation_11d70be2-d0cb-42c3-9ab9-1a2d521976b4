package workflow

import (
	"context"
	"fmt"
	"log"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/autoProcess"
	autoProcessService "github.com/flipped-aurora/gin-vue-admin/server/service/autoProcess"
	"github.com/flipped-aurora/gin-vue-admin/server/service/autoProcess/runAiImage"
)

// RunWorkflowReq 单个工作流执行请求
type RunWorkflowReq struct {
	WorkflowID  int                    `json:"workflow_id"`        // 工作流ID
	Input       map[string]interface{} `json:"input"`              // 输入数据
	AssignedTo  uint                   `json:"assigned_to"`        // 指定任务分配用户ID
	Priority    int                    `json:"priority"`           // 优先级
	CallbackURL string                 `json:"callback_url"`       // 完成回调地址
	ExecutedBy  uint                   `json:"executed_by"`        // 执行用户ID
	BatchID     *uint                  `json:"batch_id,omitempty"` // 批量执行ID
	BatchIndex  int                    `json:"batch_index"`        // 批量中的索引
	RetryCount  int                    `json:"retry_count"`        // 当前重试次数
	Timeout     time.Duration          `json:"timeout"`            // 超时时间
}

// RunWorkflowResp 单个工作流执行响应
type RunWorkflowResp struct {
	TaskID        int                    `json:"task_id"`                 // 任务ID
	WorkflowRunID int                    `json:"workflow_run_id"`         // 工作流运行ID
	Status        string                 `json:"status"`                  // 状态
	Message       string                 `json:"message"`                 // 消息
	Error         string                 `json:"error,omitempty"`         // 错误信息
	OutputData    map[string]interface{} `json:"output_data,omitempty"`   // 输出数据
	CallbackData  map[string]interface{} `json:"callback_data,omitempty"` // 回调数据
	StartTime     time.Time              `json:"start_time"`              // 开始时间
	EndTime       *time.Time             `json:"end_time,omitempty"`      // 结束时间
	Duration      time.Duration          `json:"duration"`                // 执行时长
}

// RunWorkflow 执行单个工作流的统一服务层接口（保持兼容性）
func RunWorkflow(ctx context.Context, req *RunWorkflowReq) (*RunWorkflowResp, error) {
	// 1. 参数验证
	if err := validateSingleWorkflowRequest(req); err != nil {
		return nil, fmt.Errorf("参数验证失败: %w", err)
	}

	// 2. 验证工作流是否存在且启用
	var workflow autoProcess.Workflow
	err := global.GVA_DB.Where("id = ? AND status = ?", req.WorkflowID, 1).First(&workflow).Error
	if err != nil {
		return nil, fmt.Errorf("工作流不存在或未启用: %d", req.WorkflowID)
	}

	// 3. 创建工作流运行记录
	workflowRun, err := createWorkflowRunRecord(req)
	if err != nil {
		return nil, fmt.Errorf("创建工作流运行记录失败: %w", err)
	}

	// 4. 设置执行超时
	execCtx := ctx
	if req.Timeout > 0 {
		var cancel context.CancelFunc
		execCtx, cancel = context.WithTimeout(ctx, req.Timeout)
		defer cancel()
	}

	// 5. 执行核心工作流逻辑
	execErr := ExecuteWorkflowCore(execCtx, uint(workflowRun.ID), req.Input)

	// 6. 处理执行结果并返回响应
	return handleWorkflowResult(uint(workflowRun.ID), execErr, req)
}

// ExecuteWorkflowCore 核心工作流执行逻辑（供批量执行调用）
// 此函数只负责执行，不涉及记录创建，适合批量执行场景
// 集成全局并发控制，确保不会超出系统承载能力
func ExecuteWorkflowCore(ctx context.Context, workflowRunID uint, inputData map[string]interface{}) error {
	// 1. 获取全局并发控制器
	globalController := GetGlobalConcurrencyController()

	// 2. 请求获取工作线程资源
	err := globalController.AcquireWorker(ctx, workflowRunID)
	if err != nil {
		return fmt.Errorf("获取全局工作线程资源失败: %w", err)
	}

	// 3. 确保在函数退出时释放资源
	defer globalController.ReleaseWorker(workflowRunID)

	// 4. 查询工作流运行记录获取基本信息
	var workflowRun autoProcess.WorkflowRuns
	err = global.GVA_DB.First(&workflowRun, workflowRunID).Error
	if err != nil {
		return fmt.Errorf("查询工作流运行记录失败: %w", err)
	}

	// 5. 准备输入数据
	coreInputData := make(map[string]any)
	for k, v := range inputData {
		coreInputData[k] = v
	}

	log.Printf("🚀 开始执行工作流核心逻辑: WorkflowRunID=%d, WorkflowID=%d",
		workflowRunID, workflowRun.WorkflowID)

	// 6. 调用核心执行逻辑，传入已有记录ID
	execErr := runAiImage.RunWorkflow(
		ctx,
		workflowRun.ExecutedBy,
		workflowRun.WorkflowID,
		workflowRun.ExecutedBy, // 暂时使用同一个用户ID作为任务分配用户
		coreInputData,
		workflowRunID, // 传入已有记录ID
	)

	if execErr != nil {
		log.Printf("❌ 工作流核心逻辑执行失败: WorkflowRunID=%d, Error=%v", workflowRunID, execErr)
	} else {
		log.Printf("✅ 工作流核心逻辑执行成功: WorkflowRunID=%d", workflowRunID)
	}

	return execErr
}

// createWorkflowRunRecord 创建工作流运行记录
func createWorkflowRunRecord(req *RunWorkflowReq) (*autoProcess.WorkflowRuns, error) {
	// 准备输入数据
	inputData := make(map[string]any)
	for k, v := range req.Input {
		inputData[k] = v
	}

	// 添加执行元数据
	inputData["priority"] = req.Priority
	inputData["callback_url"] = req.CallbackURL
	inputData["retry_count"] = req.RetryCount
	if req.BatchID != nil {
		inputData["batch_id"] = *req.BatchID
		inputData["batch_index"] = req.BatchIndex
	}

	// 创建工作流运行记录
	workflowRun := &autoProcess.WorkflowRuns{
		WorkflowID:  autoProcess.WorkflowId(req.WorkflowID),
		Status:      autoProcess.WorkflowRunStatusRunning,
		Inputs:      inputData,
		ExecutedBy:  req.ExecutedBy,
		CallbackURL: req.CallbackURL,
		RetryCount:  req.RetryCount,
	}

	// 如果是批量执行，设置批量相关字段
	if req.BatchID != nil {
		batchID := *req.BatchID
		workflowRun.BatchID = &batchID
		batchIndex := req.BatchIndex
		workflowRun.BatchIndex = &batchIndex
	}

	err := global.GVA_DB.Create(workflowRun).Error
	if err != nil {
		return nil, fmt.Errorf("创建工作流运行记录失败: %w", err)
	}

	return workflowRun, nil
}

// handleWorkflowResult 处理工作流执行结果
func handleWorkflowResult(workflowRunID uint, execErr error, req *RunWorkflowReq) (*RunWorkflowResp, error) {
	// 查询最新的工作流运行记录
	var workflowRun autoProcess.WorkflowRuns
	err := global.GVA_DB.First(&workflowRun, workflowRunID).Error
	if err != nil {
		log.Printf("❌ 查询工作流运行记录失败: %v", err)
		return nil, fmt.Errorf("查询工作流运行记录失败: %w", err)
	}

	// 构建响应
	resp := &RunWorkflowResp{
		WorkflowRunID: int(workflowRun.ID),
		StartTime:     workflowRun.CreatedAt,
	}

	if workflowRun.EndTime != nil {
		resp.EndTime = workflowRun.EndTime
		resp.Duration = workflowRun.EndTime.Sub(workflowRun.CreatedAt)
	} else {
		now := time.Now()
		resp.EndTime = &now
		resp.Duration = now.Sub(workflowRun.CreatedAt)
	}

	// 处理执行结果
	if execErr != nil {
		// 检查是否是正常的中断（等待用户确认）
		if isInterruptErrorSingle(execErr) {
			resp.Status = autoProcess.ItemStatusPending
			resp.Message = "工作流已启动，等待用户确认"
			log.Printf("✅ 工作流正常中断（等待确认）: %v", execErr)
		} else {
			resp.Status = autoProcess.ItemStatusFailed
			resp.Message = "工作流执行失败"
			resp.Error = execErr.Error()
			log.Printf("❌ 工作流执行失败: %v", execErr)
		}
	} else {
		resp.Status = autoProcess.ItemStatusCompleted
		resp.Message = "工作流执行完成"
	}

	// 查询任务信息（如果存在）
	if resp.Status == autoProcess.ItemStatusPending {
		var latestTask autoProcess.Task
		err = global.GVA_DB.Where("workflow_run_id = ?", workflowRunID).
			Order("created_at DESC").
			First(&latestTask).Error
		if err == nil {
			resp.TaskID = int(latestTask.ID)
		}
	}

	// 准备回调数据
	if err := prepareCallbackData(resp, req); err != nil {
		log.Printf("⚠️ 准备回调数据失败: %v", err)
	}

	// 发送回调（单独执行时）
	if req.BatchID == nil && workflowRun.CallbackURL != "" {
		go func() {
			callbackService := autoProcessService.NewCallbackService()
			if err := callbackService.ProcessAndSendCallback(&workflowRun); err != nil {
				log.Printf("❌ 发送工作流回调失败: WorkflowRun ID=%d, 错误: %v", workflowRun.ID, err)
			} else {
				log.Printf("✅ 工作流回调发送成功: WorkflowRun ID=%d", workflowRun.ID)
			}
		}()
	}

	return resp, nil
}

// validateSingleWorkflowRequest 验证单个工作流执行请求参数
func validateSingleWorkflowRequest(req *RunWorkflowReq) error {
	if req.WorkflowID <= 0 {
		return fmt.Errorf("工作流ID必须大于0")
	}
	if req.ExecutedBy == 0 {
		return fmt.Errorf("执行用户ID不能为空")
	}
	if req.AssignedTo == 0 {
		return fmt.Errorf("分配用户ID不能为空")
	}
	if req.Input == nil {
		return fmt.Errorf("输入数据不能为空")
	}
	if req.Priority <= 0 {
		req.Priority = 1 // 设置默认优先级
	}
	if req.Timeout <= 0 {
		req.Timeout = 15 * time.Minute // 设置默认超时时间
	}
	return nil
}

// prepareCallbackData 准备回调数据
func prepareCallbackData(resp *RunWorkflowResp, req *RunWorkflowReq) error {
	// TODO: 这里需要根据eino框架的回调机制来实现
	// 现在先简单准备基本的回调数据

	callbackData := map[string]interface{}{
		"workflow_id":     req.WorkflowID,
		"workflow_run_id": resp.WorkflowRunID,
		"task_id":         resp.TaskID,
		"status":          resp.Status,
		"message":         resp.Message,
		"start_time":      resp.StartTime,
		"end_time":        resp.EndTime,
		"duration":        resp.Duration.Seconds(),
	}

	if req.BatchID != nil {
		callbackData["batch_id"] = *req.BatchID
		callbackData["batch_index"] = req.BatchIndex
	}

	if resp.Error != "" {
		callbackData["error"] = resp.Error
	}

	resp.CallbackData = callbackData
	return nil
}

// isInterruptErrorSingle 判断是否是正常的中断错误
func isInterruptErrorSingle(err error) bool {
	// 这里可以根据具体的错误类型判断
	// 简化实现：包含"中断"或"interrupt"关键字的认为是正常中断
	errMsg := err.Error()
	return errMsg == "" // 空错误认为是正常完成
}

// BatchWorkflowReq 批量工作流执行请求（重构后的）
type BatchWorkflowReq struct {
	WorkflowID  int                               `json:"workflow_id"`      // 工作流ID
	Inputs      []map[string]interface{}          `json:"inputs"`           // 输入数据数组
	AssignedTo  *int                              `json:"assigned_to"`      // 指定任务分配用户ID
	Priority    *int                              `json:"priority"`         // 优先级
	CallbackURL *string                           `json:"callback_url"`     // 完成回调地址
	ExecutedBy  uint                              `json:"executed_by"`      // 执行用户ID
	Config      *autoProcess.BatchExecutionConfig `json:"config,omitempty"` // 批量执行配置
}

// BatchWorkflowResp 批量工作流执行响应（重构后的）
type BatchWorkflowResp struct {
	BatchID      uint      `json:"batch_id"`      // 批量执行ID
	Status       string    `json:"status"`        // 状态
	Message      string    `json:"message"`       // 消息
	TotalCount   int       `json:"total_count"`   // 总任务数
	SuccessCount int       `json:"success_count"` // 成功任务数
	FailedCount  int       `json:"failed_count"`  // 失败任务数
	RunningCount int       `json:"running_count"` // 运行中任务数
	PendingCount int       `json:"pending_count"` // 待执行任务数
	StartTime    time.Time `json:"start_time"`    // 开始时间
}
