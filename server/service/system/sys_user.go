package system

import (
	"errors"
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/model/common"
	systemReq "github.com/flipped-aurora/gin-vue-admin/server/model/system/request"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

//@author: [piexlmax](https://github.com/piexlmax)
//@function: Register
//@description: 用户注册
//@param: u model.SysUser
//@return: userInter system.SysUser, err error

type UserService struct{}

var UserServiceApp = new(UserService)

func (userService *UserService) Register(TenantDB *gorm.DB, u system.SysUser) (userInter system.SysUser, err error) {
	var user system.SysUser
	if !errors.Is(TenantDB.Where("username = ?", u.Username).First(&user).Error, gorm.ErrRecordNotFound) { // 判断用户名是否注册
		return userInter, errors.New("用户名已注册")
	}
	// 否则 附加uuid 密码hash加密 注册
	u.Password = utils.BcryptHash(u.Password)
	u.UUID = uuid.New()
	err = TenantDB.Create(&u).Error
	return u, err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@author: [SliverHorn](https://github.com/SliverHorn)
//@function: Login
//@description: 用户登录
//@param: u *model.SysUser
//@return: err error, userInter *model.SysUser

func (userService *UserService) Login(TenantDB *gorm.DB, u *system.SysUser) (userInter *system.SysUser, err error) {
	if nil == TenantDB {
		return nil, fmt.Errorf("db not init")
	}

	var user system.SysUser
	err = TenantDB.Where("username = ?", u.Username).Preload("Authorities").Preload("Authority").First(&user).Error
	if err == nil {
		if ok := utils.BcryptCheck(u.Password, user.Password); !ok {
			return nil, errors.New("密码错误")
		}
		MenuServiceApp.UserAuthorityDefaultRouter(TenantDB, &user)
	}
	return &user, err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: ChangePassword
//@description: 修改用户密码
//@param: u *model.SysUser, newPassword string
//@return: userInter *model.SysUser,err error

func (userService *UserService) ChangePassword(TenantDB *gorm.DB, u *system.SysUser, newPassword string) (userInter *system.SysUser, err error) {
	var user system.SysUser
	if err = TenantDB.Where("id = ?", u.ID).First(&user).Error; err != nil {
		return nil, err
	}
	if ok := utils.BcryptCheck(u.Password, user.Password); !ok {
		return nil, errors.New("原密码错误")
	}
	user.Password = utils.BcryptHash(newPassword)
	err = TenantDB.Save(&user).Error
	return &user, err

}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: GetUserInfoList
//@description: 分页获取数据
//@param: info request.PageInfo
//@return: err error, list interface{}, total int64

func (userService *UserService) GetUserInfoList(TenantDB *gorm.DB, info systemReq.GetUserList) (list interface{}, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	db := TenantDB.Model(&system.SysUser{})
	var userList []system.SysUser

	if info.NickName != "" {
		db = db.Where("nick_name LIKE ?", "%"+info.NickName+"%")
	}
	if info.Phone != "" {
		db = db.Where("phone LIKE ?", "%"+info.Phone+"%")
	}
	if info.Username != "" {
		db = db.Where("username LIKE ?", "%"+info.Username+"%")
	}
	if info.Email != "" {
		db = db.Where("email LIKE ?", "%"+info.Email+"%")
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	err = db.Limit(limit).Offset(offset).Preload("Authorities").Preload("Authority").Find(&userList).Error
	return userList, total, err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: SetUserAuthority
//@description: 设置一个用户的权限
//@param: uuid uuid.UUID, authorityId string
//@return: err error

func (userService *UserService) SetUserAuthority(TenantDB *gorm.DB, id uint, authorityId uint) (err error) {

	assignErr := TenantDB.Where("sys_user_id = ? AND sys_authority_authority_id = ?", id, authorityId).First(&system.SysUserAuthority{}).Error
	if errors.Is(assignErr, gorm.ErrRecordNotFound) {
		return errors.New("该用户无此角色")
	}

	var authority system.SysAuthority
	err = TenantDB.Where("authority_id = ?", authorityId).First(&authority).Error
	if err != nil {
		return err
	}
	var authorityMenu []system.SysAuthorityMenu
	var authorityMenuIDs []string
	err = TenantDB.Where("sys_authority_authority_id = ?", authorityId).Find(&authorityMenu).Error
	if err != nil {
		return err
	}

	for i := range authorityMenu {
		authorityMenuIDs = append(authorityMenuIDs, authorityMenu[i].MenuId)
	}

	var authorityMenus []system.SysBaseMenu
	err = TenantDB.Preload("Parameters").Where("id in (?)", authorityMenuIDs).Find(&authorityMenus).Error
	if err != nil {
		return err
	}
	hasMenu := false
	for i := range authorityMenus {
		if authorityMenus[i].Name == authority.DefaultRouter {
			hasMenu = true
			break
		}
	}
	if !hasMenu {
		return errors.New("找不到默认路由,无法切换本角色")
	}

	err = TenantDB.Model(&system.SysUser{}).Where("id = ?", id).Update("authority_id", authorityId).Error
	return err
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: SetUserAuthorities
//@description: 设置一个用户的权限
//@param: id uint, authorityIds []string
//@return: err error

func (userService *UserService) SetUserAuthorities(TenantDB *gorm.DB, adminAuthorityID, id uint, authorityIds []uint) (err error) {
	return TenantDB.Transaction(func(tx *gorm.DB) error {
		var user system.SysUser
		TxErr := tx.Where("id = ?", id).First(&user).Error
		if TxErr != nil {
			global.GVA_LOG.Debug(TxErr.Error())
			return errors.New("查询用户数据失败")
		}
		TxErr = tx.Delete(&[]system.SysUserAuthority{}, "sys_user_id = ?", id).Error
		if TxErr != nil {
			return TxErr
		}
		var useAuthority []system.SysUserAuthority
		for _, v := range authorityIds {
			e := AuthorityServiceApp.CheckAuthorityIDAuth(TenantDB, adminAuthorityID, v)
			if e != nil {
				return e
			}
			useAuthority = append(useAuthority, system.SysUserAuthority{
				SysUserId: id, SysAuthorityAuthorityId: v,
			})
		}
		TxErr = tx.Create(&useAuthority).Error
		if TxErr != nil {
			return TxErr
		}
		TxErr = tx.Model(&user).Update("authority_id", authorityIds[0]).Error
		if TxErr != nil {
			return TxErr
		}
		// 返回 nil 提交事务
		return nil
	})
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: DeleteUser
//@description: 删除用户
//@param: id float64
//@return: err error

func (userService *UserService) DeleteUser(TenantDB *gorm.DB, id int) (err error) {
	return TenantDB.Transaction(func(tx *gorm.DB) error {
		if err := tx.Where("id = ?", id).Delete(&system.SysUser{}).Error; err != nil {
			return err
		}
		if err := tx.Delete(&[]system.SysUserAuthority{}, "sys_user_id = ?", id).Error; err != nil {
			return err
		}
		return nil
	})
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: SetUserInfo
//@description: 设置用户信息
//@param: reqUser model.SysUser
//@return: err error, user model.SysUser

func (userService *UserService) SetUserInfo(TenantDB *gorm.DB, req system.SysUser) error {
	return TenantDB.Model(&system.SysUser{}).
		Select("updated_at", "nick_name", "header_img", "phone", "email", "enable").
		Where("id=?", req.ID).
		Updates(map[string]interface{}{
			"updated_at": time.Now(),
			"nick_name":  req.NickName,
			"header_img": req.HeaderImg,
			"phone":      req.Phone,
			"email":      req.Email,
			"enable":     req.Enable,
		}).Error
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: SetSelfInfo
//@description: 设置用户信息
//@param: reqUser model.SysUser
//@return: err error, user model.SysUser

func (userService *UserService) SetSelfInfo(TenantDB *gorm.DB, req system.SysUser) error {
	return TenantDB.Model(&system.SysUser{}).
		Where("id=?", req.ID).
		Updates(req).Error
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: SetSelfSetting
//@description: 设置用户配置
//@param: req datatypes.JSON, uid uint
//@return: err error

func (userService *UserService) SetSelfSetting(TenantDB *gorm.DB, req common.JSONMap, uid uint) error {
	return TenantDB.Model(&system.SysUser{}).Where("id = ?", uid).Update("origin_setting", req).Error
}

//@author: [piexlmax](https://github.com/piexlmax)
//@author: [SliverHorn](https://github.com/SliverHorn)
//@function: GetUserInfo
//@description: 获取用户信息
//@param: uuid uuid.UUID
//@return: err error, user system.SysUser

func (userService *UserService) GetUserInfo(TenantDB *gorm.DB, uuid uuid.UUID) (user system.SysUser, err error) {
	var reqUser system.SysUser
	err = TenantDB.Preload("Authorities").Preload("Authority").First(&reqUser, "uuid = ?", uuid).Error
	if err != nil {
		return reqUser, err
	}
	MenuServiceApp.UserAuthorityDefaultRouter(TenantDB, &reqUser)
	return reqUser, err
}

//@author: [SliverHorn](https://github.com/SliverHorn)
//@function: FindUserById
//@description: 通过id获取用户信息
//@param: id int
//@return: err error, user *model.SysUser

func (userService *UserService) FindUserById(TenantDB *gorm.DB, id int) (user *system.SysUser, err error) {
	var u system.SysUser
	err = TenantDB.Where("id = ?", id).First(&u).Error
	return &u, err
}

//@author: [SliverHorn](https://github.com/SliverHorn)
//@function: FindUserByUuid
//@description: 通过uuid获取用户信息
//@param: uuid string
//@return: err error, user *model.SysUser

func (userService *UserService) FindUserByUuid(TenantDB *gorm.DB, uuid string) (user *system.SysUser, err error) {
	var u system.SysUser
	if err = TenantDB.Where("uuid = ?", uuid).First(&u).Error; err != nil {
		return &u, errors.New("用户不存在")
	}
	return &u, nil
}

//@author: [piexlmax](https://github.com/piexlmax)
//@function: ResetPassword
//@description: 修改用户密码
//@param: ID uint
//@return: err error

func (userService *UserService) ResetPassword(TenantDB *gorm.DB, ID uint, password string) (err error) {
	err = TenantDB.Model(&system.SysUser{}).Where("id = ?", ID).Update("password", utils.BcryptHash(password)).Error
	return err
}

// ==== 全局用户相关方法 ====

// GetGlobalUserByUsername 根据用户名获取全局用户信息
func (userService *UserService) GetGlobalUserByUsername(username string) (globalUser system.GlobalUser, err error) {
	err = global.GVA_DB.Where("username = ?", username).First(&globalUser).Error
	return globalUser, err
}

// GetUserTenantRelation 获取用户租户关系
func (userService *UserService) GetUserTenantRelation(globalUserId uint, tenantId string) (relation system.UserTenantRelation, err error) {
	err = global.GVA_DB.Where("global_user_id = ? AND tenant_id = ?", globalUserId, tenantId).First(&relation).Error
	return relation, err
}

// GetTenantUserById 根据ID获取租户内用户信息
func (userService *UserService) GetTenantUserById(tenantDB *gorm.DB, userId uint) (user system.SysUser, err error) {
	err = tenantDB.Where("id = ?", userId).Preload("Authorities").Preload("Authority").First(&user).Error
	return user, err
}

// CheckGlobalUsernameUnique 检查全局用户名是否唯一
func (userService *UserService) CheckGlobalUsernameUnique(username string, excludeId ...uint) error {
	var count int64
	query := global.GVA_DB.Model(&system.GlobalUser{}).Where("username = ?", username)

	// 如果提供了excludeId，则排除当前用户（用于更新时的检查）
	if len(excludeId) > 0 && excludeId[0] > 0 {
		query = query.Where("id != ?", excludeId[0])
	}

	err := query.Count(&count).Error
	if err != nil {
		return err
	}

	if count > 0 {
		return errors.New("用户名已存在，请使用其他用户名")
	}

	return nil
}

// CreateGlobalUser 创建全局用户
func (userService *UserService) CreateGlobalUser(globalUser *system.GlobalUser) error {
	// 检查用户名是否已存在
	if err := userService.CheckGlobalUsernameUnique(globalUser.Username); err != nil {
		return err
	}

	// 加密密码
	globalUser.Password = utils.BcryptHash(globalUser.Password)
	globalUser.UUID = uuid.New()

	return global.GVA_DB.Create(globalUser).Error
}

// UpdateGlobalUser 更新全局用户
func (userService *UserService) UpdateGlobalUser(globalUser *system.GlobalUser) error {
	// 检查用户名是否已存在（排除当前用户）
	if err := userService.CheckGlobalUsernameUnique(globalUser.Username, globalUser.ID); err != nil {
		return err
	}

	// 如果密码有变化，则加密
	if globalUser.Password != "" {
		globalUser.Password = utils.BcryptHash(globalUser.Password)
	} else {
		// 如果密码为空，则不更新密码字段
		return global.GVA_DB.Model(globalUser).Omit("password").Updates(globalUser).Error
	}

	return global.GVA_DB.Model(globalUser).Updates(globalUser).Error
}

// CreateUserTenantRelation 建立用户租户关系
func (userService *UserService) CreateUserTenantRelation(relation *system.UserTenantRelation) error {
	// 检查关系是否已存在
	var existingRelation system.UserTenantRelation
	if !errors.Is(global.GVA_DB.Where("global_user_id = ? AND tenant_id = ?",
		relation.GlobalUserId, relation.TenantId).First(&existingRelation).Error, gorm.ErrRecordNotFound) {
		return errors.New("用户租户关系已存在")
	}

	return global.GVA_DB.Create(relation).Error
}

// GetGlobalUserByUsernameAndTenant 根据用户名查找全局用户（用于无租户登录）
func (userService *UserService) GetGlobalUserByUsernameAndTenant(username string) (globalUser system.GlobalUser, err error) {
	// 尝试直接查找用户名
	err = global.GVA_DB.Where("username = ?", username).First(&globalUser).Error
	if err == nil {
		return globalUser, nil
	}

	// 如果未找到，可能用户名包含租户后缀，尝试模糊匹配
	err = global.GVA_DB.Where("username LIKE ?", username+"%").First(&globalUser).Error
	return globalUser, err
}

// GetUserPrimaryTenant 获取用户的主租户信息
func (userService *UserService) GetUserPrimaryTenant(globalUserId uint) (relation system.UserTenantRelation, err error) {
	err = global.GVA_DB.Where("global_user_id = ? AND is_primary = ?", globalUserId, true).First(&relation).Error
	return relation, err
}

// GetUserTenantList 获取用户所属的所有租户
func (userService *UserService) GetUserTenantList(globalUserId uint) (relations []system.UserTenantRelation, err error) {
	err = global.GVA_DB.Where("global_user_id = ?", globalUserId).Find(&relations).Error
	return relations, err
}

// CreateTenantUserWithGlobalCheck 创建租户用户时进行全局唯一性检查
func (userService *UserService) CreateTenantUserWithGlobalCheck(tenantDB *gorm.DB, tenantId string, user system.SysUser) (userInter system.SysUser, err error) {
	// 1. 检查全局用户名唯一性
	if err := userService.CheckGlobalUsernameUnique(user.Username); err != nil {
		return userInter, err
	}

	// 2. 在租户数据库中创建用户
	userInter, err = userService.Register(tenantDB, user)
	if err != nil {
		return userInter, err
	}

	// 3. 创建全局用户记录
	globalUser := system.GlobalUser{
		Username: user.Username,
		Password: user.Password, // Register方法已经加密了密码，这里需要原始密码再次加密
		Email:    user.Email,
		Phone:    user.Phone,
		TenantId: tenantId,
		IsActive: user.Enable == 1,
	}

	// 为全局用户重新加密密码，因为Register方法已经加密了密码
	globalUser.Password = utils.BcryptHash(user.Password)
	globalUser.UUID = uuid.New()

	err = global.GVA_DB.Create(&globalUser).Error
	if err != nil {
		// 如果全局用户创建失败，需要删除已创建的租户用户
		userService.DeleteUser(tenantDB, int(userInter.ID))
		return userInter, fmt.Errorf("创建全局用户失败: %v", err)
	}

	// 4. 建立用户租户关系
	relation := system.UserTenantRelation{
		GlobalUserId: globalUser.ID,
		TenantId:     tenantId,
		TenantUserId: userInter.ID,
		IsPrimary:    true, // 创建用户的租户设为主租户
	}

	err = userService.CreateUserTenantRelation(&relation)
	if err != nil {
		// 如果关系创建失败，删除已创建的全局用户和租户用户
		global.GVA_DB.Delete(&globalUser)
		userService.DeleteUser(tenantDB, int(userInter.ID))
		return userInter, fmt.Errorf("创建用户租户关系失败: %v", err)
	}

	return userInter, nil
}

// UpdateTenantUserWithGlobalCheck 更新租户用户时进行全局唯一性检查
func (userService *UserService) UpdateTenantUserWithGlobalCheck(tenantDB *gorm.DB, tenantId string, user system.SysUser) error {
	// 1. 获取当前用户的全局用户记录
	var globalUser system.GlobalUser
	err := global.GVA_DB.Where("tenant_id = ? AND username = ?", tenantId, user.Username).First(&globalUser).Error
	if err != nil && !errors.Is(err, gorm.ErrRecordNotFound) {
		return fmt.Errorf("查找全局用户失败: %v", err)
	}

	// 2. 如果用户名有变化，检查全局唯一性
	if !errors.Is(err, gorm.ErrRecordNotFound) && globalUser.Username != user.Username {
		if err := userService.CheckGlobalUsernameUnique(user.Username, globalUser.ID); err != nil {
			return err
		}

		// 更新全局用户信息
		globalUser.Username = user.Username
		globalUser.Email = user.Email
		globalUser.Phone = user.Phone
		globalUser.IsActive = user.Enable == 1

		if err := userService.UpdateGlobalUser(&globalUser); err != nil {
			return fmt.Errorf("更新全局用户失败: %v", err)
		}
	}

	// 3. 更新租户用户
	return userService.SetUserInfo(tenantDB, user)
}
