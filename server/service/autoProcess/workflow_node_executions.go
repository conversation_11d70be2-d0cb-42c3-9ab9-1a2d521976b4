package autoProcess

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/autoProcess"
	autoProcessReq "github.com/flipped-aurora/gin-vue-admin/server/model/autoProcess/request"
)

type WorkflowNodeExecutionsService struct{}

// CreateWorkflowNodeExecutions 创建工作流节点执行记录
func (service *WorkflowNodeExecutionsService) CreateWorkflowNodeExecutions(ctx context.Context, workflowNodeExecutions *autoProcess.WorkflowNodeExecutions) (err error) {
	err = global.GVA_DB.Create(workflowNodeExecutions).Error
	return err
}

// DeleteWorkflowNodeExecutions 删除工作流节点执行记录
func (service *WorkflowNodeExecutionsService) DeleteWorkflowNodeExecutions(ctx context.Context, ID autoProcess.WorkflowNodeExecutionId) (err error) {
	err = global.GVA_DB.Delete(&autoProcess.WorkflowNodeExecutions{}, "id = ?", ID).Error
	return err
}

// DeleteWorkflowNodeExecutionsByIds 批量删除工作流节点执行记录
func (service *WorkflowNodeExecutionsService) DeleteWorkflowNodeExecutionsByIds(ctx context.Context, IDs []autoProcess.WorkflowNodeExecutionId) (err error) {
	err = global.GVA_DB.Delete(&[]autoProcess.WorkflowNodeExecutions{}, "id in ?", IDs).Error
	return err
}

// UpdateWorkflowNodeExecutions 更新工作流节点执行记录
func (service *WorkflowNodeExecutionsService) UpdateWorkflowNodeExecutions(ctx context.Context, workflowNodeExecutions autoProcess.WorkflowNodeExecutions) (err error) {
	err = global.GVA_DB.Model(&autoProcess.WorkflowNodeExecutions{}).Where("id = ?", workflowNodeExecutions.ID).Updates(&workflowNodeExecutions).Error
	return err
}

// GetWorkflowNodeExecutions 根据ID获取工作流节点执行记录
func (service *WorkflowNodeExecutionsService) GetWorkflowNodeExecutions(ctx context.Context, ID autoProcess.WorkflowNodeExecutionId) (workflowNodeExecutions autoProcess.WorkflowNodeExecutions, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&workflowNodeExecutions).Error
	return
}

// GetWorkflowNodeExecutionsList 分页获取工作流节点执行记录列表
func (service *WorkflowNodeExecutionsService) GetWorkflowNodeExecutionsList(ctx context.Context, info autoProcessReq.WorkflowNodeExecutionsSearch) (list []autoProcess.WorkflowNodeExecutions, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)

	db := global.GVA_DB.Model(&autoProcess.WorkflowNodeExecutions{})

	// 搜索条件
	if info.WorkflowID > 0 {
		db = db.Where("workflow_id = ?", info.WorkflowID)
	}
	if info.WorkflowRunID > 0 {
		db = db.Where("workflow_run_id = ?", info.WorkflowRunID)
	}
	if info.NodeName != "" {
		db = db.Where("node_name LIKE ?", "%"+info.NodeName+"%")
	}
	if info.Status > 0 {
		db = db.Where("status = ?", info.Status)
	}
	if !info.CreatedStart.IsZero() {
		db = db.Where("created_at >= ?", info.CreatedStart)
	}
	if !info.CreatedEnd.IsZero() {
		db = db.Where("created_at <= ?", info.CreatedEnd)
	}

	err = db.Count(&total).Error
	if err != nil {
		return
	}

	err = db.Limit(limit).Offset(offset).Order("created_at desc").Find(&list).Error
	return list, total, err
}

// GetWorkflowNodeExecutionsByRunID 根据工作流执行ID获取所有节点执行记录
func (service *WorkflowNodeExecutionsService) GetWorkflowNodeExecutionsByRunID(ctx context.Context, workflowRunID autoProcess.WorkflowRunId) (list []autoProcess.WorkflowNodeExecutions, err error) {
	err = global.GVA_DB.Where("workflow_run_id = ?", workflowRunID).Order("`index` asc, created_at asc").Find(&list).Error
	return list, err
}
