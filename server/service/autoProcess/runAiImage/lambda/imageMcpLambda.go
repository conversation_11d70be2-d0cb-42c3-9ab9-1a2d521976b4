package lambda

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"log"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/pkg/einoext/components/lambda"
	"go.uber.org/zap"
)

// safeGetImageUrl 安全地从output中提取image_url，如果失败则返回原图URL并记录日志
func safeGetImageUrl(output map[string]any, originalUrl any, operation string) string {
	// 使用通用提取器尝试获取image_url
	imageUrl, ok := ExtractImageUrl(output, "result.data.data.image_url")
	if ok {
		return imageUrl
	}

	// 如果提取失败，记录完整的output日志
	outputJson, _ := json.Marshal(output)
	if global.GVA_LOG != nil {
		global.GVA_LOG.Error(fmt.Sprintf("%s - 无法从返回结果中获取image_url", operation),
			zap.String("完整返回数据", string(outputJson)),
			zap.Any("原始URL", originalUrl))
	}

	// 返回原图URL作为fallback
	if originalUrlStr, ok := originalUrl.(string); ok {
		return originalUrlStr
	}
	return ""
}

// safeGetMaskUrl 安全地从output中提取mask_url，如果失败则返回空字符串并记录日志
func safeGetMaskUrl(output map[string]any, originalUrl any, operation string) string {
	// 使用通用提取器尝试获取mask_url
	maskUrl, ok := ExtractMaskUrl(output)
	if ok {
		return maskUrl
	}

	// 如果提取失败，记录完整的output日志
	outputJson, _ := json.Marshal(output)
	if global.GVA_LOG != nil {
		global.GVA_LOG.Error(fmt.Sprintf("%s - 无法从返回结果中获取mask_url", operation),
			zap.String("完整返回数据", string(outputJson)),
			zap.Any("原始URL", originalUrl))
	}

	return ""
}

// safeGetTranslateImageUrl 专门用于图片翻译的image_url提取
func safeGetTranslateImageUrl(output map[string]any, originalUrl any, operation string) string {
	// 图片翻译的返回结构：result.data.image_url (直接在data下，不是data.data)
	// 使用通用提取器，指定特定路径
	imageUrl, ok := ExtractImageUrl(output, "result.data.image_url")
	if ok {
		return imageUrl
	}

	// 如果提取失败，记录完整的output日志
	outputJson, _ := json.Marshal(output)
	if global.GVA_LOG != nil {
		global.GVA_LOG.Error(fmt.Sprintf("%s - 无法从返回结果中获取image_url", operation),
			zap.String("完整返回数据", string(outputJson)),
			zap.Any("原始URL", originalUrl))
	}

	// 返回原图URL作为fallback
	if originalUrlStr, ok := originalUrl.(string); ok {
		return originalUrlStr
	}
	return ""
}

// ImageMattingMcpLambda 抠图
func ImageMattingMcpLambda(ctx context.Context, input map[string]any, opts ...lambda.McpLambdaOption) (output map[string]any, err error) {
	inputImageUrl := ExtractStringFromInput(input, "image_url", "")
	if inputImageUrl == "" {
		inputImageUrl = ExtractStringFromInput(input, "imageUrl", "")
	}
	if inputImageUrl == "" {
		return nil, errors.New("抠图错误，image_url or imageUrl is required")
	}

	// 修复输入数据格式，确保包含节点期望的 req 键
	mcpReq := map[string]any{
		"req": map[string]any{
			"toolParams": map[string]any{
				"method": "tools/call",
				"params": map[string]any{
					"name": "remove_background",
					"arguments": map[string]any{
						"imgurl": inputImageUrl,
						"model":  "ben2",
					},
				},
			},
		},
	}

	output, err = lambda.McpLambdaFunc(ctx, mcpReq, opts...)
	if err != nil {
		return BuildErrorResponse("", input), err
	}

	// 安全地获取mask_url
	maskUrl := safeGetMaskUrl(output, input["image_url"], "图片抠图")
	imageUrl := safeGetImageUrl(output, input["image_url"], "图片抠图") // 抠完图后的

	output = input
	output["origin_url"] = inputImageUrl
	output["mask_url"] = maskUrl
	output["imageUrl"] = imageUrl
	output["imageUrl"] = imageUrl
	return
}

// ImageRemoveWatermarkMcpLambda 去除水印
func ImageRemoveWatermarkMcpLambda(ctx context.Context, input map[string]any, opts ...lambda.McpLambdaOption) (output map[string]any, err error) {
	inputImageUrl := ExtractStringFromInput(input, "image_url", "")
	if inputImageUrl == "" {
		inputImageUrl = ExtractStringFromInput(input, "imageUrl", "")
	}
	if inputImageUrl == "" {
		return nil, errors.New("抠图错误，image_url or imageUrl is required")
	}
	maskUrl := ExtractStringFromInput(input, "mask_url", "")
	var autoMask string
	if maskUrl == "" {
		maskUrl = ExtractStringFromInput(input, "maskUrl", "")
		autoMask = "yes"
	} else {
		autoMask = "no"
	}

	// 修复输入数据格式，确保包含节点期望的 req 键
	mcpReq := map[string]any{
		"req": map[string]any{
			"toolParams": map[string]any{
				"method": "tools/call",
				"params": map[string]any{
					"name": "ai_remove",
					"arguments": map[string]any{
						"orig_imgurl": inputImageUrl, // 使用动态获取的图片URL
						"mask_imgurl": maskUrl,
						"auto_mask":   autoMask, // 使用自动识别水印位置功能
					},
				},
			},
		},
	}

	mcpOutput := make(map[string]any)
	mcpOutput, err = lambda.McpLambdaFunc(ctx, mcpReq, opts...)
	if err != nil {
		return BuildErrorResponse("", input), err
	}
	// log.Printf("去除水印，mcpOutput: %v", output)

	output = input
	// 安全地获取处理后的image_url
	imageUrl := safeGetImageUrl(mcpOutput, input["image_url"], "去除水印")
	if imageUrl == "" {
		// TODO 继续走一下逻辑，返回原图URL，不影响流程先
		output["image_url"] = inputImageUrl
		output["imageUrl"] = inputImageUrl
		log.Printf("TODO 去除水印失败，获取不到处理的图片链接，响应信息：%v", output)
	} else {
		output["image_url"] = imageUrl
		output["imageUrl"] = imageUrl
	}
	// log.Printf("去除水印，imageUrl: %v", imageUrl)

	output["origin_url"] = inputImageUrl
	output["watermarkRemoved"] = true

	return
}

// ImageOcrTranslateMcpLambda 图片翻译
func ImageOcrTranslateMcpLambda(ctx context.Context, input map[string]any, opts ...lambda.McpLambdaOption) (output map[string]any, err error) {

	// 修复输入数据格式，确保包含节点期望的 req 键
	mcpReq := map[string]any{
		"req": map[string]any{
			"toolParams": map[string]any{
				"method": "tools/call",
				"params": map[string]any{
					"name": "translate_image",
					"arguments": map[string]any{
						"imgurl":                       input["image_url"], // 使用动态获取的图片URL
						"method":                       "aidc",             // 翻译方法
						"aidc_version":                 "pro",              //  aidc版本选择，"pro"为pro版本，"standard"为标准版本
						"language":                     "en",               // 目标语言
						"from_code":                    "auto",             // 源语言
						"including_product_area":       "false",            // 是否翻译产品区域
						"translating_brand_in_product": "false",            // 是否翻译品牌
					},
					"_meta": map[string]any{
						"progressToken": 18,
					},
				},
			},
		},
	}

	output, err = lambda.McpLambdaFunc(ctx, mcpReq, opts...)
	if err != nil {
		return BuildErrorResponse("", input), err
	}

	// 安全地获取处理后的image_url（使用专门的翻译图片URL提取函数）
	imageUrl := safeGetTranslateImageUrl(output, input["image_url"], "图片翻译")

	// 提取图层数据
	imageLayers, err := ExtractTranslateImageLayers(output)
	if err != nil && global.GVA_LOG != nil {
		global.GVA_LOG.Warn(fmt.Sprintf("图片翻译 - 提取图层数据失败: %v", err))
	}

	// 构建响应，包含图层数据
	extraFields := map[string]any{}
	if imageLayers != nil {
		extraFields["image_layers"] = imageLayers
	}

	output = BuildSuccessResponse(imageUrl, input, extraFields)
	return
}

// ImageWhiteBgMcpLambda 白底图生成
func ImageWhiteBgMcpLambda(ctx context.Context, input map[string]any, opts ...lambda.McpLambdaOption) (output map[string]any, err error) {

	inputImageUrl := ExtractStringFromInput(input, "image_url", "")
	if inputImageUrl == "" {
		inputImageUrl = ExtractStringFromInput(input, "imageUrl", "")
	}
	if inputImageUrl == "" {
		return nil, errors.New("白底图生成错误，image_url is required")
	}

	// 修复输入数据格式，确保包含节点期望的 req 键
	mcpReq := map[string]any{
		"req": map[string]any{
			"toolParams": map[string]any{
				"method": "tools/call",
				"params": map[string]any{
					"name": "white_image",
					"arguments": map[string]any{
						"imgurl":       inputImageUrl,
						"canvas_size":  "(1001,1001)",
						"target_ratio": 1,
					},
					"_meta": map[string]any{
						"progressToken": 17,
					},
				},
			},
		},
	}

	output, err = lambda.McpLambdaFunc(ctx, mcpReq, opts...)
	if err != nil {
		return BuildErrorResponse("", input), err
	}

	// 安全地获取处理后的image_url
	imageUrl := safeGetImageUrl(output, input["image_url"], "白底图生成")
	if imageUrl == "" {
		return output, fmt.Errorf("白底图生成错误，无法获取处理后的图片URL,MCP响应信息：%v", output)
	}

	output = input
	output["origin_url"] = inputImageUrl
	output["image_url"] = imageUrl
	output["imageUrl"] = imageUrl
	return
}

// safeGetExpansionImageUrl 专门用于AI扩图的image_url提取
func safeGetExpansionImageUrl(output map[string]any, originalUrl any, operation string) string {
	extractor := NewNestedMapExtractor(output)
	// AI扩图工具返回的是一个URL数组，路径为 result.data.image_urls
	urls, ok := extractor.GetArray("result.data.image_urls")
	if ok && len(urls) > 0 {
		// 确保数组中的第一个元素是字符串
		if urlStr, isString := urls[0].(string); isString {
			return urlStr
		}
	}

	// 如果提取失败或数组为空，记录日志并返回原图
	outputJson, _ := json.Marshal(output)
	if global.GVA_LOG != nil {
		global.GVA_LOG.Error(fmt.Sprintf("%s - 无法从返回结果中获取image_urls或数组为空", operation),
			zap.String("完整返回数据", string(outputJson)),
			zap.Any("原始URL", originalUrl))
	}

	if originalUrlStr, ok := originalUrl.(string); ok {
		return originalUrlStr
	}
	return ""
}

// ImageExpansionMcpLambda AI扩图
func ImageExpansionMcpLambda(ctx context.Context, input map[string]any, opts ...lambda.McpLambdaOption) (output map[string]any, err error) {
	// 从输入中提取可选参数，如果不存在则使用默认值
	// 参考文档：扩图工具说明.md
	size := ExtractStringFromInput(input, "size", "(1001,1001)")
	padding := ExtractStringFromInput(input, "padding", "(0,0,0,0)")
	prompt := ExtractStringFromInput(input, "prompt", "")
	whitePadding := ExtractBoolFromInput(input, "white_padding", true)
	outputNum := ExtractIntFromInput(input, "output_num", 1)

	// 构建MCP工具调用请求
	mcpReq := map[string]any{
		"req": map[string]any{
			"toolParams": map[string]any{
				"method": "tools/call",
				"params": map[string]any{
					"name": "ai_image_expansion",
					"arguments": map[string]any{
						"imgurl":        input["image_url"],
						"size":          size,
						"padding":       padding,
						"prompt":        prompt,
						"white_padding": whitePadding,
						"output_num":    outputNum,
					},
				},
			},
		},
	}

	// 调用通用MCP Lambda函数，但不传递外部opts，强制使用默认执行器
	output, err = lambda.McpLambdaFunc(ctx, mcpReq)
	if err != nil {
		return BuildErrorResponse("", input), err
	}

	// 使用专门为扩图工具定制的URL提取函数
	imageUrl := safeGetExpansionImageUrl(output, input["image_url"], "AI扩图")

	output = BuildSuccessResponse(imageUrl, input)
	return
}

// ImageOcrTextExtractionMcpLambda 抠出图片文字 - OCR文字识别
func ImageOcrTextExtractionMcpLambda(ctx context.Context, input map[string]any, opts ...lambda.McpLambdaOption) (output map[string]any, err error) {

	// 获取图片URL，支持imageUrl和image_url两种格式
	imageUrl := ExtractStringFromInput(input, "imageUrl", "")

	// 修复输入数据格式，确保包含节点期望的 req 键
	mcpReq := map[string]any{
		"req": map[string]any{
			"toolParams": map[string]any{
				"method": "tools/call",
				"params": map[string]any{
					"name": "recognize_image_text",
					"arguments": map[string]any{
						"imgurl": imageUrl, // 使用提取到的图片URL
					},
				},
			},
		},
	}

	// 创建MCP服务配置，指定正确的服务地址
	mcpOpts := []lambda.McpLambdaOption{
		{ServerURL: "http://mcp-ai-draw-image.gaia.svc.yafex.io:3000/mcp"},
	}

	mcpOutput, err := lambda.McpLambdaFunc(ctx, mcpReq, mcpOpts...)
	if err != nil {
		return input, err // 出错时直接返回原input
	}

	// 提取文本数组
	output = map[string]any{}
	var texts []string

	data := mcpOutput["result"].(map[string]any)["data"]
	result := make(map[string]any)
	ok := false
	if result, ok = data.(map[string]any); !ok {
		return input, fmt.Errorf("调用抠出图片文字MCP服务失败，响应信息为: %v", mcpOutput)
	}
	if data, ok := result["data"].(map[string]any); ok {
		if textArray, ok := data["text"].([]any); ok {
			// 遍历数组，提取每个文本对象的text字段
			for _, item := range textArray {
				if textObj, ok := item.(map[string]any); ok {
					if text, ok := textObj["text"].(string); ok && text != "" {
						texts = append(texts, text)
					}
				}
			}
		}
	} else {
		return input, fmt.Errorf("调用抠出图片文字MCP服务失败，响应信息为: %v", mcpOutput)
	}
	output = input
	output["texts"] = texts
	output["extractedText"] = texts // TODO 用于1688自动化回调数据
	return output, nil
}

// ImageResolutionCheckMcpLambda 调用分辨率mcp接口
func ImageResolutionCheckMcpLambda(ctx context.Context, input map[string]any, opts ...lambda.McpLambdaOption) (output map[string]any, err error) {

	// 获取图片URL，支持多种格式
	imageUrl := ExtractStringFromInput(input, "imageUrl", "")
	minResolution := ExtractIntFromInput(input, "minResolution", -1)

	// 分辨率限制为-1，说明拿不到minResolution，则没有限制，则不进行mcp调用，直接返回true
	if minResolution == -1 {
		output = input
		output["is_satisfied"] = true
		return output, nil
	}

	// 分辨率限制为0，说明直接过滤掉，则不进行mcp调用，直接返回false
	if minResolution == 0 {
		output = input
		output["is_satisfied"] = false
		return output, nil
	}

	// 修复输入数据格式，确保包含节点期望的 req 键
	mcpReq := map[string]any{
		"req": map[string]any{
			"toolParams": map[string]any{
				"method": "tools/call",
				"params": map[string]any{
					"name": "check_image_resolution",
					"arguments": map[string]any{
						"image_url":      imageUrl,      // 使用提取到的图片URL
						"min_resolution": minResolution, // 最小分辨率要求
					},
				},
			},
		},
	}

	// 创建MCP服务配置，指定正确的服务地址
	mcpOpts := []lambda.McpLambdaOption{
		{ServerURL: "http://mcp-ai-draw-image.gaia.svc.yafex.io:3000/mcp"},
	}

	mcpOutput, err := lambda.McpLambdaFunc(ctx, mcpReq, mcpOpts...)
	if err != nil {
		fmt.Printf("❌ ImageResolutionCheckMcpLambda - MCP调用失败: %v\n", err)
		return input, err // 出错时直接返回原input
	}

	// 提取 is_satisfied 和 image_resolution 字段
	output = input
	data := mcpOutput["result"].(map[string]any)["data"]
	if result, ok := data.(map[string]any); ok {
		if data, ok := result["data"].(map[string]any); ok {
			// 提取 is_satisfied 字段
			if isSatisfied, exists := data["is_satisfied"]; exists {
				output["is_satisfied"] = isSatisfied
			} else {
				return input, fmt.Errorf("is_satisfied is required,mcpOutput: %v", mcpOutput)
			}

			// 提取 image_resolution 字段
			if imageResolution, exists := data["image_resolution"]; exists {
				output["image_resolution"] = imageResolution
			}
		}
	}

	return output, nil
}

// ImageTextRemovalMcpLambda 非主图的去除文字（直接用aidge翻译处理后 ai_remove_imgurl替换image_url）
func ImageTextRemovalMcpLambda(ctx context.Context, input map[string]any, opts ...lambda.McpLambdaOption) (output map[string]any, err error) {

	aiRemoveImgurl := ExtractStringFromInput(input, "ai_remove_imgurl", "")
	if aiRemoveImgurl == "" {
		return nil, fmt.Errorf("ai_remove_imgurl is required")
	}
	input["image_url"] = aiRemoveImgurl
	input["imageUrl"] = aiRemoveImgurl

	return input, nil
}

// ImageCropMcpLambda 调用裁剪接口
func ImageCropMcpLambda(ctx context.Context, input map[string]any, opts ...lambda.McpLambdaOption) (output map[string]any, err error) {
	// 从输入中提取图片URL，支持image_url和imageUrl两种格式
	inputImageUrl := ExtractStringFromInput(input, "image_url", "")
	if inputImageUrl == "" {
		inputImageUrl = ExtractStringFromInput(input, "imageUrl", "")
	}

	if inputImageUrl == "" {
		return nil, fmt.Errorf("image_url is required")
	}

	// 构建MCP工具调用请求
	mcpReq := map[string]any{
		"req": map[string]any{
			"toolParams": map[string]any{
				"method": "tools/call",
				"params": map[string]any{
					"name": "ai_crop_image",
					"arguments": map[string]any{
						"imgurl":           inputImageUrl,
						"target_size":      "(1001,1001)",
						"crop_coords":      "",
						"detect_products":  false,
						"detect_expansion": false,
					},
				},
			},
		},
	}

	// 调用MCP Lambda函数（使用默认的ai-draw服务）
	mcpOutput, err := lambda.McpLambdaFunc(ctx, mcpReq)
	if err != nil {
		return nil, fmt.Errorf("AI图片裁剪失败，MCP接口调用失败: %v", err)
	}

	// 解析返回结果
	output = input // 继承输入数据
	output["image_url"] = ""
	output["imageUrl"] = ""
	// 安全地提取返回数据 - ai_crop_image返回格式与其他ai-draw工具一致
	data := mcpOutput["result"].(map[string]any)["data"]
	if result, ok := data.(map[string]any); ok {
		if success, ok := result["success"].(bool); ok && success {
			if data, ok := result["data"].(map[string]any); ok {
				// 提取裁剪后的图片URL
				if croppedImageUrl, ok := data["image_url"].(string); ok && croppedImageUrl != "" {
					output["image_url"] = croppedImageUrl
					output["imageUrl"] = croppedImageUrl
				}
			}
		}
	}
	log.Printf("AI图片裁剪，imageUrl: %v", output["imageUrl"])

	// 验证是否成功获取裁剪后的图片URL
	if output["image_url"] == "" || output["imageUrl"] == "" {
		return nil, fmt.Errorf("AI图片裁剪失败，获取不到处理后的图片链接，响应信息：%v", mcpOutput)
	}

	return output, nil
}

// ImageOcrMaskMcpLambda 调用OCR文字遮罩生成接口
func ImageOcrMaskMcpLambda(ctx context.Context, input map[string]any, opts ...lambda.McpLambdaOption) (output map[string]any, err error) {
	// 从输入中提取图片URL，支持image_url和imageUrl两种格式
	imageUrl := ExtractStringFromInput(input, "image_url", "")
	if imageUrl == "" {
		imageUrl = ExtractStringFromInput(input, "imageUrl", "")
	}

	if imageUrl == "" {
		return nil, fmt.Errorf("image_url is required")
	}

	// 构建MCP工具调用请求
	mcpReq := map[string]any{
		"req": map[string]any{
			"toolParams": map[string]any{
				"method": "tools/call",
				"params": map[string]any{
					"name": "ocr_mask",
					"arguments": map[string]any{
						"imgurl": imageUrl,
					},
				},
			},
		},
	}

	// 调用MCP Lambda函数
	mcpOutput, err := lambda.McpLambdaFunc(ctx, mcpReq)
	if err != nil {
		return nil, fmt.Errorf("OCR文字遮罩生成失败，MCP接口调用失败: %v", err)
	}

	// 解析返回结果
	output = input // 继承输入数据
	output["mask_url"] = ""

	// 安全地提取返回数据 - ocr_mask返回格式与其他工具略有不同
	data := mcpOutput["result"].(map[string]any)["data"]
	if result, ok := data.(map[string]any); ok {
		if success, ok := result["success"].(bool); ok && success {
			if data, ok := result["data"].(map[string]any); ok {
				// 提取遮罩图片URL
				if maskUrl, ok := data["mask_url"].(string); ok && maskUrl != "" {
					output["mask_url"] = maskUrl
				}
			}
		}
	}

	if output["mask_url"] == "" {
		log.Printf("OCR文字遮罩生成失败，获取不到处理后的图片链接，响应信息：%v", mcpOutput)
	}

	// log.Printf("OCR文字遮罩生成，mask_url: %v", output["mask_url"])
	return output, nil
}
