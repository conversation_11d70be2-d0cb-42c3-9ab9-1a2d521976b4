package lambda

import (
	"context"
	"fmt"
	"log"
	"strings"

	"github.com/flipped-aurora/gin-vue-admin/server/pkg/einoext/components/lambda"
)

// ImageMarketingWordCheckMcpLambda 调用营销词mcp接口
func ImageMarketingWordCheckMcpLambda(ctx context.Context, input map[string]any, opts ...lambda.McpLambdaOption) (output map[string]any, err error) {
	// 从输入中提取文本数组
	var marketingWords []string
	if textsAny, exists := input["texts"]; exists && textsAny != nil {
		// 安全地转换文本数组
		if textsSlice, ok := textsAny.([]any); ok {
			for _, item := range textsSlice {
				if text, ok := item.(string); ok && text != "" {
					marketingWords = append(marketingWords, text)
				}
			}
		} else if textsStringSlice, ok := textsAny.([]string); ok {
			marketingWords = textsStringSlice
		}
	}

	// 说明前面没有扣到文字，直接返回
	if len(marketingWords) == 0 {
		output = input // 继承输入数据
		output["has_marketing_words"] = false
		output["sensitiveWords"] = []string{}
		log.Printf("营销词检测，检测通过：这张图片没有文字，直接返回")
		return output, nil
	}
	// ["夏季新款","纯棉T恤","男士休闲","短裤","宽松"]
	//marketingWords = []string{"夏季新款", "纯棉T恤", "男士休闲", "短裤", "宽松"}

	// 构建MCP工具调用请求
	mcpReq := map[string]any{
		"req": map[string]any{
			"toolParams": map[string]any{
				"method": "tools/call",
				"params": map[string]any{
					"name": "check_marketing_words_sensitivity",
					"arguments": map[string]any{
						"marketing_words": marketingWords,
					},
				},
			},
		},
	}

	// 创建MCP服务配置，指定compliance检查服务地址
	// TODO 测试环境和正式环境mcp服务地址区分
	mcpOpts := []lambda.McpLambdaOption{
		{ServerURL: "http://mcp-compliance-check.dev.yafex.cn/mcp/"},
	}

	// 调用MCP Lambda函数
	mcpOutput, err := lambda.McpLambdaFunc(ctx, mcpReq, mcpOpts...)
	if err != nil {
		return nil, fmt.Errorf("营销词检测失败，MCP接口调用失败: %v", err)
	}

	// 解析返回结果
	output = input // 继承输入数据

	// 安全地提取返回数据
	output["sensitiveWords"] = []string{}
	mcpOutputData := mcpOutput["result"].(map[string]any)["data"]
	if result, ok := mcpOutputData.(map[string]any); ok {
		if data, ok := result["data"].(map[string]any); ok {
			if sensitiveWords, ok := data["sensitiveWords"].([]any); ok {
				output["sensitiveWords"] = sensitiveWords
			}
		}
	}

	// 设置检测状态信息
	if len(output["sensitiveWords"].([]any)) > 0 {
		output["has_marketing_words"] = true
		output["marketingFilter"] = fmt.Sprintf("【%v】进行检测，含有敏感词：%v", marketingWords, output["sensitiveWords"]) // TODO 用于1688自动化回调数据
	} else {
		output["has_marketing_words"] = false
		output["marketingFilter"] = "" // TODO 用于1688自动化回调数据
	}

	return output, nil
}

// ImageCopyrightCheckMcpLambda 调用侵权检测接口
func ImageCopyrightCheckMcpLambda(ctx context.Context, input map[string]any, opts ...lambda.McpLambdaOption) (output map[string]any, err error) {
	description := ""
	if textsAny, exists := input["content"]; exists && textsAny != nil {
		// 安全地转换文本数组
		if textsStringSlice, ok := textsAny.([]string); ok {
			description = strings.Join(textsStringSlice, ",")
		} else if textsSlice, ok := textsAny.([]any); ok {
			for _, item := range textsSlice {
				if text, ok := item.(string); ok && text != "" {
					description += text
				}
			}
		}
	}

	output = input // 继承输入数据

	if description == "" {
		log.Printf("获取不到翻译后的文字，侵权直接标志为未侵权，信息为%v", input)
		output["violation_details"] = make([]string, 0)
		output["is_copyright"] = false
		return output, nil
	}
	log.Printf("侵权检测，description: %v", description)

	// 从输入中提取商品描述
	languageId := ExtractStringFromInput(input, "languageId", "2")
	platSiteMap := ExtractStringFromInput(input, "platSiteMap", "{\"1\": \"1,2,3,4\", \"2\": \"5,6,7\"}")

	// 构建MCP工具调用请求
	mcpReq := map[string]any{
		"req": map[string]any{
			"toolParams": map[string]any{
				"method": "tools/call",
				"params": map[string]any{
					"name": "check_tort_violation",
					"arguments": map[string]any{
						"description":   description,
						"language_id":   languageId,
						"plat_site_map": platSiteMap,
					},
				},
			},
		},
	}

	// 创建MCP服务配置，指定compliance检查服务地址
	mcpOpts := []lambda.McpLambdaOption{
		{ServerURL: "http://mcp-compliance-check.gaia.svc.yafex.io:3000/mcp"},
	}

	// 调用MCP Lambda函数
	mcpOutput, err := lambda.McpLambdaFunc(ctx, mcpReq, mcpOpts...)
	if err != nil {
		return mcpOutput, err
	}

	// 默认设置为侵权
	output["is_copyright"] = true

	// 安全地提取返回数据
	data := mcpOutput["result"].(map[string]any)["data"]
	if result, ok := data.(map[string]any); ok {
		if data, ok := result["data"].(map[string]any); ok {
			// 检查业务返回码
			if ret, ok := data["ret"].(float64); ok && ret == 1 {
				// 提取侵权数据数组
				if violationData, ok := data["data"].([]any); ok {
					// 当data数组长度大于0时，说明侵权了
					if len(violationData) > 0 {
						output["is_copyright"] = true
						// 可选：保存具体的侵权详情
						output["violation_details"] = violationData
					} else {
						output["is_copyright"] = false
					}
				}
			}
		}
	}
	if output["is_copyright"].(bool) {
		log.Printf("侵权，检测词：%v", description)
	} else {
		log.Printf("不侵权，检测词：%v", description)
	}

	return output, nil
}
