package lambda

import (
	"context"
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/pkg/einoext/components/lambda"
)

// ImageRemoveLogoMcpLambda 调用去除logo mcp接口
func ImageRemoveLogoMcpLambda(ctx context.Context, input map[string]any, opts ...lambda.McpLambdaOption) (output map[string]any, err error) {
	// 从输入中提取图片URL，支持image_url和imageUrl两种格式
	inputImageUrl := ExtractStringFromInput(input, "image_url", "")
	if inputImageUrl == "" {
		inputImageUrl = ExtractStringFromInput(input, "imageUrl", "")
	}

	if inputImageUrl == "" {
		return nil, fmt.Errorf("image_url is required")
	}

	// 构建MCP工具调用请求
	mcpReq := map[string]any{
		"req": map[string]any{
			"toolParams": map[string]any{
				"method": "tools/call",
				"params": map[string]any{
					"name": "intelligent_image_removal",
					"arguments": map[string]any{
						"image_url":                  inputImageUrl,
						"object_remove_elements":     []int{2}, // 固定为2，去除LOGO
						"non_object_remove_elements": nil,
						"mask":                       nil,
					},
				},
			},
		},
	}

	// 创建MCP服务配置，指定aidge服务地址
	mcpOpts := []lambda.McpLambdaOption{
		{ServerURL: "http://mcp-aidge.gaia.svc.yafex.io:3000/mcp"},
	}

	// 调用MCP Lambda函数
	mcpOutput, err := lambda.McpLambdaFunc(ctx, mcpReq, mcpOpts...)
	if err != nil {
		return nil, fmt.Errorf("LOGO去除失败，MCP接口调用失败: %v", err)
	}

	//log.Printf("LOGO去除成功，返回结果: %v", mcpOutput)
	// 解析返回结果
	output = input
	data := mcpOutput["result"].(map[string]any)["data"]
	if result, ok := data.(map[string]any); ok {
		if data, ok := result["data"].(map[string]any); ok {
			// 提取 image_url 字段
			if imageUrl, exists := data["image_url"].(string); exists {
				output["image_url"] = imageUrl
				output["imageUrl"] = imageUrl
			}
		}
	}
	//log.Printf("LOGO去除成功，imageUrl: %v", output["image_url"])
	output["logoRemoved"] = true

	return output, nil
}

// ImageTextTranslateMcpLambda 文字中译英
func ImageTextTranslateMcpLambda(ctx context.Context, input map[string]any, opts ...lambda.McpLambdaOption) (output map[string]any, err error) {
	// 从输入中提取文本数组
	var texts []string
	if textsAny, exists := input["texts"]; exists && textsAny != nil {
		// 安全地转换文本数组
		if textsSlice, ok := textsAny.([]any); ok {
			for _, item := range textsSlice {
				if text, ok := item.(string); ok && text != "" {
					texts = append(texts, text)
				}
			}
		} else if textsStringSlice, ok := textsAny.([]string); ok {
			texts = textsStringSlice
		}
	}

	if len(texts) == 0 {
		return nil, fmt.Errorf("缺少待翻译文本，无法进行翻译")
	}

	// 构建MCP工具调用请求
	mcpReq := map[string]any{
		"req": map[string]any{
			"toolParams": map[string]any{
				"method": "tools/call",
				"params": map[string]any{
					"name": "translate_text_marco",
					"arguments": map[string]any{
						"text_string":     "",
						"text_list":       texts,
						"target_language": "en", // 固定中文到英文
						"source_language": "zh",
						"format_type":     "text",
						"glossary":        "",
					},
				},
			},
		},
	}

	// 创建MCP服务配置，指定aidge服务地址
	mcpOpts := []lambda.McpLambdaOption{
		{ServerURL: "http://mcp-aidge.gaia.svc.yafex.io:3000/mcp"},
	}

	// 调用MCP Lambda函数
	mcpOutput, err := lambda.McpLambdaFunc(ctx, mcpReq, mcpOpts...)
	if err != nil {
		return nil, fmt.Errorf("文字翻译失败，MCP接口调用失败: %v", err)
	}
	//log.Printf("文字中译英，mcpOutput: %v", mcpOutput)

	// 解析返回结果
	output = input
	data := mcpOutput["result"].(map[string]any)["data"]
	if result, ok := data.(map[string]any); ok {
		if data, ok := result["data"].(map[string]any); ok {
			// 提取 text_string 字段
			if translationList, exists := data["translation_list"]; exists {
				output["translation_list"] = translationList
			}
			// 提取 text_list 字段
			if translationText, exists := data["translation_text"]; exists {
				output["translation_text"] = translationText
			}
		}
	}
	//log.Printf("文字中译英，translation_list: %v", output["translation_list"])
	//log.Printf("文字中译英，translation_text: %v", output["translation_text"])

	return output, nil
}

// ImageAidgeOcrTranslateMcpLambda aidge图片翻译
func ImageAidgeOcrTranslateMcpLambda(ctx context.Context, input map[string]any, opts ...lambda.McpLambdaOption) (output map[string]any, err error) {

	// 从输入中提取图片URL，支持image_url和imageUrl两种格式
	inputImageUrl := ExtractStringFromInput(input, "image_url", "")
	if inputImageUrl == "" {
		inputImageUrl = ExtractStringFromInput(input, "imageUrl", "")
	}

	if inputImageUrl == "" {
		return nil, fmt.Errorf("image_url is required")
	}

	// 修复输入数据格式，确保包含节点期望的 req 键
	mcpReq := map[string]any{
		"req": map[string]any{
			"toolParams": map[string]any{
				"method": "tools/call",
				"params": map[string]any{
					"name": "translate_image_aidc",
					"arguments": map[string]any{
						"imgurl":                       inputImageUrl, // 使用动态获取的图片URL
						"method":                       "aidc",        // 翻译方法
						"aidc_version":                 "pro",         //  aidc版本选择，"pro"为pro版本，"standard"为标准版本
						"language":                     "en",          // 目标语言
						"from_code":                    "zh",          // 源语言
						"including_product_area":       "false",       // 是否翻译产品区域
						"translating_brand_in_product": "true",        // 是否翻译品牌
					},
					"_meta": map[string]any{
						"progressToken": 18,
					},
				},
			},
		},
	}

	// 创建MCP服务配置，指定aidge服务地址
	mcpOpts := []lambda.McpLambdaOption{
		{ServerURL: "http://mcp-aidge.gaia.svc.yafex.io:3000/mcp"},
	}

	mcpOutput, err := lambda.McpLambdaFunc(ctx, mcpReq, mcpOpts...)
	if err != nil {
		return nil, fmt.Errorf("图片翻译失败，MCP接口调用失败: %v", err)
	}

	// 安全地获取处理后的image_url（使用专门的翻译图片URL提取函数）
	imageUrl := safeGetTranslateImageUrl(mcpOutput, input["image_url"], "图片翻译")

	output = input
	data := mcpOutput["result"].(map[string]any)["data"]
	if result, ok := data.(map[string]any); ok {
		if data, ok := result["data"].(map[string]any); ok {
			// 提取 content 字段
			if content, exists := data["content"]; exists {
				output["content"] = content
			}
			// 提取 ai_remove_imgurl 字段
			if aiRemoveImgurl, exists := data["ai_remove_imgurl"]; exists {
				output["ai_remove_imgurl"] = aiRemoveImgurl
			}
		}
	}
	if imageUrl == "" {
		output["image_url"] = inputImageUrl
		output["imageUrl"] = inputImageUrl
	} else {
		output["image_url"] = imageUrl
		output["imageUrl"] = imageUrl
	}
	//log.Printf("aidge图片翻译，content: %v", output["content"])
	//log.Printf("aidge图片翻译，ai_remove_imgurl: %v", output["ai_remove_imgurl"])
	//log.Printf("aidge图片翻译，imageUrl: %v", imageUrl)
	return output, nil
}
