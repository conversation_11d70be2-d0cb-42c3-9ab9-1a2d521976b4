package lambda

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/service/pdm"
	"go.uber.org/zap"
)

// UploadPicToImgLambdaOption 选项类型
type UploadPicToImgLambdaOption struct{}

// UploadPicToImgLambda 上传图片到图片服务器
func UploadPicToImgLambda(ctx context.Context, input map[string]any, opts ...UploadPicToImgLambdaOption) (output map[string]any, err error) {

	imageURL, ok := input["imageUrl"].(string)
	if !ok {
		if imageURL, ok := input["image_url"].(string); ok {
			imageURL = imageURL
		} else {
			return nil, fmt.Errorf("imageUrl 不是字符串类型")
		}
	}

	spu, ok := input["spu"].(string)
	if !ok {
		spuFloat, ok := input["spu"].(float64)
		if !ok {
			return nil, fmt.Errorf("spu 不是字符串类型或数字类型")
		}
		spu = strconv.Itoa(int(spuFloat))
	}

	newImageURL, err := uploadPicToImg(ctx, spu, imageURL)
	if err != nil {
		return nil, err
	}

	output = input
	output["imageUrl"] = newImageURL
	output["image_url"] = newImageURL
	return
}

// uploadPicToImg 上传图片到图片服务器
func uploadPicToImg(ctx context.Context, spu string, imageURL string) (string, error) {
	// 构建图片路径 - 按照规范：年/月/日/spu/original/aiUpload
	now := time.Now()
	imagePath := fmt.Sprintf("%d/%02d/%02d/%v/original/",
		now.Year(), now.Month(), now.Day(), spu)

	// 获取图片URL
	if imageURL == "" {
		return "", fmt.Errorf("无法获取有效的图片URL")
	}

	// 构建上传请求
	uploadReq := &pdm.ImageUploadRequest{
		LayuiUpload: "true",
		SourceCode:  "ipds",
		Path:        imagePath,
		FilePath:    imageURL, // 直接使用URL，让上传服务处理下载
	}

	// 创建图片上传服务
	imageUploadService := &pdm.ImageUploadService{}

	// 上传图片
	uploadResp, err := imageUploadService.UploadImage(uploadReq)
	if err != nil {
		logError("上传图片到服务器失败",
			zap.String("spu", spu),
			zap.String("imageUrl", imageURL),
			zap.Error(err),
		)
		return "", err
	}

	// 构建完整图片URL
	fullImageURL := imageUploadService.GetFullImageURL(uploadResp.Info)

	return fullImageURL, nil
}
