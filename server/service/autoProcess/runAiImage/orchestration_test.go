package runAiImage

import (
	"context"
	"flag"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"testing"

	"github.com/go-resty/resty/v2"
	"github.com/redis/go-redis/v9"

	"github.com/flipped-aurora/gin-vue-admin/server/model/autoProcess"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"

	"github.com/flipped-aurora/gin-vue-admin/server/config"
	"github.com/flipped-aurora/gin-vue-admin/server/global"

	"time"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"

	"github.com/flipped-aurora/gin-vue-admin/server/api/external"
	"github.com/flipped-aurora/gin-vue-admin/server/api/external/ai_draw"
	"github.com/flipped-aurora/gin-vue-admin/server/api/external/pdm"

	"math/rand"
)

// 命令行参数定义
var (
	taskId   = flag.Uint("task_id", 1, "task id for test")
	username = flag.String("username", "maiyouming", "用户名，用于分配任务")
	testEnv  = flag.String("env", "local", "运行环境，可选值: local(本地), dev(开发环境), prod(正式环境)")
	spu      = flag.String("spu", "", "SPU编码，用于指定特定图片任务")
)

// getTestConfig 根据环境获取数据库配置
func getTestConfig(env string) config.Mysql {
	switch env {
	case "dev":
		// 开发环境配置
		return config.Mysql{
			GeneralDB: config.GeneralDB{
				Prefix:       "",
				Port:         "3306",
				Config:       "charset=utf8mb4&parseTime=True&loc=Local",
				Dbname:       "gaia",                // 开发环境数据库
				Username:     "gaia",                // 开发环境用户名
				Password:     "gaia123*",            // 开发环境密码
				Path:         "gaiadb.dev.yafex.cn", // 开发环境主机
				Engine:       "",
				LogMode:      "",
				MaxIdleConns: 0,
				MaxOpenConns: 0,
				Singular:     false,
				LogZap:       false,
			},
		}
	case "local":
		// 开发环境配置
		return config.Mysql{
			GeneralDB: config.GeneralDB{
				Prefix:       "",
				Port:         "3306",
				Config:       "charset=utf8mb4&parseTime=True&loc=Local",
				Dbname:       "gaia_local",                // 开发环境数据库
				Username:     "gaia",                // 开发环境用户名
				Password:     "gaia123*",            // 开发环境密码
				Path:         "gaiadb.dev.yafex.cn", // 开发环境主机
				Engine:       "",
				LogMode:      "",
				MaxIdleConns: 0,
				MaxOpenConns: 0,
				Singular:     false,
				LogZap:       false,
			},
		}
	default:
		// 本地环境配置（默认）
		return config.Mysql{
			GeneralDB: config.GeneralDB{
				Prefix:       "",
				Port:         "3306",
				Config:       "charset=utf8mb4&parseTime=True&loc=Local",
				Dbname:       "gaia-x-admin", // 本地数据库
				Username:     "root",         // 本地用户名
				Password:     "123456",       // 本地密码
				Path:         "127.0.0.1",    // 本地主机
				Engine:       "",
				LogMode:      "",
				MaxIdleConns: 0,
				MaxOpenConns: 0,
				Singular:     false,
				LogZap:       false,
			},
		}
	}
}

// getRedisConfig 根据环境获取Redis配置
func getRedisConfig(env string) *redis.Options {
	switch env {
	case "dev":
		// 开发环境Redis配置
		return &redis.Options{
			Addr:     "redis-gaia.dev.svc.yafex.test:6379", // 开发环境Redis地址
			Password: "yafex",                              // 开发环境Redis密码
			DB:       9,
		}
	case "local":
		fallthrough
	default:
		// 本地环境Redis配置（默认）
		return &redis.Options{
			Addr:     "127.0.0.1:6379", // 本地Redis地址
			Password: "",               // 本地Redis无密码
			DB:       0,
		}
	}
}

// initTestEnvironment 初始化测试环境
func initTestEnvironment() error {
	var err error

	global.GVA_CONFIG.System.Environment = "test"

	// 确保flag已解析
	if !flag.Parsed() {
		flag.Parse()
	}

	// 初始化数据库连接
	if global.GVA_DB == nil {
		// 根据环境选择配置
		m := getTestConfig(*testEnv)

		mysqlConfig := mysql.Config{
			DSN:                       m.Dsn(), // DSN data source name
			DefaultStringSize:         191,     // string 类型字段的默认长度
			SkipInitializeWithVersion: false,   // 根据版本自动配置
		}
		if global.GVA_DB, err = gorm.Open(mysql.New(mysqlConfig)); err != nil {
			return fmt.Errorf("数据库连接失败: %v", err)
		}

		fmt.Printf("【测试配置】数据库连接成功: 环境=%s, 主机=%s, 数据库=%s\n", *testEnv, m.Path, m.Dbname)
	}

	// 初始化Redis连接
	if global.GVA_REDIS == nil {
		// 根据环境选择Redis配置
		redisOpts := getRedisConfig(*testEnv)
		client := redis.NewClient(redisOpts)
		global.GVA_REDIS = client

		fmt.Printf("【测试配置】Redis连接成功: 环境=%s, 地址=%s\n", *testEnv, redisOpts.Addr)
	}

	return nil
}

func TestRunAiImageGraph(t *testing.T) {
	// 初始化测试环境
	if err := initTestEnvironment(); err != nil {
		t.Fatalf("初始化测试环境失败: %v", err)
	}

	type args struct {
		ctx          context.Context
		inputData    map[string]any
		checkpointID string
		currentState *GraphState
	}

	checkpointId := "test_checkpoint_666"
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "测试AI图像处理流程中断",
			args: args{
				ctx: context.Background(),
				inputData: map[string]any{
					"method": "tools/call",
					"params": map[string]any{
						"name": "remove_background",
						"arguments": map[string]any{
							"imgurl": "http://img.dev.yafex.cn/upload/product/2025/05/14/UUU0184619/original/aiUpload/3860481d6105104646d3916d1f3f5460.png",
							"model":  "ben2",
						},
					},
					"good": map[string]any{
						"id": "1",
					},
				},
				checkpointID: checkpointId,
				currentState: &GraphState{
					Index:         0,
					WorkflowRunId: 1,
					WorkflowId:    1,
				},
			},
			wantErr: false, // 中断是正常情况，不应该视为错误
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			_, err := RunAiImageGraph(tt.args.ctx, tt.args.inputData, tt.args.checkpointID, tt.args.currentState)

			if (err != nil) != tt.wantErr {
				t.Errorf("RunAiImageGraph() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			// 不检查具体的返回值，因为它可能是中断结果或正常结果
			if tt.wantErr && err == nil {
				t.Errorf("RunAiImageGraph() expected error but got none")
			}
		})
	}
}

func TestRunTask(t *testing.T) {
	// 初始化测试环境
	if err := initTestEnvironment(); err != nil {
		t.Fatalf("初始化测试环境失败: %v", err)
	}

	type args struct {
		ctx           context.Context
		taskId        autoProcess.TaskId
		userInputData map[string]any
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "测试执行任务（包含确认操作）",
			args: args{
				ctx:    context.Background(),
				taskId: autoProcess.TaskId(*taskId),
				userInputData: map[string]any{
					"image_url": "https://ai-draw.yafex.cn/static/images/2025/06/04/6694c5c6_%E9%80%8F%E6%98%8E%E5%9B%BE.png",
				},
			},
			wantErr: true, // 因为测试环境中可能没有对应的任务记录
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := RunTask(tt.args.ctx, tt.args.taskId, tt.args.userInputData); (err != nil) != tt.wantErr {
				t.Errorf("RunTask() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestRunWorkflow(t *testing.T) {
	// 初始化测试环境
	if err := initTestEnvironment(); err != nil {
		t.Fatalf("初始化测试环境失败: %v", err)
	}

	type args struct {
		ctx        context.Context
		userID     uint
		workflowId autoProcess.WorkflowId
		inputData  map[string]any
		taskUserID uint
	}
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "非主图",
			args: args{
				ctx:        context.Background(),
				userID:     1,
				workflowId: 128,
				inputData: map[string]any{
							"unqId":     "ed316bd06e01526ac23e5d14d2f77fd4",
							"imageUrl":  "https://cbu01.alicdn.com/img/ibank/O1CN0129SEoI1MsT5wf7EFa_!!*************-0-cib.jpg",
					"picId":      "214258eae234c71240b2b7432b600ea2",
					"isSku":      false,
							"skuNumber": "SKU003",
					"languageId": 2, // 用于侵权检测
					"platSiteMap": map[string][]string{ // 用于侵权检测
								"1": {"0", "3"},
								"2": {"1", "2", "14"},
							},
					"isRemoveWatermark": false,
					"isRemoveLogo":      false,
					"minResolution":     200,
				},
				taskUserID: 3,
			},
			wantErr: false, // 批量处理可能会中断，这是正常情况
		}, {
			name: "测试工作流ID为128的批量图片处理主图",
			args: args{
				ctx:        context.Background(),
				userID:     1,
				workflowId: 128,
				inputData: map[string]any{
					"spu":      123,
					"unqId":      "ed316bd06e01526ac23e5d14d2f77fd4",
					"imageUrl": "http://img.dev.yafex.cn/upload/product/2025/07/15/************/original/8566524b53bcd3628235a2f00b6c1c5b.jpg",
					//"imageUrl":   "http://ai-draw.yafex.cn/static/images/2025/07/15/b7234e21f6f046158bdd765867afc79c.png",
					"picId":      "8566524b53bcd3628235a2f00b6c1c5b",
					"isSku":      true,
					"skuNumber":  "SKU003",
					"languageId": 2, // 用于侵权检测
					"platSiteMap": map[string][]string{ // 用于侵权检测
						"1": {"0", "3"},
						"2": {"1", "2", "14"},
					},
					"isRemoveWatermark": true,
					"isRemoveLogo":      true,
					"minResolution":     300,
				},
				taskUserID: 3,
			},
			wantErr: false, // 批量处理可能会中断，这是正常情况
		},
		{
			name: "测试抠不出图的情况",
			args: args{
				ctx:        context.Background(),
				userID:     1,
				workflowId: 128,
				inputData: map[string]any{
					"unqId":    "maiyouming-test",
					"imageUrl": "http://img.dev.yafex.cn/upload/product/2025/07/18/737769773713/original/autoProduct/660f1ee55be065ab90a8e314f3264571.jpg",
					//"imageUrl":   "http://ai-draw.yafex.cn/static/images/2025/07/15/b7234e21f6f046158bdd765867afc79c.png",
					"picId":      "maiyouming-test",
					"isSku":      true,
					"skuNumber":  "SKU003",
					"languageId": 2, // 用于侵权检测
					"platSiteMap": map[string][]string{ // 用于侵权检测
						"1": {"0", "3"},
						"2": {"1", "2", "14"},
					},
					"isRemoveWatermark": false,
					"isRemoveLogo":      false,
					"minResolution":     200,
				},
				taskUserID: 3,
			},
			wantErr: false, // 批量处理可能会中断，这是正常情况
		},
		{
			name: "抠图效果差的情况",
			args: args{
				ctx:        context.Background(),
				userID:     1,
				workflowId: 128,
				inputData: map[string]any{
					"spu":      "mym-test-spu",
					"unqId":    "maiyouming-test",
					"imageUrl": "http://img.dev.yafex.cn/upload/product/2025/07/18/737769773713/original/autoProduct/8017405eace016354a8ddf34f1cb3e10.jpg",
					//"imageUrl":   "http://ai-draw.yafex.cn/static/images/2025/07/15/b7234e21f6f046158bdd765867afc79c.png",
					"picId":      "maiyouming-test",
					"isSku":      true,
					"skuNumber":  "SKU003",
					"languageId": 2, // 用于侵权检测
					"platSiteMap": map[string][]string{ // 用于侵权检测
						"1": {"0", "3"},
						"2": {"1", "2", "14"},
					},
					"isRemoveWatermark": false,
					"isRemoveLogo":      false,
					"minResolution":     200,
				},
				taskUserID: 3,
			},
			wantErr: false, // 批量处理可能会中断，这是正常情况
		},
		{
			name: "异常抠出字",
			// 这张图实际上没有文字，但识别出了 Aa 走了后续逻辑异常了
			args: args{
				ctx:        context.Background(),
				userID:     1,
				workflowId: 128,
				inputData: map[string]any{
					"spu":        "mym-test-spu",
					"unqId":      "maiyouming-test",
					"imageUrl":   "http://img.dev.svc.yafex.test:8080//upload/product/2025/07/18/857201450080/original/autoProduct/e2e6d5e10a39d8bc239b82c1cfb9e14a.jpg",
					"picId":      "maiyouming-test",
					"isSku":      true,
					"skuNumber":  "SKU003",
					"languageId": 2, // 用于侵权检测
					"platSiteMap": map[string][]string{ // 用于侵权检测
						"1": {"0", "3"},
						"2": {"1", "2", "14"},
					},
					"isRemoveWatermark": false,
					"isRemoveLogo":      false,
					"minResolution":     200,
				},
				taskUserID: 3,
			},
			wantErr: false, // 批量处理可能会中断，这是正常情况
		},
		{
			name: "异常抠出字",
			args: args{
				ctx:        context.Background(),
				userID:     1,
				workflowId: 128,
				inputData: map[string]any{
					"spu":        "mym-test-spu",
					"unqId":      "maiyouming-test",
					"imageUrl":   "http://img.dev.svc.yafex.test:8080//upload/product/2025/07/18/857201450080/original/autoProduct/e2e6d5e10a39d8bc239b82c1cfb9e14a.jpg",
					"picId":      "maiyouming-test",
					"isSku":      true,
					"skuNumber":  "SKU003",
					"languageId": 2, // 用于侵权检测
					"platSiteMap": map[string][]string{ // 用于侵权检测
						"1": {"0", "3"},
						"2": {"1", "2", "14"},
					},
					"isRemoveWatermark": false,
					"isRemoveLogo":      false,
					"minResolution":     200,
				},
				taskUserID: 3,
			},
			wantErr: false, // 批量处理可能会中断，这是正常情况
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := RunWorkflow(tt.args.ctx, tt.args.userID, tt.args.workflowId, tt.args.taskUserID, tt.args.inputData); (err != nil) != tt.wantErr {
				t.Errorf("RunWorkflow() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestCreateImageAndRunWorkflow(t *testing.T) {
	// 初始化测试环境
	if err := initTestEnvironment(); err != nil {
		t.Fatalf("初始化测试环境失败: %v", err)
	}

	// 创建测试上下文
	ctx := context.Background()

	mockData := autoProcess.ImageUploadDataPdm{
		SPU:          "HHH0442498_test_mym",
		Claim:        "抠图、裁剪、翻译、消除、白底、套版.jpg",
		PicURL:       "https://ipds.yafex.cn/data/upload/salesImage/2025-06-04/810a0a5c7e1cc566ee9adabb964df744/37b844d325eceb7cacf455281167ddc1.jpg",
		FullImageURL: "https://ipds.yafex.cn/data/upload/salesImage/2025-06-04/810a0a5c7e1cc566ee9adabb964df744/37b844d325eceb7cacf455281167ddc1.jpg",
		PSUser:       "maiyouming",
		PSUserText:   "麦友铭",
	}

	// 创建图片记录
	imageUploadData, err := (autoProcess.ImageUploadDataPdm{}).CreatePdmImageData(global.GVA_DB, mockData.SPU, mockData.Claim, mockData.PicURL, mockData.PSUser, mockData.PSUserText, time.Now())
	if err != nil {
		t.Fatalf("创建图片数据失败: %v", err)
	}

	t.Logf("成功创建图片数据，ID: %d", imageUploadData.ID)

	// 2. 运行工作流
	type args struct {
		ctx        context.Context
		userID     uint
		workflowId autoProcess.WorkflowId
		inputData  map[string]any
		taskUserID uint
	}

	// 测试用例
	tests := []struct {
		name    string
		args    args
		wantErr bool
	}{
		{
			name: "创建图片数据后运行工作流",
			args: args{
				ctx:        ctx,
				userID:     1,
				workflowId: 1,
				inputData: map[string]any{
					"image_url":     imageUploadData.PicURL, // 使用刚创建的图片URL
					"image_data_id": imageUploadData.ID,     // 使用刚创建的图片ID

				},
				taskUserID: 3,
			},
			wantErr: false,
		},
	}

	// 执行测试
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if err := RunWorkflow(tt.args.ctx, tt.args.userID, tt.args.workflowId, tt.args.taskUserID, tt.args.inputData); (err != nil) != tt.wantErr {
				t.Errorf("RunWorkflow() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestGetAssertDataAndRunWorkflow(t *testing.T) {
	// 初始化测试环境
	if err := initTestEnvironment(); err != nil {
		t.Fatalf("初始化测试环境失败: %v", err)
	}

	// 创建测试上下文
	ctx := context.Background()

	// 1. 从PDM接口获取图片数据
	// 这里使用外部API获取图片数据
	pdmApi := external.GetPDMAPI()

	// 构建搜索参数，查询特定SPU的待处理图片
	searchParams := &pdm.SearchParams{
		SPU:   "UUU0443118", // 指定SPU
		State: 0,            // 0：待处理状态
	}

	searchJSON, err := pdm.BuildSearchParams(searchParams)
	if err != nil {
		t.Fatalf("构建搜索参数失败: %v", err)
	}

	// 构建API请求
	req := &pdm.GetAssertDataReq{
		Page:   1,
		Limit:  5, // 限制数量，避免测试数据过多
		Search: searchJSON,
	}

	// 调用PDM API获取数据
	resp, err := pdmApi.GetAssertData(ctx, req)
	if err != nil {
		t.Logf("调用PDM API失败，可能是网络问题或服务不可用: %v", err)
		t.Skip("跳过PDM API测试")
		return
	}

	if resp == nil || resp.Data == nil || len(resp.Data.List) == 0 {
		t.Log("PDM API返回空数据，跳过测试")
		t.Skip("无数据可测试")
		return
	}

	t.Logf("从PDM获取到 %d 条图片数据", len(resp.Data.List))

	// 2. 循环处理每个图片数据项
	for i, item := range resp.Data.List {
		t.Logf("处理第 %d 个图片数据项, SPU: %s", i+1, item.SPU)

		// 检查是否有图片信息
		if item.PushInfo == nil || item.PushInfo.PicList == nil {
			t.Logf("SPU %s 没有图片信息，跳过", item.SPU)
			continue
		}

		// 处理该SPU下的每张图片
		for j, pic := range item.PushInfo.PicList {
			if pic.PicURL == "" {
				t.Logf("SPU %s 的第 %d 张图片URL为空，跳过", item.SPU, j+1)
				continue
			}

			t.Logf("处理SPU %s 的第 %d 张图片: %s", item.SPU, j+1, pic.PicURL)

			// 保存图片数据到数据库
			imageUploadData, err := (autoProcess.ImageUploadDataPdm{}).CreatePdmImageData(global.GVA_DB, item.SPU, pic.Claim, pic.PicURL, item.PSUser, item.PSUserText, time.Now())
			if err != nil {
				t.Logf("保存图片数据失败: %v", err)
				continue
			}

			t.Logf("成功创建图片数据，ID: %d", imageUploadData.ID)

			// 构建RunWorkflow的输入参数
			inputData := map[string]any{
				"image_url":     imageUploadData.PicURL,
				"image_data_id": imageUploadData.ID,
			}

			// 执行工作流
			err = RunWorkflow(ctx, 1, 1, 3, inputData)
			if err != nil {
				t.Logf("执行工作流失败: %v", err)
			} else {
				t.Logf("成功执行工作流，图片ID: %d", imageUploadData.ID)
			}

			// 限制测试数量，只处理前几张图片
			if j >= 2 { // 每个SPU最多处理3张图片
				break
			}
		}

		// 限制测试数量，只处理前几个SPU
		if i >= 1 { // 最多处理2个SPU
			break
		}
	}

	t.Log("GetAssertData和RunWorkflow循环测试完成")
}

func TestCreateImageAndRunDynamicWorkflow(t *testing.T) {
	// 初始化测试环境
	if err := initTestEnvironment(); err != nil {
		t.Fatalf("初始化测试环境失败: %v", err)
	}

	// 创建测试上下文
	ctx := context.Background()

	// 定义测试用例，每个用例对应不同的工作流组合
	testCases := []struct {
		name         string
		claim        string
		expectedFlow string
		workflowId   autoProcess.WorkflowId
	}{
		{
			name:         "仅抠图流程",
			claim:        "抠图商品.jpg",
			expectedFlow: "仅抠图",
			workflowId:   1, // 2^0 = 1
		},
		{
			name:         "仅裁剪流程",
			claim:        "裁剪产品.jpg",
			expectedFlow: "仅裁剪",
			workflowId:   2, // 2^1 = 2
		},
		{
			name:         "抠图+裁剪流程",
			claim:        "抠图裁剪商品.jpg",
			expectedFlow: "抠图+裁剪",
			workflowId:   3, // 2^0 + 2^1 = 3
		},
		{
			name:         "仅去logo流程",
			claim:        "去logo处理.jpg",
			expectedFlow: "仅去logo",
			workflowId:   4, // 2^2 = 4
		},
		{
			name:         "抠图+去logo流程",
			claim:        "抠图去logo商品.jpg",
			expectedFlow: "抠图+去logo",
			workflowId:   5, // 2^0 + 2^2 = 5
		},
		{
			name:         "仅翻译流程",
			claim:        "翻译文案.jpg",
			expectedFlow: "仅翻译",
			workflowId:   8, // 2^3 = 8
		},
		{
			name:         "仅白底流程",
			claim:        "白底处理.jpg",
			expectedFlow: "仅白底",
			workflowId:   16, // 2^4 = 16
		},
		{
			name:         "仅套版流程",
			claim:        "套版设计.jpg",
			expectedFlow: "仅套版",
			workflowId:   32, // 2^5 = 32
		},
		{
			name:         "去logo+白底+套版流程",
			claim:        "去logo白底套版商品.jpg",
			expectedFlow: "去logo+白底+套版",
			workflowId:   52, // 2^2 + 2^4 + 2^5 = 4 + 16 + 32 = 52
		},
		{
			name:         "抠图+裁剪+翻译流程",
			claim:        "抠图裁剪翻译产品.jpg",
			expectedFlow: "抠图+裁剪+翻译",
			workflowId:   11, // 2^0 + 2^1 + 2^3 = 1 + 2 + 8 = 11
		},
		{
			name:         "完整流程",
			claim:        "抠图裁剪去logo翻译白底套版全套处理.jpg",
			expectedFlow: "完整流程",
			workflowId:   63, // 2^6 - 1 = 63（所有步骤）
		},
		{
			name:         "默认完整流程（无关键字）",
			claim:        "普通商品图片.jpg",
			expectedFlow: "默认完整流程",
			workflowId:   63, // 默认使用完整流程
		},
	}

	// 执行每个测试用例
	for i, testCase := range testCases {
		t.Run(testCase.name, func(t *testing.T) {
			mockData := autoProcess.ImageUploadDataPdm{
				SPU:        "TEST" + fmt.Sprintf("%06d", i+1), // 生成唯一SPU
				Claim:      testCase.claim,
				PicURL:     "https://ipds.yafex.cn/data/upload/salesImage/2025-06-04/810a0a5c7e1cc566ee9adabb964df744/37b844d325eceb7cacf455281167ddc1.jpg",
				PSUser:     "maiyouming",
				PSUserText: "麦友铭",
			}

			// 创建图片记录
			imageUploadData, err := (autoProcess.ImageUploadDataPdm{}).CreatePdmImageData(global.GVA_DB, mockData.SPU, mockData.Claim, mockData.PicURL, mockData.PSUser, mockData.PSUserText, time.Now())
			if err != nil {
				t.Fatalf("创建图片数据失败: %v", err)
			}

			t.Logf("✅ 成功创建图片数据，ID: %d, 原始名称: %s, 期望工作流: %s (ID: %d)",
				imageUploadData.ID, testCase.claim, testCase.expectedFlow, testCase.workflowId)

			// 运行工作流，使用期望的工作流ID
			inputData := map[string]any{
				"image_url":     imageUploadData.PicURL,
				"spu":           imageUploadData.SPU,
				"image_name":    imageUploadData.Claim,
				"image_data_id": imageUploadData.ID,
			}

			err = RunWorkflow(ctx, 1, testCase.workflowId, 3, inputData)
			if err != nil {
				// 工作流可能会因为中断而返回特定错误，这在测试环境中是正常的
				t.Logf("⚠️  工作流执行返回: %v (这可能是正常的中断行为)", err)
			} else {
				t.Logf("✅ 成功执行工作流: %s (ID: %d)", testCase.expectedFlow, testCase.workflowId)
			}
		})
	}

	t.Log("🎉 动态工作流系统测试完成！")
}

// TestWorkflowSelection 测试工作流选择逻辑
func TestWorkflowSelection(t *testing.T) {
	// 测试图片名称映射到工作流ID的逻辑
	testCases := []struct {
		imageName  string
		expectedId autoProcess.WorkflowId
	}{
		{"抠图测试.jpg", 1},
		{"裁剪产品.png", 2},
		{"抠图裁剪商品.jpg", 3},
		{"去logo处理.png", 4},
		{"翻译文案.jpg", 8},
		{"白底处理.jpg", 16},
		{"套版设计.jpg", 32},
		{"去logo白底套版.jpg", 52},
		{"抠图裁剪翻译.jpg", 11},
		{"普通图片.jpg", 63}, // 默认完整流程
	}

	for _, testCase := range testCases {
		t.Run(testCase.imageName, func(t *testing.T) {
			// 这里模拟 getWorkflowIdByImageName 的逻辑
			workflowId := simulateWorkflowSelection(testCase.imageName)

			if workflowId != testCase.expectedId {
				t.Errorf("图片 %s 的工作流选择错误，期望: %d, 实际: %d",
					testCase.imageName, testCase.expectedId, workflowId)
			} else {
				t.Logf("✅ 图片 %s -> 工作流ID: %d", testCase.imageName, workflowId)
			}
		})
	}
}

// simulateWorkflowSelection 模拟工作流选择逻辑
func simulateWorkflowSelection(imageName string) autoProcess.WorkflowId {
	// 关键字映射（需要与实际代码保持一致）
	keywordMapping := map[string]int{
		"抠图":    1,  // 2^0
		"裁剪":    2,  // 2^1
		"去logo": 4,  // 2^2
		"翻译":    8,  // 2^3
		"白底":    16, // 2^4
		"套版":    32, // 2^5
	}

	workflowId := 0
	for keyword, value := range keywordMapping {
		if strings.Contains(imageName, keyword) {
			workflowId |= value
		}
	}

	// 如果没有检测到任何关键字，返回默认裁剪
	if workflowId == 0 {
		return autoProcess.WorkflowId(2) // 裁剪
	}

	return autoProcess.WorkflowId(workflowId)
}

// TestCreateTaskFromLocalImages 从本地图片创建任务的测试方法
func TestCreateTaskFromLocalImages(t *testing.T) {
	// 初始化测试环境
	if err := initTestEnvironment(); err != nil {
		t.Fatalf("初始化测试环境失败: %v", err)
	}

	// 创建测试上下文
	ctx := context.Background()

	// 确保flag已解析
	flag.Parse()

	t.Logf("🚀 开始测试：从本地图片创建任务，分配给用户: %s (环境: %s)", *username, *testEnv)

	// 1. 列出 ai_image 目录中的所有图片文件
	aiImageDir := "./ai_image"
	files, err := os.ReadDir(aiImageDir)
	if err != nil {
		t.Fatalf("❌ 读取 ai_image 目录失败: %v", err)
	}

	// 过滤出图片文件
	var imageFiles []string
	for _, file := range files {
		if !file.IsDir() {
			name := file.Name()
			ext := strings.ToLower(filepath.Ext(name))
			if ext == ".jpg" || ext == ".jpeg" || ext == ".png" {
				imageFiles = append(imageFiles, filepath.Join(aiImageDir, name))
			}
		}
	}

	if len(imageFiles) == 0 {
		t.Fatalf("❌ ai_image 目录中没有找到图片文件")
	}

	// 2. 获取AI Draw API客户端
	aiDrawApi := external.GetAiDrawAPI()

	// 4. 生成随机SPU
	randomSPU := generateRandomSPU()

	// 3. 逐个处理图片文件
	for i, imagePath := range imageFiles {
		fileName := filepath.Base(imagePath)
		fileNameWithoutExt := strings.TrimSuffix(fileName, filepath.Ext(fileName))

		t.Logf("📸 --------------------处理第 %d/%d 个图片: %s--------------------\n", i+1, len(imageFiles), fileName)

		// 检查文件是否存在
		if _, err := os.Stat(imagePath); os.IsNotExist(err) {
			t.Logf("⚠️  文件不存在，跳过: %s", imagePath)
			continue
		}

		// 上传图片到AI Draw服务
		uploadResp, err := uploadImageFromPath(ctx, aiDrawApi, imagePath)
		if err != nil {
			t.Logf("❌ 上传图片失败: %v", err)
			continue
		}

		if uploadResp.Code != 200 || !uploadResp.Success {
			t.Logf("❌ AI Draw 服务返回失败: %s", uploadResp.Msg)
			continue
		}

		imageURL := uploadResp.Data

		// 5. 使用文件名作为Claim
		claim := fileNameWithoutExt

		// 6. 根据Claim获取工作流ID
		workflowId := getWorkflowIdByImageNameLocal(claim)

		// 7. 创建图片数据记录
		imageUploadData, err := (autoProcess.ImageUploadDataPdm{}).CreatePdmImageData(global.GVA_DB, randomSPU, claim, imageURL, *username, *username, time.Now())
		if err != nil {
			t.Logf("❌ 保存图片数据失败: %v", err)
			continue
		}

		// 8. 获取任务分配用户ID
		taskUserID := system.GetUserIdByUsernameWithFallback(*username, global.GVA_DB)

		// 9. 构建工作流输入数据
		inputData := map[string]any{
			"image_url":     imageURL,
			"user_id":       taskUserID,
			"image_name":    fileName,
			"spu":           randomSPU,
			"image_data_id": imageUploadData.ID,
		}

		// 10. 执行工作流
		err = RunWorkflow(ctx, system.SystemUserId, workflowId, taskUserID, inputData)
		if err != nil {
			// 工作流可能会因为中断而返回特定错误，这在测试环境中是正常的
			t.Logf("⚠️  工作流执行返回: %v (这可能是正常的中断行为)", err)
		}

		t.Logf("🎯 任务创建完成: SPU=%s, 图片=%s, 工作流=%d, 分配给=%s\n\n",
			randomSPU, fileName, workflowId, *username)

		// 添加短暂延迟，避免请求过于频繁
		time.Sleep(1 * time.Second)
	}

	t.Log("🎉 所有本地图片处理完成！")
}

// uploadImageFromPath 从文件路径直接上传图片
func uploadImageFromPath(ctx context.Context, aiDrawApi ai_draw.API, filePath string) (*ai_draw.UploadImageResp, error) {
	// 使用HTTP客户端直接上传文件
	baseURL := aiDrawApi.BaseURL()

	// 创建resty客户端
	client := resty.New()
	client.SetTimeout(time.Duration(aiDrawApi.Timeout()) * time.Second)

	// 执行文件上传
	resp, err := client.R().
		SetContext(ctx).
		SetFile("image", filePath).
		SetResult(&ai_draw.UploadImageResp{}).
		Post(baseURL + "/api/upload/image")

	if err != nil {
		return nil, fmt.Errorf("上传请求失败: %w", err)
	}

	if resp.StatusCode() != 200 {
		return nil, fmt.Errorf("上传失败，状态码: %d, 响应: %s", resp.StatusCode(), string(resp.Body()))
	}

	result, ok := resp.Result().(*ai_draw.UploadImageResp)
	if !ok {
		return nil, fmt.Errorf("响应解析失败")
	}

	return result, nil
}

// generateRandomSPU 生成随机SPU
func generateRandomSPU() string {
	// 使用时间戳和随机数生成唯一SPU
	timestamp := time.Now().Unix()
	randomPart := rand.Intn(999999)
	return fmt.Sprintf("TEST%d%06d", timestamp%1000000, randomPart)
}

// getWorkflowIdByImageName 根据图片名称获取工作流ID（本地版本，避免循环导入）
func getWorkflowIdByImageNameLocal(imageName string) autoProcess.WorkflowId {
	// 图片名称关键字映射
	keywordMapping := map[string]int{
		"抠图":    1,  // 2^0
		"裁剪":    2,  // 2^1
		"去logo": 4,  // 2^2
		"去水印":   4,  // 2^2
		"消除":    4,  // 2^2
		"翻译":    8,  // 2^3
		"白底":    16, // 2^4
		"套版":    32, // 2^5
	}

	workflowId := 0
	var detectedKeywords []string

	for keyword, value := range keywordMapping {
		if strings.Contains(imageName, keyword) {
			workflowId |= value
			detectedKeywords = append(detectedKeywords, keyword)
		}
	}

	// 如果没有检测到任何关键字，返回默认裁剪
	if workflowId == 0 {
		fmt.Printf("图片名称 '%s' 中未检测到关键字，使用默认裁剪\n", imageName)
		return autoProcess.WorkflowId(2) // 裁剪工作流
	}

	return autoProcess.WorkflowId(workflowId)
}

// TestImageExpansionNode 测试AI扩图节点
func TestImageExpansionNode(t *testing.T) {
	// 初始化测试环境
	if err := initTestEnvironment(); err != nil {
		t.Fatalf("初始化测试环境失败: %v", err)
	}

	// 创建测试上下文
	ctx := context.Background()

	// 确保flag已解析
	flag.Parse()
	testUsername := "zhongzhigang"

	t.Logf("🚀 开始测试：AI扩图节点功能，分配给用户: %s (环境: %s)", testUsername, *testEnv)

	// 1. 指定要测试的图片
	imagePath := "./ai_image/扩图.png"
	fileName := filepath.Base(imagePath)
	fileNameWithoutExt := strings.TrimSuffix(fileName, filepath.Ext(fileName))

	t.Logf("📸 处理图片: %s", fileName)

	// 检查文件是否存在
	if _, err := os.Stat(imagePath); os.IsNotExist(err) {
		t.Fatalf("❌ 测试图片不存在: %s", imagePath)
	}

	// 2. 获取AI Draw API客户端
	aiDrawApi := external.GetAiDrawAPI()
	t.Logf("🔗 AI Draw API 配置: %s (超时: %ds)", aiDrawApi.BaseURL(), aiDrawApi.Timeout())

	// 3. 上传图片到AI Draw服务
	t.Logf("📤 正在上传图片...")
	uploadResp, err := uploadImageFromPath(ctx, aiDrawApi, imagePath)
	if err != nil {
		t.Fatalf("❌ 上传图片失败: %v", err)
	}

	if uploadResp.Code != 200 || !uploadResp.Success {
		t.Fatalf("❌ AI Draw 服务返回失败: %s", uploadResp.Msg)
	}

	imageURL := uploadResp.Data
	t.Logf("✅ 图片上传成功: %s", imageURL)

	// 4. 生成随机SPU
	randomSPU := generateRandomSPU()

	// 5. 使用文件名作为Claim
	claim := fileNameWithoutExt

	// 6. 指定扩图工作流ID
	workflowId := autoProcess.WorkflowId(64) // 64 for StepExpansion
	t.Logf("🔧 指定工作流ID: %d (扩图)", workflowId)

	// 7. 创建图片数据记录
	imageUploadData, err := (autoProcess.ImageUploadDataPdm{}).CreatePdmImageData(global.GVA_DB, randomSPU, claim, imageURL, testUsername, testUsername, time.Now())
	if err != nil {
		t.Fatalf("❌ 保存图片数据失败: %v", err)
	}

	t.Logf("💾 成功创建图片数据记录，ID: %d, SPU: %s", imageUploadData.ID, randomSPU)

	// 8. 获取任务分配用户ID
	taskUserID := system.GetUserIdByUsernameWithFallback(testUsername, global.GVA_DB)
	t.Logf("👤 任务分配给用户: %s (ID: %d)", testUsername, taskUserID)

	// 9. 构建工作流输入数据
	inputData := map[string]any{
		"image_url":     imageURL,
		"user_id":       taskUserID,
		"image_name":    fileName,
		"spu":           randomSPU,
		"image_data_id": imageUploadData.ID,
	}

	// 10. 执行工作流
	t.Logf("⚙️  正在执行工作流...")
	err = RunWorkflow(ctx, system.SystemUserId, workflowId, taskUserID, inputData)
	if err != nil {
		// 工作流可能会因为中断而返回特定错误，这在测试环境中是正常的
		t.Logf("⚠️  工作流执行返回: %v (这可能是正常的中断行为)", err)
	} else {
		t.Logf("✅ 工作流执行成功")
	}

	t.Logf("🎯 任务创建完成: SPU=%s, 图片=%s, 工作流=%d, 分配给=%s",
		randomSPU, fileName, workflowId, testUsername)

	t.Log("🎉 AI扩图节点测试完成！")
}

// TestCreateTaskBySpu 根据SPU为指定用户批量创建图片处理任务
func TestCreateTaskBySpu(t *testing.T) {
	// 初始化测试环境
	if err := initTestEnvironment(); err != nil {
		t.Fatalf("初始化测试环境失败: %v", err)
	}

	// 解析命令行参数
	flag.Parse()

	// 验证必要参数
	if *spu == "" {
		t.Fatalf("❌ 请通过 -spu 参数指定SPU编码")
	}

	t.Logf("🚀 开始批量创建图片处理任务")
	t.Logf("📋 参数信息: SPU=%s, 环境=%s", *spu, *testEnv)

	ctx := context.Background()

	// 查询指定SPU的所有待处理图片记录
	var imageDataList []autoProcess.ImageUploadDataPdm
	err := global.GVA_DB.Where("spu = ? AND status = ?", *spu, autoProcess.ProcessStatusPending).
		Find(&imageDataList).Error

	if err != nil {
		t.Fatalf("❌ 查询图片记录失败: %v", err)
	}

	if len(imageDataList) == 0 {
		t.Fatalf("❌ 未找到匹配的图片记录: SPU=%s, 状态=待处理", *spu)
	}

	t.Logf("✅ 找到 %d 条图片记录", len(imageDataList))

	// 统计信息
	successCount := 0
	failureCount := 0

	// 循环处理每条图片记录
	for i, imageData := range imageDataList {
		t.Logf("\n📸 处理第 %d/%d 条图片记录: ID=%d", i+1, len(imageDataList), imageData.ID)
		t.Logf("   - SPU: %s", imageData.SPU)
		t.Logf("   - Claim: %s", imageData.Claim)
		t.Logf("   - PicURL: %s", imageData.PicURL)

		// 获取任务分配用户ID
		userID := system.GetUserIdByUsernameWithFallback(imageData.PSUser, global.GVA_DB)
		if userID == 0 {
			t.Logf("❌ 未找到用户: %s，跳过此记录", imageData.PSUser)
			failureCount++
			continue
		}
		t.Logf("👤 任务分配给用户: %s (ID: %d)", imageData.PSUser, userID)

		// 根据图片描述获取工作流ID
		workflowId := getWorkflowIdByImageNameLocal(imageData.Claim)
		t.Logf("🔧 根据图片名称 '%s' 检测到工作流ID: %d", imageData.Claim, workflowId)

		// 构建工作流输入数据（参考processPdmImageData逻辑）
		inputData := map[string]any{
			"image_url":     imageData.PicURL,
			"user_id":       userID,
			"image_name":    imageData.OriginalName + "|" + imageData.Claim,
			"spu":           imageData.SPU,
			"image_data_id": imageData.ID,
		}

		t.Logf("📦 构建输入数据: %+v", inputData)

		// 执行工作流（参考processPdmImageData逻辑）
		t.Logf("⚙️  正在执行工作流...")
		err = RunWorkflow(ctx, system.SystemUserId, workflowId, userID, inputData)
		if err != nil {
			// 工作流可能会因为中断而返回特定错误，这在测试环境中是正常的
			t.Logf("⚠️  工作流执行返回: %v (这可能是正常的中断行为)", err)
			failureCount++
		} else {
			t.Logf("✅ 工作流执行成功")
			successCount++
		}

		t.Logf("🎯 第 %d 条记录处理完成:", i+1)
		t.Logf("   - 图片ID: %d", imageData.ID)
		t.Logf("   - 图片名称: %s", imageData.Claim)
		t.Logf("   - 工作流ID: %d", workflowId)
		t.Logf("   - 分配用户: %s (ID: %d)", imageData.PSUser, userID)

		// 添加短暂延迟，避免请求过于频繁
		time.Sleep(500 * time.Millisecond)
	}

	// 输出最终统计信息
	t.Logf("\n🎉 批量图片处理任务创建完成！")
	t.Logf("📊 处理统计:")
	t.Logf("   - 总记录数: %d", len(imageDataList))
	t.Logf("   - 成功数: %d", successCount)
	t.Logf("   - 失败数: %d", failureCount)
	t.Logf("   - SPU: %s", *spu)
	t.Logf("   - 运行环境: %s", *testEnv)
}

// TestCropOnlyWorkflow 测试仅裁剪流程
func TestCropOnlyWorkflow(t *testing.T) {
	// 初始化测试环境
	if err := initTestEnvironment(); err != nil {
		t.Fatalf("初始化测试环境失败: %v", err)
	}

	// 创建测试上下文
	ctx := context.Background()

	// 确保flag已解析
	flag.Parse()
	testUsername := "zhongzhigang"

	t.Logf("🚀 开始测试：仅裁剪流程，分配给用户: %s (环境: %s)", testUsername, *testEnv)

	// 1. 指定要测试的图片
	imagePath := "./ai_image/裁剪.png"
	fileName := filepath.Base(imagePath)
	fileNameWithoutExt := strings.TrimSuffix(fileName, filepath.Ext(fileName))

	t.Logf("📸 处理图片: %s", fileName)

	// 检查文件是否存在
	if _, err := os.Stat(imagePath); os.IsNotExist(err) {
		t.Fatalf("❌ 测试图片不存在: %s", imagePath)
	}

	// 2. 获取AI Draw API客户端
	aiDrawApi := external.GetAiDrawAPI()
	t.Logf("🔗 AI Draw API 配置: %s (超时: %ds)", aiDrawApi.BaseURL(), aiDrawApi.Timeout())

	// 3. 上传图片到AI Draw服务
	t.Logf("📤 正在上传图片...")
	uploadResp, err := uploadImageFromPath(ctx, aiDrawApi, imagePath)
	if err != nil {
		t.Fatalf("❌ 上传图片失败: %v", err)
	}

	if uploadResp.Code != 200 || !uploadResp.Success {
		t.Fatalf("❌ AI Draw 服务返回失败: %s", uploadResp.Msg)
	}

	imageURL := uploadResp.Data
	t.Logf("✅ 图片上传成功: %s", imageURL)

	// 4. 生成随机SPU
	randomSPU := generateRandomSPU()

	// 5. 使用文件名作为Claim
	claim := fileNameWithoutExt

	// 6. 指定裁剪工作流ID
	workflowId := autoProcess.WorkflowId(2) // 2 for StepCrop (仅裁剪)
	t.Logf("🔧 指定工作流ID: %d (仅裁剪)", workflowId)

	// 7. 创建图片数据记录
	imageUploadData, err := (autoProcess.ImageUploadDataPdm{}).CreatePdmImageData(global.GVA_DB, randomSPU, claim, imageURL, testUsername, testUsername, time.Now())
	if err != nil {
		t.Fatalf("❌ 保存图片数据失败: %v", err)
	}

	t.Logf("💾 成功创建图片数据记录，ID: %d, SPU: %s", imageUploadData.ID, randomSPU)

	// 8. 获取任务分配用户ID
	taskUserID := system.GetUserIdByUsernameWithFallback(testUsername, global.GVA_DB)
	t.Logf("👤 任务分配给用户: %s (ID: %d)", testUsername, taskUserID)

	// 9. 构建工作流输入数据
	inputData := map[string]any{
		"image_url":     imageURL,
		"user_id":       taskUserID,
		"image_name":    fileName,
		"spu":           randomSPU,
		"image_data_id": imageUploadData.ID,
	}

	// 10. 执行工作流
	t.Logf("⚙️  正在执行仅裁剪工作流...")
	err = RunWorkflow(ctx, system.SystemUserId, workflowId, taskUserID, inputData)
	if err != nil {
		// 工作流可能会因为中断而返回特定错误，这在测试环境中是正常的
		t.Logf("⚠️  工作流执行返回: %v (这可能是正常的中断行为)", err)
	} else {
		t.Logf("✅ 工作流执行成功")
	}

	t.Logf("🎯 任务创建完成: SPU=%s, 图片=%s, 工作流=%d (仅裁剪), 分配给=%s",
		randomSPU, fileName, workflowId, testUsername)

	t.Log("🎉 仅裁剪流程测试完成！")
}
