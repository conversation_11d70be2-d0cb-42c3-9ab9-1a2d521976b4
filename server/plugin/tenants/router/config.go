package router

import (
	// "github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

var Config = new(cf)

type cf struct{}

// Init 初始化 租户配置 路由信息
func (r *cf) Init(public *gin.RouterGroup, private *gin.RouterGroup) {
	{
		group := private.Group("cf").Use(middleware.OperationRecord())
		group.POST("createConfig", apiConfig.CreateConfig)              // 新建租户配置
		group.PUT("openTenant", apiConfig.OpenTenant)                   // 开启租户
		group.PUT("closeTenant", apiConfig.CloseTenant)                 // 关闭租户
		group.PUT("restartTenant", apiConfig.RestartTenant)             // 重启租户
		group.PUT("initTenant", apiConfig.InitTenant)                   // 初始化租户服务器
		group.PUT("editConfig", apiConfig.EditConfig)                   // 修改配置
		group.PUT("hotAddOrUpdateTable", apiConfig.HotAddOrUpdateTable) // 热更新-表结构
	}
	{
		group := private.Group("cf")
		group.GET("getConfigList", apiConfig.GetConfigList)                                   // 分页获取租户配置列表
		group.GET("getAddOrUpdateTabelList", apiConfig.GetAddOrUpdateTabelList)               // 获取热更新列表
		group.GET("getAddOrUpdateTableHistoryList", apiConfig.GetAddOrUpdateTableHistoryList) // 获取热更新历史记录
		group.GET("getHotMenuDataList", apiConfig.GetHotMenuDataList)                         // 热更新-菜单-获取列表
		group.GET("getHotApiDataList", apiConfig.GetHotApiDataList)                           // 热更新-API-获取列表
		group.GET("getAllAuthority", apiConfig.GetAllAuthority)                               // 热更新-角色-获取列表
		group.GET("getHotAuthority", apiConfig.GetHotAuthority)                               // 热更新-角色权限
	}
	{
		group := public.Group("cf")
		group.GET("getConfigPublic", apiConfig.GetConfigPublic) // 租户配置开放接口
	}
}
