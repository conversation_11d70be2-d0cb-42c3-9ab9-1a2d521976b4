package router

import (
	"github.com/flipped-aurora/gin-vue-admin/server/middleware"
	"github.com/gin-gonic/gin"
)

type menuRouter struct{}

func (r *menuRouter) Init(public *gin.RouterGroup, private *gin.RouterGroup) {
	group := private.Group("menu").Use(middleware.OperationRecord())
	{
		group.PUT("setMenuAuthority", apiMenu.SetMenuAuthority) //热更新-租户-角色-菜单
		group.GET("getAuthorityBtn", apiMenu.GetAuthorityBtn)   //热更新-租户-角色-菜单-按钮
		group.PUT("setAuthorityBtn", apiMenu.SetAuthorityBtn)   //热更新-设置租户-角色-菜单-按钮
	}
}
