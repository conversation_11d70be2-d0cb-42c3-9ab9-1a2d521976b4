package router

import "github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/api"

var (
	Router          = new(router)
	apiDatabase     = api.Api.Database
	apiConfig       = api.Api.Config
	apiUser         = api.Api.User
	apiRenewalLease = api.Api.RenewalLease
	apiCasbinApi    = api.Api.CasbinApi
	apiMenu         = api.Api.MenuApi
)

type router struct {
	Database     db
	Config       cf
	User         use
	RenewalLease rl
	CasbinRouter casbinRouter
	MenuRouter   menuRouter
}
