package initialize

import (
	"context"
	model "github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/plugin-tool/utils"
)

func Api(ctx context.Context) {
	entities := []model.SysApi{{Path: "/db/getDatabaseList", Description: "获取数据库列表", ApiGroup: "数据库管理", Method: "GET"}, {Path: "/db/createDatabase", Description: "创建数据库", ApiGroup: "数据库管理", Method: "POST"}, {Path: "/db/deleteDatabase", Description: "删除数据库", ApiGroup: "数据库管理", Method: "DELETE"}, {Path: "/db/deleteDatabaseByIds", Description: "批量删除数据库", ApiGroup: "数据库管理", Method: "DELETE"}, {Path: "/cf/getConfigList", Description: "获取配置列表", ApiGroup: "租户配置", Method: "GET"}, {Path: "/cf/getConfigPublic", Description: "测试接口", ApiGroup: "租户配置", Method: "GET"}, {Path: "/db/findDatabase", Description: "根据ID获取数据库详情", ApiGroup: "数据库管理", Method: "GET"}, {Path: "/cf/createConfig", Description: "创建配置", ApiGroup: "租户配置", Method: "POST"}, {Path: "/cf/openTenant", Description: "启动服务器", ApiGroup: "租户配置", Method: "PUT"}, {Path: "/cf/closeTenant", Description: "关闭服务器", ApiGroup: "租户配置", Method: "PUT"}, {Path: "/cf/restartTenant", Description: "重启服务器", ApiGroup: "租户配置", Method: "PUT"}, {Path: "/cf/initTenant", Description: "初始化服务器", ApiGroup: "租户配置", Method: "PUT"}, {Path: "/db/updateDatabase", Description: "更新数据库", ApiGroup: "数据库管理", Method: "PUT"}, {Path: "/rl/createRenewalLease", Description: "新增续租记录", ApiGroup: "续租记录", Method: "POST"}, {Path: "/rl/deleteRenewalLease", Description: "删除续租记录", ApiGroup: "续租记录", Method: "DELETE"}, {Path: "/rl/deleteRenewalLeaseByIds", Description: "批量删除续租记录", ApiGroup: "续租记录", Method: "DELETE"}, {Path: "/rl/updateRenewalLease", Description: "更新续租记录", ApiGroup: "续租记录", Method: "PUT"}, {Path: "/rl/findRenewalLease", Description: "根据ID获取续租记录", ApiGroup: "续租记录", Method: "GET"}, {Path: "/rl/getRenewalLeaseList", Description: "获取续租记录列表", ApiGroup: "续租记录", Method: "GET"}, {Path: "/db/pingDatabase", Description: "PING测试", ApiGroup: "数据库管理", Method: "POST"}, {Path: "/cf/editConfig", Description: "修改配置", ApiGroup: "租户配置", Method: "PUT"}, {Path: "/use/createUser", Description: "新增租户", ApiGroup: "租户信息", Method: "POST"}, {Path: "/use/deleteUser", Description: "删除租户", ApiGroup: "租户信息", Method: "DELETE"}, {Path: "/use/deleteUserByIds", Description: "批量删除租户", ApiGroup: "租户信息", Method: "DELETE"}, {Path: "/use/updateUser", Description: "更新租户", ApiGroup: "租户信息", Method: "PUT"}, {Path: "/use/findUser", Description: "根据ID获取租户", ApiGroup: "租户信息", Method: "GET"}, {Path: "/use/getUserList", Description: "获取租户列表", ApiGroup: "租户信息", Method: "GET"}, {Path: "/cf/getAddOrUpdateTabelList", Description: "获取热更新列表-数据库", ApiGroup: "租户热更新", Method: "GET"}, {Path: "/cf/hotAddOrUpdateTable", Description: "热更新-表结构", ApiGroup: "租户热更新", Method: "PUT"}, {Path: "/cf/getAddOrUpdateTableHistoryList", Description: "热更新-表结构-历史记录", ApiGroup: "租户热更新", Method: "GET"}, {Path: "/cf/getHotMenuDataList", Description: "热更新-菜单列表", ApiGroup: "租户热更新", Method: "GET"}, {Path: "/cf/getHotApiDataList", Description: "热更新-api列表", ApiGroup: "租户热更新", Method: "GET"}, {Path: "/cf/getAllAuthority", Description: "热更新-获取角色列表", ApiGroup: "租户热更新", Method: "GET"}, {Path: "/cf/getHotAuthority", Description: "热更新-获取角色权限", ApiGroup: "租户热更新", Method: "GET"}, {Path: "/cb/updateCasbin", Description: "热更新-角色api", ApiGroup: "租户热更新", Method: "PUT"}, {Path: "/menu/setMenuAuthority", Description: "热更新-租户角色菜单", ApiGroup: "租户热更新", Method: "PUT"}, {Path: "/menu/getAuthorityBtn", Description: "热更新-获取租户角色按钮", ApiGroup: "租户热更新", Method: "GET"}, {Path: "/menu/setAuthorityBtn", Description: "热更新-设置租户角色按钮", ApiGroup: "租户热更新", Method: "PUT"}}
	utils.RegisterApis(entities...)
}
