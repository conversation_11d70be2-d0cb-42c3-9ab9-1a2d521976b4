package initialize

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	t_middleware "github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/middleware"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/router"
	"github.com/gin-gonic/gin"
)

func Router(engine *gin.Engine) {
	public := engine.Group(global.GVA_CONFIG.System.RouterPrefix).Group("")
	public.Use()
	private := engine.Group(global.GVA_CONFIG.System.RouterPrefix).Group("")
	private.Use(t_middleware.JWTAuth()).Use(t_middleware.TenantsCasbinHandler())
	router.Router.Database.Init(public, private)
	router.Router.Config.Init(public, private)
	router.Router.User.Init(public, private)
	router.Router.RenewalLease.Init(public, private)
	router.Router.CasbinRouter.Init(public, private)
	router.Router.MenuRouter.Init(public, private)
}
