package config

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/example"
	"github.com/flipped-aurora/gin-vue-admin/server/model/mcp"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	t_model "github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/model"
)

type Config struct {
}

// 基础
var BaseAutoMigrate = map[string]t_model.TenantTable{
	"SysApi":                   {StructName: "SysApi", Version: "1.0.0", Description: "基础-api", AutoMigrate: system.SysApi{}},
	"SysIgnoreApi":             {StructName: "SysIgnoreApi", Version: "1.0.0", Description: "忽略-api", AutoMigrate: system.SysIgnoreApi{}},
	"SysUser":                  {StructName: "SysUser", Version: "1.0.0", Description: "系统用户", AutoMigrate: system.SysUser{}},
	"SysBaseMenu":              {StructName: "SysBaseMenu", Version: "1.0.0", Description: "基础菜单", AutoMigrate: system.SysBaseMenu{}},
	"JwtBlacklist":             {StructName: "JwtBlacklist", Version: "1.0.0", Description: "JWT黑名单", AutoMigrate: system.JwtBlacklist{}},
	"SysAuthority":             {StructName: "SysAuthority", Version: "1.0.0", Description: "系统角色", AutoMigrate: system.SysAuthority{}},
	"SysDictionary":            {StructName: "SysDictionary", Version: "1.0.0", Description: "系统字典", AutoMigrate: system.SysDictionary{}},
	"SysOperationRecord":       {StructName: "SysOperationRecord", Version: "1.0.0", Description: "操作记录", AutoMigrate: system.SysOperationRecord{}},
	"SysAutoCodeHistory":       {StructName: "SysAutoCodeHistory", Version: "1.0.0", Description: "自动化代码历史", AutoMigrate: system.SysAutoCodeHistory{}},
	"SysDictionaryDetail":      {StructName: "SysDictionaryDetail", Version: "1.0.0", Description: "字典详情", AutoMigrate: system.SysDictionaryDetail{}},
	"SysBaseMenuParameter":     {StructName: "SysBaseMenuParameter", Version: "1.0.0", Description: "基础菜单参数", AutoMigrate: system.SysBaseMenuParameter{}},
	"SysBaseMenuBtn":           {StructName: "SysBaseMenuBtn", Version: "1.0.0", Description: "基础菜单按钮", AutoMigrate: system.SysBaseMenuBtn{}},
	"SysAuthorityBtn":          {StructName: "SysAuthorityBtn", Version: "1.0.0", Description: "角色按钮", AutoMigrate: system.SysAuthorityBtn{}},
	"SysAutoCodePackage":       {StructName: "SysAutoCodePackage", Version: "1.0.0", Description: "自动化代码包", AutoMigrate: system.SysAutoCodePackage{}},
	"SysExportTemplate":        {StructName: "SysExportTemplate", Version: "1.0.0", Description: "导出模板", AutoMigrate: system.SysExportTemplate{}},
	"Condition":                {StructName: "Condition", Version: "1.0.0", Description: "条件", AutoMigrate: system.Condition{}},
	"JoinTemplate":             {StructName: "JoinTemplate", Version: "1.0.0", Description: "连接模板", AutoMigrate: system.JoinTemplate{}},
	"SysParams":                {StructName: "SysParams", Version: "1.0.0", Description: "系统参数", AutoMigrate: system.SysParams{}},
	"ExaFile":                  {StructName: "ExaFile", Version: "1.0.0", Description: "示例文件", AutoMigrate: example.ExaFile{}},
	"ExaCustomer":              {StructName: "ExaCustomer", Version: "1.0.0", Description: "示例客户", AutoMigrate: example.ExaCustomer{}},
	"ExaFileChunk":             {StructName: "ExaFileChunk", Version: "1.0.0", Description: "文件分块", AutoMigrate: example.ExaFileChunk{}},
	"ExaFileUploadAndDownload": {StructName: "ExaFileUploadAndDownload", Version: "1.0.0", Description: "文件上传下载", AutoMigrate: example.ExaFileUploadAndDownload{}},
	"ExaAttachmentCategory":    {StructName: "ExaAttachmentCategory", Version: "1.0.0", Description: "附件分类", AutoMigrate: example.ExaAttachmentCategory{}},
	// MCP相关表结构 - 租户初始化时自动创建
	"MCPService":       {StructName: "MCPService", Version: "1.0.0", Description: "MCP服务注册表", AutoMigrate: mcp.MCPService{}},
	"MCPTool":          {StructName: "MCPTool", Version: "1.0.0", Description: "MCP工具信息表", AutoMigrate: mcp.MCPTool{}},
	"MCPCallLog":       {StructName: "MCPCallLog", Version: "1.0.0", Description: "MCP调用日志表", AutoMigrate: mcp.MCPCallLog{}},
	"MCPServiceHealth": {StructName: "MCPServiceHealth", Version: "1.0.0", Description: "MCP服务健康检查记录表", AutoMigrate: mcp.MCPServiceHealth{}},
}

// 添加或更新
var AddOrUpdateAutoMigrate = map[string]t_model.TenantTable{
	"Test": {StructName: "Test", Version: "1.0.0", Description: "测试", AutoMigrate: Test{}},
	// MCP相关表结构 - 用于现有租户的热更新升级
	"MCPService":       {StructName: "MCPService", Version: "1.0.0", Description: "MCP服务注册表", AutoMigrate: mcp.MCPService{}},
	"MCPTool":          {StructName: "MCPTool", Version: "1.0.0", Description: "MCP工具信息表", AutoMigrate: mcp.MCPTool{}},
	"MCPCallLog":       {StructName: "MCPCallLog", Version: "1.0.0", Description: "MCP调用日志表", AutoMigrate: mcp.MCPCallLog{}},
	"MCPServiceHealth": {StructName: "MCPServiceHealth", Version: "1.0.0", Description: "MCP服务健康检查记录表", AutoMigrate: mcp.MCPServiceHealth{}},
}

type Test struct {
	ID uint `gorm:"primarykey"`
}

func (Test) TableName() string {
	return "dsafdsafdsaf_Test"
}
