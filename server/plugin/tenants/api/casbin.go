package api

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/model/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type CasbinApi struct{}

func (q *CasbinApi) UpdateCasbin(c *gin.Context) {
	var req request.CasbinInReceive
	if err := c.ShouldBindJSON(&req); err != nil {
		global.GVA_LOG.Error("参数绑定失败!", zap.Any("err", err))
		response.FailWithMessage("参数绑定失败", c)
		return
	}
	err := serviceCasbin.UpdateCasbin(req.ConfigID, req.AuthorityId, req.CasbinInfos)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Any("err", err))
		response.FailWithMessage("更新失败", c)
		return
	}
	response.OkWithMessage("更新成功", c)
}
