package api

import (
	"fmt"
	"strconv"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/model"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/model/request"
	systemService "github.com/flipped-aurora/gin-vue-admin/server/service/system"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

var Config = new(cf)

type cf struct{}

// GetConfigPublic 不需要鉴权的租户配置接口
// @Tags Config
// @Summary 不需要鉴权的租户配置接口
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /cf/getConfigPublic [get]
func (a *cf) GetConfigPublic(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 此接口不需要鉴权 示例为返回了一个固定的消息接口，一般本接口用于C端服务，需要自己实现业务逻辑
	serviceConfig.GetConfigPublic(ctx)
	response.OkWithDetailed(gin.H{"info": "不需要鉴权的租户配置接口信息"}, "获取成功", c)
}

// @Tags Config
// @Summary 新增租户配置
// @Accept application/json
// @Produce application/json
// @Param data body model.Config true "新增租户配置"
// @Success 200 {object} response.Response{data=object,msg=string} "新增成功"
// @Router /cf/add [post]
func (a *cf) CreateConfig(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	// 获取参数
	var cf request.Config
	if err := c.ShouldBindJSON(&cf); err != nil {
		global.GVA_LOG.Error("参数绑定失败!", zap.Any("err", err))
		response.FailWithMessage("参数绑定失败", c)
		return
	}

		// 验证租户ID
	if cf.TenantId == "" {
		response.FailWithMessage("租户ID不能为空", c)
		return
	}
	
	// 根据TenantId查询关联的租户用户信息
	// 这里假设每个租户只有一个主用户，或者取第一个启用的用户
	var tenantUser model.User
	if err := global.GVA_DB.Where("id = ?", cf.UserId).First(&tenantUser).Error; err != nil {
		global.GVA_LOG.Error("获取租户用户信息失败!", zap.Error(err))
		response.FailWithMessage("获取租户用户信息失败", c)
		return
	}
	
	// 验证用户名密码不为空
	if tenantUser.UserName == nil || *tenantUser.UserName == "" {
		response.FailWithMessage("租户用户名为空", c)
		return
	}
	if tenantUser.Password == nil || *tenantUser.Password == "" {
		response.FailWithMessage("租户密码为空", c)
		return
	}
	
	// 检查管理员用户名的全局唯一性
	if err := systemService.UserServiceApp.CheckGlobalUsernameUnique(*tenantUser.UserName); err != nil {
		global.GVA_LOG.Error("管理员用户名校验失败!", zap.Error(err))
		response.FailWithMessage(err.Error(), c)
		return
	}

	// 若有效期不为空，则检查是否大于当前时间
	if cf.ExpirationDate != nil && !cf.ExpirationDate.IsZero() {
		if cf.ExpirationDate.Before(time.Now()) {
			global.GVA_LOG.Error("有效期不能早于当前时间!")
			response.FailWithMessage("有效期不能早于当前时间", c)
			return
		}
	}

	// 添加租户配置
	err := serviceConfig.CreateConfig(ctx, model.Config{
		UserId:                cf.UserId,
		TenantId:              cf.TenantId,
		Description:           cf.Description,
		IsAutoStart:           cf.IsAutoStart,
		DatabaseId:            cf.DatabaseId,
		DbName:                cf.DbName,
		AuthorityId:           cf.AuthorityId,
		IsCustomizedAuthority: cf.IsCustomizedAuthority,
		ExpirationDate:        cf.ExpirationDate,
		JWT: model.JWT{
			SigningKey:  cf.SigningKey,
			BufferTime:  cf.BufferTime,
			ExpiresTime: cf.ExpiresTime,
			Issuer:      cf.Issuer,
		},
	})
	if err != nil {
		global.GVA_LOG.Error("新增失败!", zap.Any("err", err))
		response.FailWithMessage("新增失败", c)
		return
	}

	response.OkWithMessage("新增成功", c)
}

// 修改配置
func (a *cf) EditConfig(c *gin.Context) {

	// 获取参数
	var cf request.EditConfig
	if err := c.ShouldBindJSON(&cf); err != nil {
		global.GVA_LOG.Error("参数绑定失败!", zap.Any("err", err))
		response.FailWithMessage("参数绑定失败", c)
		return
	}

	// 编辑配置时，无需检查管理员用户名，因为它来自关联的租户用户

	// 修改租户配置
	err := serviceConfig.EditConfig(model.Config{
		GVA_MODEL: global.GVA_MODEL{
			ID: cf.ID,
		},
		TenantId:              cf.TenantId,
		Description:           cf.Description,
		IsAutoStart:           cf.IsAutoStart,
		AuthorityId:           cf.AuthorityId,
		IsCustomizedAuthority: cf.IsCustomizedAuthority,
		ExpirationDate:        cf.ExpirationDate,
		JWT: model.JWT{
			SigningKey:  cf.SigningKey,
			BufferTime:  cf.BufferTime,
			ExpiresTime: cf.ExpiresTime,
			Issuer:      cf.Issuer,
		},
	})
	if err != nil {
		global.GVA_LOG.Error("修改失败!", zap.Any("err", err))
		response.FailWithMessage("修改失败", c)
		return
	}
	response.OkWithMessage("修改成功", c)
}

// GetConfigList 分页获取配置列表
// @Tags Config
// @Summary 分页获取配置列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.ConfigSearch true "分页获取配置列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
func (a *cf) GetConfigList(c *gin.Context) {
	var pageInfo request.ConfigSearch
	if err := c.ShouldBindQuery(&pageInfo); err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	list, total, err := serviceConfig.GetConfigList(pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Any("err", err))
		response.FailWithMessage("获取失败", c)
		return
	}

	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// 打开租户
func (s *cf) OpenTenant(c *gin.Context) {
	ConfigID := c.Query("ID")

	ID, err := strconv.Atoi(ConfigID)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}
	err = serviceConfig.OpenTenant(uint(ID))
	if err != nil {
		global.GVA_LOG.Error("打开租户失败!", zap.Any("err", err))
		response.FailWithMessage(fmt.Sprintf("打开租户失败, %s", err), c)
		return
	}
	response.Ok(c)
}

// 关闭租户
func (s *cf) CloseTenant(c *gin.Context) {
	ConfigID := c.Query("ID")
	ID, err := strconv.Atoi(ConfigID)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}
	err = serviceConfig.CloseTenant(uint(ID))
	if err != nil {
		global.GVA_LOG.Error("关闭租户失败!", zap.Any("err", err))
		response.FailWithMessage(fmt.Sprintf("关闭租户失败, %s", err), c)
		return
	}
	response.Ok(c)
}

// 重启租户
func (s *cf) RestartTenant(c *gin.Context) {
	ConfigID := c.Query("ID")
	ID, err := strconv.Atoi(ConfigID)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}
	err = serviceConfig.RestartTenant(uint(ID))
	if err != nil {
		global.GVA_LOG.Error("重启租户失败!", zap.Any("err", err))
		response.FailWithMessage(fmt.Sprintf("重启租户失败, %s", err), c)
		return
	}
	response.Ok(c)
}

// 初始化服务器
func (s *cf) InitTenant(c *gin.Context) {
	ConfigID := c.Query("ID")
	ID, err := strconv.Atoi(ConfigID)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}
	err = serviceConfig.InitTenant(uint(ID))
	if err != nil {
		global.GVA_LOG.Error("初始化服务器失败!", zap.Any("err", err))
		response.FailWithMessage(fmt.Sprintf("初始化服务器失败,%s", err), c)
		return
	}
	response.OkWithMessage("初始化成功", c)
}

// 获取热更新列表
func (s *cf) GetAddOrUpdateTabelList(c *gin.Context) {
	list := serviceConfig.GetAddOrUpdateTabelList()
	response.OkWithDetailed(list, "获取成功", c)
}

// 热更新-表结构
func (s *cf) HotAddOrUpdateTable(c *gin.Context) {
	var req request.HotAddOrUpdateTable
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	err := serviceConfig.HotAddOrUpdateTable(req)
	if err != nil {
		global.GVA_LOG.Error("热更新失败!", zap.Any("err", err))
		response.FailWithMessage(fmt.Sprintf("热更新失败, %s", err), c)
		return
	}
	response.OkWithMessage("热更新成功", c)
}

// 热更新-表结构-历史记录
func (s *cf) GetAddOrUpdateTableHistoryList(c *gin.Context) {
	ConfigID := c.Query("ID")
	ID, err := strconv.Atoi(ConfigID)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}
	list, err := serviceConfig.GetAddOrUpdateTableHistoryList(uint(ID))
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Any("err", err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithDetailed(list, "获取成功", c)
}

// 热更新-菜单-获取列表
func (s *cf) GetHotMenuDataList(c *gin.Context) {
	ConfigID := c.Query("ID")
	ID, err := strconv.Atoi(ConfigID)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}
	data, err := serviceConfig.GetHotMenuDataList(uint(ID))
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Any("err", err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithDetailed(data, "获取成功", c)
}

// 热更新-API-获取数据
func (s *cf) GetHotApiDataList(c *gin.Context) {
	ConfigID := c.Query("ID")
	ID, err := strconv.Atoi(ConfigID)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	data, err := serviceConfig.GetHotApiDataList(uint(ID))
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Any("err", err))
		response.FailWithMessage(fmt.Sprintf("获取失败, %s", err), c)
		return
	}
	response.OkWithDetailed(data, "获取成功", c)
}

// 热更新-角色列表
func (s *cf) GetAllAuthority(c *gin.Context) {
	ConfigID := c.Query("ID")
	ID, err := strconv.Atoi(ConfigID)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}
	data, err := serviceConfig.GetAllAuthority(uint(ID))
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Any("err", err))
		response.FailWithMessage("获取失败", c)
		return
	}
	response.OkWithDetailed(data, "获取成功", c)
}

// 热更新-角色权限
func (s *cf) GetHotAuthority(c *gin.Context) {
	ConfigID := c.Query("ID")
	AuthorityID := c.Query("AuthorityID")

	ID, err := strconv.Atoi(ConfigID)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}
	AuthorityIDA, err := strconv.Atoi(AuthorityID)
	if err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}

	data, err := serviceConfig.GetHotAuthority(uint(ID), uint(AuthorityIDA))
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Any("err", err))
		response.FailWithMessage(fmt.Sprintf("获取失败, %s", err), c)
		return
	}
	response.OkWithDetailed(data, "获取成功", c)
}
