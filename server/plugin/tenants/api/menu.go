package api

import (
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/model/request"
	"github.com/gin-gonic/gin"
)

type MenuApi struct{}

func (q *MenuApi) SetMenuAuthority(c *gin.Context) {
	var req request.SetMenuAuthority
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}
	err := serviceMenu.SetMenuAuthority(req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithMessage("菜单设置成功", c)
}

func (q *MenuApi) GetAuthorityBtn(c *gin.Context) {
	var req request.GetAuthorityBtn
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}
	res, err := serviceMenu.GetAuthorityBtn(req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithData(res, c)
}

func (q *MenuApi) SetAuthorityBtn(c *gin.Context) {
	var req request.SetAuthorityBtn
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误", c)
		return
	}
	err := serviceMenu.SetAuthorityBtn(req)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	response.OkWithMessage("菜单设置成功", c)
}
