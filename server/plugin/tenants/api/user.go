package api

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/model"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/model/request"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

var User = new(use)

type use struct{}

// CreateUser 创建租户
// @Tags User
// @Summary 创建租户
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.User true "创建租户"
// @Success 200 {object} response.Response{msg=string} "创建成功"
// @Router /use/createUser [post]
func (a *use) CreateUser(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var info request.User
	err := c.ShouldBindJSON(&info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	var user model.User
	user.UserName = info.UserName
	user.Password = info.Password
	user.Enable = info.Enable

	err = serviceUser.CreateUser(ctx, &user)
	if err != nil {
		global.GVA_LOG.Error("创建失败!", zap.Error(err))
		response.FailWithMessage("创建失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("创建成功", c)
}

// DeleteUser 删除租户
// @Tags User
// @Summary 删除租户
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.User true "删除租户"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /use/deleteUser [delete]
func (a *use) DeleteUser(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	err := serviceUser.DeleteUser(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteUserByIds 批量删除租户
// @Tags User
// @Summary 批量删除租户
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /use/deleteUserByIds [delete]
func (a *use) DeleteUserByIds(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	IDs := c.QueryArray("IDs[]")
	err := serviceUser.DeleteUserByIds(ctx, IDs)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}

// UpdateUser 更新租户
// @Tags User
// @Summary 更新租户
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body model.User true "更新租户"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /use/updateUser [put]
func (a *use) UpdateUser(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var info request.User
	err := c.ShouldBindJSON(&info)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}

	var user model.User
	user.UUID = info.UUID
	user.UserName = info.UserName
	user.Password = info.Password
	user.Enable = info.Enable

	err = serviceUser.UpdateUser(ctx, user)
	if err != nil {
		global.GVA_LOG.Error("更新失败!", zap.Error(err))
		response.FailWithMessage("更新失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("更新成功", c)
}

// FindUser 用id查询租户
// @Tags User
// @Summary 用id查询租户
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param ID query uint true "用id查询租户"
// @Success 200 {object} response.Response{data=model.User,msg=string} "查询成功"
// @Router /use/findUser [get]
func (a *use) FindUser(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	ID := c.Query("ID")
	reuse, err := serviceUser.GetUser(ctx, ID)
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(reuse, c)
}

// GetUserList 分页获取租户列表
// @Tags User
// @Summary 分页获取租户列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query request.UserSearch true "分页获取租户列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /use/getUserList [get]
func (a *use) GetUserList(c *gin.Context) {
	// 创建业务用Context
	ctx := c.Request.Context()

	var pageInfo request.UserSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := serviceUser.GetUserInfoList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}
