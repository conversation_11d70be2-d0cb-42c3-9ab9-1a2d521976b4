package api

import "github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/service"

var (
	Api                 = new(api)
	serviceDatabase     = service.Service.Database
	serviceCasbin       = service.Service.Casbin
	serviceMenu         = service.Service.Menu
	serviceConfig       = service.Service.Config
	serviceUser         = service.Service.User
	serviceRenewalLease = service.Service.RenewalLease
	serviceTenant       = service.Service.Tenant
)

type api struct {
	Database     db
	Config       cf
	User         use
	RenewalLease rl
	CasbinApi    CasbinApi
	MenuApi      MenuApi
}
