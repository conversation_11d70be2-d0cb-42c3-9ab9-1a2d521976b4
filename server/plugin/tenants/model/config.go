package model

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// Config 租户配置 结构体
type Config struct {
	global.GVA_MODEL
	UserId                uint       `json:"user_id" form:"user_id" gorm:"column:user_id;comment:用户ID"`
	TenantId              string     `json:"tenant_id" form:"tenant_id" gorm:"column:tenant_id;uniqueIndex;comment:租户ID"`
	Description           string     `json:"description" form:"description" gorm:"column:description;comment:描述"`
	IsAutoStart           *bool      `json:"is_auto_start" form:"is_auto_start" gorm:"column:is_auto_start;comment:是否自启动"`
	DatabaseId            uint       `json:"database_id" form:"database_id" gorm:"column:database_id;comment:数据库ID"`
	Database              Database   `json:"database" form:"database" gorm:"foreignKey:DatabaseId;references:ID;comment:数据库"`
	DbName                string     `json:"db_name" form:"db_name" gorm:"column:db_name;comment:租户数据库名称"`
	AuthorityId           uint       `json:"authority_id" form:"authority_id" gorm:"column:authority_id;comment:角色ID"`
	IsCustomizedAuthority *bool      `json:"is_customized_authority" form:"is_customized_authority" gorm:"column:is_customized_authority;comment:是否自定义角色"`
	ExpirationDate        *time.Time `json:"expiration_date" form:"expiration_date" gorm:"column:expiration_date;comment:租户有效期"` //为空时为永久
	JWT
}

// TableName 租户配置 Config自定义表名 t_config
func (Config) TableName() string {
	return "t_config"
}

type JWT struct {
	SigningKey  string `json:"signing_key" gorm:"column:signing_key;"`
	BufferTime  string `json:"buffer_time" gorm:"column:buffer_time;"`
	ExpiresTime string `json:"expires_time" gorm:"column:expires_time;"`
	Issuer      string `json:"issuer" gorm:"column:issuer;"`
}

type TenantTable struct {
	StructName  string `json:"struct_name" gorm:"column:struct_name;"`
	Version     string `json:"version" gorm:"column:version;"`
	Description string `json:"description" gorm:"column:description;"`
	AutoMigrate any    `json:"-" gorm:"-"`
}
type TenantTableRecord struct {
	ConfigID    uint   `json:"config_id" form:"config_id" gorm:"column:config_id;comment:配置ID"`
	StructName  string `json:"struct_name" gorm:"column:struct_name;"`
	Version     string `json:"version" gorm:"column:version;"`
	Description string `json:"description" gorm:"column:description;"`
	AutoMigrate any    `json:"-" gorm:"-"`
}

// TableName 租户配置 Config自定义表名 t_tenant_table_record
func (TenantTableRecord) TableName() string {
	return "t_tenant_table_record"
}
