package model

import (
	"github.com/google/uuid"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
)

// User 租户 结构体
type User struct {
	global.GVA_MODEL
	UUID     uuid.UUID `json:"uuid" gorm:"index;comment:租户UUID"`                                                   // 用户UUID
	UserName *string   `json:"user_name" form:"user_name" gorm:"column:user_name;uniqueIndex;" binding:"required"` //租户登录名
	Password *string   `json:"-" form:"-" gorm:"column:password;" binding:"required"`                              //租户登录密码
	Enable   *bool     `json:"enable" form:"enable" gorm:"column:enable;" binding:"required"`                      //启用状态
}

// TableName 租户 User自定义表名 t_user
func (User) TableName() string {
	return "t_user"
}
