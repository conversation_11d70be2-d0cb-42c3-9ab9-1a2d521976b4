package request

import "github.com/flipped-aurora/gin-vue-admin/server/model/system"

type SetMenuAuthority struct {
	ConfigID    uint                 `json:"configID" binding:"required"`
	AuthorityId uint                 `json:"authorityId" binding:"required"` // 权限id
	Menus       []system.SysBaseMenu `json:"menus"`
}

type GetAuthorityBtn struct {
	ConfigID    uint `json:"configID" form:"configID" binding:"required"`
	AuthorityId uint `json:"authorityId" form:"authorityId" binding:"required"` // 权限id
	MenuID      uint `json:"menuID" form:"menuID" binding:"required"`
}
type SetAuthorityBtn struct {
	ConfigID    uint   `json:"configID" binding:"required"`
	AuthorityId uint   `json:"authorityId" binding:"required"` // 权限id
	MenuID      uint   `json:"menuID" binding:"required"`
	Selected    []uint `json:"selected"`
}
