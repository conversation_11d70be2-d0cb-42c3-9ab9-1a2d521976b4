package request

import (
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/model/common/request"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/model"
)

type ConfigSearch struct {
	request.PageInfo
	Sort  string `json:"sort" form:"sort"`
	Order string `json:"order" form:"order"`
}

// 热更新-表结构
type HotAddOrUpdateTable struct {
	ConfigID uint                `json:"configID" form:"configID" binding:"required"`
	Tables   []model.TenantTable `json:"tables" form:"tables" binding:"required"`
}

type Config struct {
	UserId                uint       `json:"user_id" form:"user_id" gorm:"column:user_id;comment:用户ID"`
	TenantId              string     `json:"tenant_id" form:"tenant_id" gorm:"column:tenant_id;comment:租户ID"`
	Description           string     `json:"description" form:"description" gorm:"column:description;comment:描述"`
	IsAutoStart           *bool      `json:"is_auto_start" form:"is_auto_start" gorm:"column:is_auto_start;comment:是否自启动"`
	DatabaseId            uint       `json:"database_id" form:"database_id" gorm:"column:database_id;comment:数据库ID"`
	DbName                string     `json:"db_name" form:"db_name" gorm:"column:db_name;comment:租户数据库名称"`
	AuthorityId           uint       `json:"authority_id" form:"authority_id" gorm:"column:authority_id;comment:角色ID"`
	IsCustomizedAuthority *bool      `json:"is_customized_authority" form:"is_customized_authority" gorm:"column:is_customized_authority;comment:是否自定义角色"`
	ExpirationDate        *time.Time `json:"expiration_date" form:"expiration_date" gorm:"column:expiration_date;comment:租户有效期"` //为空时为永久
	SigningKey    string `json:"signing_key" gorm:"column:signing_key;"`
	BufferTime    string `json:"buffer_time" gorm:"column:buffer_time;"`
	ExpiresTime   string `json:"expires_time" gorm:"column:expires_time;"`
	Issuer        string `json:"issuer" gorm:"column:issuer;"`
}

type EditConfig struct {
	ID uint `json:"ID" form:"ID" binding:"required"`
	// UserId                uint       `json:"user_id" form:"user_id" gorm:"column:user_id;comment:用户ID"`
	TenantId    string `json:"tenant_id" form:"tenant_id" gorm:"column:tenant_id;comment:租户ID"`
	Description string `json:"description" form:"description" gorm:"column:description;comment:描述"`
	IsAutoStart *bool  `json:"is_auto_start" form:"is_auto_start" gorm:"column:is_auto_start;comment:是否自启动"`
	// DatabaseId            uint       `json:"database_id" form:"database_id" gorm:"column:database_id;comment:数据库ID"`
	// DbName                string     `json:"db_name" form:"db_name" gorm:"column:db_name;comment:租户数据库名称"`
	AuthorityId           uint       `json:"authority_id" form:"authority_id" gorm:"column:authority_id;comment:角色ID"`
	IsCustomizedAuthority *bool      `json:"is_customized_authority" form:"is_customized_authority" gorm:"column:is_customized_authority;comment:是否自定义角色"`
	ExpirationDate        *time.Time `json:"expiration_date" form:"expiration_date" gorm:"column:expiration_date;comment:租户有效期"` //为空时为永久
	SigningKey    string `json:"signing_key" gorm:"column:signing_key;"`
	BufferTime    string `json:"buffer_time" gorm:"column:buffer_time;"`
	ExpiresTime   string `json:"expires_time" gorm:"column:expires_time;"`
	Issuer        string `json:"issuer" gorm:"column:issuer;"`
}
