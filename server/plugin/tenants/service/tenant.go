package service

import (
	"errors"
	"fmt"
	"strconv"
	"sync"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system/response"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/config"
	globalTenant "github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/global"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/model"
	"github.com/google/uuid"

	"gorm.io/driver/mysql"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

type tenant struct {
	MainDatabase model.Database
	Config       model.Config
}

var TenantService = new(tenant)

func (t *tenant) NewTenant(ConfigID uint) *tenant {
	var con model.Config
	err := global.GVA_DB.Where("id = ?", ConfigID).First(&con).Error
	if err != nil {
		return nil

	}
	var DBinfo model.Database
	err = global.GVA_DB.Where("id = ?", con.DatabaseId).First(&DBinfo).Error
	if err != nil {
		return nil
	}
	con.DbName = globalTenant.TENANT_DB_NAME + con.DbName
	return &tenant{
		MainDatabase: DBinfo,
		Config:       con,
	}
}

// 租户-关闭DB
func (t *tenant) CloseDB() error {
	globalDB := globalTenant.GetTenantDB(t.Config.TenantId)
	if globalDB == nil {
		return nil
	}

	rawDB, err := globalDB.DB()
	if err != nil {
		return fmt.Errorf("关闭全局租户数据库失败：%v", err)
	}

	err = rawDB.Close()
	if err != nil {
		return fmt.Errorf("关闭全局租户数据库失败：%v", err)
	}
	globalTenant.DeleteTenantDB(t.Config.TenantId)
	return nil
}

// 租户-打开DB
func (t *tenant) OpenDB(DisableForeignKeyConstraintWhenMigrating bool) error {
	if t.Config.ExpirationDate != nil {
		if t.Config.ExpirationDate.Before(time.Now()) {
			return fmt.Errorf("数据库已过期")
		}
	}

	DB := globalTenant.GetTenantDB(t.Config.TenantId)
	if DB != nil {
		return nil
	}

	switch *t.MainDatabase.Type {
	case "mysql":
		return t.OpenDBMysql(DisableForeignKeyConstraintWhenMigrating)
	case "pgsql":
		return t.OpenDBPgsql(DisableForeignKeyConstraintWhenMigrating)
	default:
		return fmt.Errorf("暂不支持%v数据库", *t.MainDatabase.Type)
	}
}

func (t *tenant) OpenDBMysql(DisableForeignKeyConstraintWhenMigrating bool) (err error) {
	d := t.MainDatabase
	d.Dbname = &t.Config.DbName
	mysqlConfig := mysql.Config{
		DSN:                       config.DnsConfig.GetDns(&d), // DSN data source name
		DefaultStringSize:         256,                         // string 类型字段的默认长度
		DisableDatetimePrecision:  true,                        // 禁用 datetime 精度，MySQL 5.6 之前的数据库不支持
		DontSupportRenameIndex:    true,                        // 重命名索引时采用删除并新建的方式，MySQL 5.7 之前的数据库和 MariaDB 不支持重命名索引
		DontSupportRenameColumn:   true,                        // 用 `change` 重命名列，MySQL 8 之前的数据库和 MariaDB 不支持重命名列
		SkipInitializeWithVersion: false,                       // 根据当前 MySQL 版本自动配置
	}
	DB, err := gorm.Open(mysql.New(mysqlConfig), &gorm.Config{
		DisableForeignKeyConstraintWhenMigrating: DisableForeignKeyConstraintWhenMigrating})
	if err != nil {
		return fmt.Errorf("连接数据库失败, %s", err)
	}
	globalTenant.SetTenantDB(t.Config.TenantId, DB)
	return nil
}

func (t *tenant) OpenDBPgsql(DisableForeignKeyConstraintWhenMigrating bool) (err error) {
	d := t.MainDatabase
	d.Dbname = &t.Config.DbName

	pgsqlConfig := postgres.Config{
		DSN:                  config.DnsConfig.GetDns(&d),
		PreferSimpleProtocol: false,
	}
	DB, err := gorm.Open(postgres.New(pgsqlConfig), &gorm.Config{
		DisableForeignKeyConstraintWhenMigrating: DisableForeignKeyConstraintWhenMigrating})
	if err != nil {
		return fmt.Errorf("连接租户数据库失败, %s", err)
	}
	globalTenant.SetTenantDB(t.Config.TenantId, DB)

	return nil

}

// 租户-打开Casbin
func (t *tenant) OpenCasbin() (err error) {
	DB := globalTenant.GetTenantDB(t.Config.TenantId)
	if DB == nil {
		return fmt.Errorf("casbin:租户数据库未打开")
	}

	err = CasbinServiceApp.AddCasbin(DB, t.Config.TenantId)
	if err != nil {
		return fmt.Errorf("casbin初始化失败, %s", err)
	}
	return nil
}

// 租户-打开JWT
func (t *tenant) OpenJwt() (err error) {
	if t.Config.SigningKey == "" || t.Config.Issuer == "" || t.Config.ExpiresTime == "" || t.Config.BufferTime == "" {
		return fmt.Errorf("请先配置JWT")
	}

	//JWT,存入全局变量
	jwt := globalTenant.JWT{
		SigningKey:  t.Config.SigningKey,
		ExpiresTime: t.Config.ExpiresTime,
		BufferTime:  t.Config.BufferTime,
		Issuer:      t.Config.Issuer,
	}
	globalTenant.SetTenantJWT(t.Config.TenantId, jwt)

	return nil
}

// 租户-创建数据库
func (t *tenant) CreateTenantDatabase() error {
	switch *t.MainDatabase.Type {
	case "mysql":
		return t.CreateTenantDatabaseMysql()
	case "pgsql":
		return t.CreateTenantDatabasePgsql()
	default:
		return fmt.Errorf("暂不支持%v数据库", *t.MainDatabase.Type)
	}
}

func (t *tenant) CreateTenantDatabaseMysql() error {
	d := t.MainDatabase
	mysqlConfig := mysql.Config{
		DSN:                       config.DnsConfig.GetDns(&d), // DSN data source name
		DefaultStringSize:         256,                         // string 类型字段的默认长度
		DisableDatetimePrecision:  true,                        // 禁用 datetime 精度，MySQL 5.6 之前的数据库不支持
		DontSupportRenameIndex:    true,                        // 重命名索引时采用删除并新建的方式，MySQL 5.7 之前的数据库和 MariaDB 不支持重命名索引
		DontSupportRenameColumn:   true,                        // 用 `change` 重命名列，MySQL 8 之前的数据库和 MariaDB 不支持重命名列
		SkipInitializeWithVersion: false,                       // 根据当前 MySQL 版本自动配置
	}
	DB, err := gorm.Open(mysql.New(mysqlConfig))
	if err != nil {
		return fmt.Errorf("连接数据库失败, %s", err)
	}
	rawDB, err := DB.DB()
	if err != nil {
		return fmt.Errorf("连接主数据库失败, %s", err)
	}
	defer rawDB.Close()
	createDBQuery := fmt.Sprintf("CREATE DATABASE IF NOT EXISTS %s CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci", t.Config.DbName)
	if err = DB.Exec(createDBQuery).Error; err != nil {
		return fmt.Errorf("创建数据库失败, %s", err)
	}
	return nil
}

func (t *tenant) CreateTenantDatabasePgsql() error {
	pgsqlConfig := postgres.Config{
		DSN:                  config.DnsConfig.GetDns(&t.MainDatabase),
		PreferSimpleProtocol: false,
	}
	DB, err := gorm.Open(postgres.New(pgsqlConfig))
	if err != nil {
		return fmt.Errorf("连接主数据库失败, %s", err)
	}
	rawDB, err := DB.DB()
	if err != nil {
		return fmt.Errorf("连接主数据库失败, %s", err)
	}
	defer rawDB.Close()

	if err = DB.Exec(fmt.Sprintf("CREATE DATABASE %s;", t.Config.DbName)).Error; err != nil {
		return fmt.Errorf("创建租户数据库失败, %s", err)
	}
	return nil
}

// 租户-清空数据库
func (t *tenant) ClearTenantDB() error {
	switch *t.MainDatabase.Type {
	case "mysql":
		return t.ClearTenantDBMysql()
	case "pgsql":
		return t.ClearTenantDBPgsql()
	default:
		return fmt.Errorf("暂不支持%v数据库", *t.MainDatabase.Type)
	}
}

func (t *tenant) ClearTenantDBMysql() error {
	DB := globalTenant.GetTenantDB(t.Config.TenantId)
	if DB == nil {
		return fmt.Errorf("清空数据库失败，租户数据库未打开")
	}
	if err := DB.Exec("SET FOREIGN_KEY_CHECKS = 0").Error; err != nil {
		return fmt.Errorf("禁用外键检查失败, %v", err)
	}
	var tables []string
	err := DB.Raw("SHOW TABLES").Scan(&tables).Error
	if err != nil {
		return fmt.Errorf("获取表列表失败: %v", err)
	}
	for _, table := range tables {
		DB.Exec("DROP TABLE IF EXISTS " + table)
	}
	// 启用外键检查
	if err := DB.Exec("SET FOREIGN_KEY_CHECKS = 1").Error; err != nil {
		return fmt.Errorf("启用外键检查失败, %v", err)
	}
	return nil
}
func (t *tenant) ClearTenantDBPgsql() error {
	DB := globalTenant.GetTenantDB(t.Config.TenantId)
	if DB == nil {
		return fmt.Errorf("清空数据库失败，租户数据库未打开")
	}

	// 获取所有表名（默认模式 public）
	var tables []string
	err := DB.Raw("SELECT tablename FROM pg_tables WHERE schemaname = 'public'").Scan(&tables).Error
	if err != nil {
		return fmt.Errorf("获取表列表失败: %v", err)
	}

	// 按顺序 TRUNCATE 表（注意外键依赖）
	for _, table := range tables {
		// 使用 CASCADE 以级联清空有外键引用的表
		if err := DB.Exec("TRUNCATE TABLE " + table + " RESTART IDENTITY CASCADE").Error; err != nil {
			return fmt.Errorf("清空表 %s 失败: %v", table, err)
		}
	}
	return nil
}

// 租户-自动迁移
func (t *tenant) TenantAutoMigrate() (err error) {
	err = t.CloseDB()
	if err != nil {
		return err
	}

	err = t.OpenDB(true)
	if err != nil {
		return err
	}
	DB := globalTenant.GetTenantDB(t.Config.TenantId)
	if DB == nil {
		return fmt.Errorf("自动迁移，打开数据库失败")
	}

	var autoMigrate []any
	for _, v := range config.BaseAutoMigrate {
		autoMigrate = append(autoMigrate, v.AutoMigrate)
	}

	err = DB.AutoMigrate(autoMigrate...)
	if err != nil {
		return fmt.Errorf("自动迁移数据库失败：%v", err)
	}
	err = t.CloseDB()
	if err != nil {
		return err
	}

	return nil
}

// 拷贝Casbin
func (t *tenant) CopyCasbin() (err error) {
	//拷贝关联Casbin
	gva_casbin := CasbinServiceApp.GetCasbin(globalTenant.ADMIN_DB_NAME)
	Tenant_casbin := CasbinServiceApp.GetCasbin(t.Config.TenantId)
	if gva_casbin == nil || Tenant_casbin == nil {
		return fmt.Errorf("获取Casbin失败")
	}

	ID := strconv.Itoa(int(t.Config.AuthorityId))
	Tenant_casbin.RemoveFilteredPolicy(0, ID)
	lise, _ := gva_casbin.GetFilteredPolicy(0, ID)
	success, _ := Tenant_casbin.AddPolicies(lise)
	if !success {
		return fmt.Errorf("同步Casbin失败")
	}
	return nil
}

// 拷贝服务商菜单相关
func (t *tenant) CopyServiceMenu(tx *gorm.DB) (err error) {
	var SysBaseMenu []system.SysBaseMenu
	var SysBaseMenuParameter []system.SysBaseMenuParameter
	var SysBaseMenuBtn []system.SysBaseMenuBtn

	ServeDB := globalTenant.GetTenantDB(globalTenant.ADMIN_DB_NAME)
	if ServeDB == nil {
		return fmt.Errorf("获取服务商数据库失败")
	}

	ServeDB.Unscoped().Find(&SysBaseMenu)
	ServeDB.Unscoped().Find(&SysBaseMenuParameter)
	ServeDB.Unscoped().Find(&SysBaseMenuBtn)

	//拷贝到租户数据库
	if len(SysBaseMenu) > 0 {
		err = tx.Table("sys_base_menus").Create(&SysBaseMenu).Error
		if err != nil {
			return fmt.Errorf("创建菜单失败: %v", err)
		}
	}
	if len(SysBaseMenuParameter) > 0 {
		err = tx.Table("sys_base_menu_parameters").Create(&SysBaseMenuParameter).Error
		if err != nil {
			return fmt.Errorf("创建菜单参数失败: %v", err)
		}
	}
	if len(SysBaseMenuBtn) > 0 {
		err = tx.Table("sys_base_menu_btns").Create(&SysBaseMenuBtn).Error
		if err != nil {
			return fmt.Errorf("创建菜单按钮失败: %v", err)
		}
	}
	return nil
}

// 拷贝服务商api
func (t *tenant) CopyServiceApi(tx *gorm.DB) (err error) {
	var SysApi []system.SysApi
	ServeDB := globalTenant.GetTenantDB(globalTenant.ADMIN_DB_NAME)
	if ServeDB == nil {
		return fmt.Errorf("获取服务商数据库失败")
	}
	ServeDB.Unscoped().Find(&SysApi)
	if len(SysApi) == 0 {
		return nil
	}
	err = tx.Create(&SysApi).Error
	if err != nil {
		return fmt.Errorf("创建api失败: %v", err)
	}
	return nil
}

// 拷贝服务商角色 及 角色关联
func (t *tenant) CopyServiceAuthority(tx *gorm.DB, AuthorityId uint) (err error) {
	//拷贝角色
	var SysAuthority system.SysAuthority
	ServeDB := globalTenant.GetTenantDB(globalTenant.ADMIN_DB_NAME)
	if ServeDB == nil {
		return fmt.Errorf("获取服务商数据库失败")
	}
	err = ServeDB.Where("authority_id = ?", AuthorityId).First(&SysAuthority).Error
	if err != nil {
		return fmt.Errorf("查询角色失败: %v", err)
	}
	err = tx.Create(&SysAuthority).Error
	if err != nil {
		return fmt.Errorf("创建角色失败: %v", err)
	}

	//拷贝关联菜单
	type SysAuthorityMenu struct {
		AuthorityId uint `gorm:"column:sys_base_menu_id"`
		BaseMenuId  uint `gorm:"column:sys_authority_authority_id"`
	}

	var sysAuthorityMenu []SysAuthorityMenu
	err = ServeDB.Table("sys_authority_menus").Where("sys_authority_authority_id = ?", AuthorityId).Find(&sysAuthorityMenu).Error
	if err != nil {
		return fmt.Errorf("查询角色菜单失败: %v", err)
	}
	if len(sysAuthorityMenu) > 0 {
		err = tx.Table("sys_authority_menus").Create(&sysAuthorityMenu).Error
		if err != nil {
			return fmt.Errorf("创建角色菜单失败: %v", err)
		}
	}

	//拷贝关联按钮
	type SysAuthorityBtn struct {
		AuthorityId uint `gorm:"column:authority_id"`
		BaseMenuId  uint `gorm:"column:sys_menu_id"`
		BtnId       uint `gorm:"column:sys_base_menu_btn_id"`
	}
	var sysAuthorityBtn []SysAuthorityBtn
	err = tx.Table("sys_authority_btns").Where("authority_id = ?", AuthorityId).Find(&sysAuthorityBtn).Error
	if err != nil {
		return fmt.Errorf("查询角色按钮失败: %v", err)
	}
	if len(sysAuthorityBtn) > 0 {
		err = tx.Table("sys_authority_btns").Create(&sysAuthorityBtn).Error
		if err != nil {
			return fmt.Errorf("创建角色按钮失败: %v", err)
		}
	}

	// 从关联的租户用户获取用户名和密码
	var tenantUser model.User
	if err = global.GVA_DB.Where("id = ?", t.Config.UserId).First(&tenantUser).Error; err != nil {
		return fmt.Errorf("获取租户用户信息失败: %v", err)
	}

	if tenantUser.UserName == nil || *tenantUser.UserName == "" {
		return fmt.Errorf("租户用户名为空")
	}
	if tenantUser.Password == nil || *tenantUser.Password == "" {
		return fmt.Errorf("租户密码为空")
	}

	//创建用户
	if errors.Is(tx.First(&system.SysUser{}, "username = ?", *tenantUser.UserName).Error, gorm.ErrRecordNotFound) {
		user := system.SysUser{
			UUID:        uuid.New(),
			Username:    *tenantUser.UserName,
			Password:    *tenantUser.Password, // 租户用户密码已经是加密的
			NickName:    "系统管理员",
			AuthorityId: AuthorityId,
			Authorities: []system.SysAuthority{
				{
					AuthorityId: AuthorityId,
				},
			},
			Phone: "18888888888",
		}

		if err = tx.Create(&user).Error; err != nil {
			return fmt.Errorf("创建用户失败: %v", err)
		}

		// 创建对应的全局用户记录
		// 生成默认邮箱地址，避免空字符串导致唯一索引冲突
		defaultEmail := fmt.Sprintf("%s@%s.local", *tenantUser.UserName, t.Config.TenantId)
		globalUser := system.GlobalUser{
			Username: *tenantUser.UserName,
			Password: *tenantUser.Password, // 租户用户密码已经是加密的
			Email:    defaultEmail,
			TenantId: t.Config.TenantId,
			IsActive: true,
			UUID:     uuid.New(),
		}

		if err = global.GVA_DB.Create(&globalUser).Error; err != nil {
			return fmt.Errorf("创建全局用户失败: %v", err)
		}

		// 建立用户租户关系
		relation := system.UserTenantRelation{
			GlobalUserId: globalUser.ID,
			TenantId:     t.Config.TenantId,
			TenantUserId: user.ID,
			IsPrimary:    true,
		}

		if err = global.GVA_DB.Create(&relation).Error; err != nil {
			return fmt.Errorf("创建用户租户关系失败: %v", err)
		}
	}

	return nil
}

// 租户-打开租户服务器
func (t *tenant) OpenTenant() (err error) {
	if err = t.OpenDB(false); err != nil {
		return fmt.Errorf("打开%v数据库失败: %v", t.Config.TenantId, err)
	}
	if err = t.OpenCasbin(); err != nil {
		return fmt.Errorf("打开%v casbin失败: %v", t.Config.TenantId, err)
	}
	if err = t.OpenJwt(); err != nil {
		return fmt.Errorf("打开%v jwt失败: %v", t.Config.TenantId, err)
	}
	return nil
}

// 租户-关闭租户服务器
func (t *tenant) CloseTenant() (err error) {
	if err = t.CloseDB(); err != nil {
		return fmt.Errorf("关闭数据库失败: %v", err)
	}
	globalTenant.DeleteTenantJWT(t.Config.TenantId)
	CasbinServiceApp.DeleteCasbin(t.Config.TenantId)
	return nil
}

// 租户-重启租户服务器
func (t *tenant) RestartTenant() (err error) {
	if err = t.CloseTenant(); err != nil {
		return fmt.Errorf("重启租户失败: %v", err)
	}
	if err = t.OpenTenant(); err != nil {
		return fmt.Errorf("重启租户失败: %v", err)
	}
	return nil
}

// 租户-初始化
func (t *tenant) TenantInit() (err error) {
	// 先清理可能存在的租户实例，避免重复添加错误
	CasbinServiceApp.DeleteCasbin(t.Config.TenantId)
	globalTenant.DeleteTenantJWT(t.Config.TenantId)

	err = t.OpenDB(false)
	if err == nil {
		err = t.ClearTenantDB()
		if err != nil {
			return fmt.Errorf("初始化：清空数据库失败: %v", err)
		}
	} else {
		err = t.CreateTenantDatabase()
		if err != nil {
			return fmt.Errorf("初始化：创建数据库失败: %v", err)
		}
	}

	err = t.TenantAutoMigrate()
	if err != nil {
		return fmt.Errorf("初始化：自动迁移失败: %v", err)
	}

	err = t.OpenTenant()
	if err != nil {
		return fmt.Errorf("初始化：打开租户服务器失败: %v", err)
	}

	DB := globalTenant.GetTenantDB(t.Config.TenantId)
	if DB == nil {
		return fmt.Errorf("初始化：获取数据库失败")
	}

	//拷贝数据
	err = t.CopyCasbin()
	if err != nil {
		return fmt.Errorf("初始化：拷贝casbin失败: %v", err)
	}

	err = DB.Transaction(func(tx *gorm.DB) (err error) {
		if err = t.CopyServiceMenu(tx); err != nil {
			return fmt.Errorf("初始化：拷贝菜单失败: %v", err)
		}
		if err = t.CopyServiceApi(tx); err != nil {
			return fmt.Errorf("初始化：拷贝api失败: %v", err)
		}
		if err = t.CopyServiceAuthority(tx, t.Config.AuthorityId); err != nil {
			return fmt.Errorf("初始化：拷贝角色失败: %v", err)
		}
		return nil
	})
	if err != nil {
		return err
	}
	return nil
}

// AutoStartTenant 是否自启动
func (t *tenant) AutoStartTenant() {
	db := globalTenant.GetTenantDB(globalTenant.ADMIN_DB_NAME)
	if db == nil {
		fmt.Println("自启动:获取服务商数据库失败")
		return
	}

	var list []model.Config
	err := db.Model(&model.Config{}).Where("is_auto_start = ?", true).Preload("Database").Find(&list).Error
	if err != nil {
		fmt.Println("自启动:查询租户配置失败")
		return
	}
	var wg sync.WaitGroup
	for _, v := range list {
		wg.Add(1)
		config := v
		config.DbName = globalTenant.TENANT_DB_NAME + config.DbName
		newTenant := &tenant{
			Config:       config,
			MainDatabase: config.Database,
		}

		go func(tenantObj *tenant) {
			defer wg.Done()
			tenantObj.OpenTenant()
		}(newTenant)
	}
	wg.Wait()

}

// 热更新- 表结构
func (t *tenant) AddOrUpdateTable(tables []model.TenantTable) (err error) {
	db := globalTenant.GetTenantDB(t.Config.TenantId)
	if db == nil {
		return fmt.Errorf("自动迁移:数据库未开启")
	}

	var records []model.TenantTableRecord
	var autoMigrate []any
	for _, v := range tables {
		autoMigrat, ok := config.AddOrUpdateAutoMigrate[v.StructName]
		if ok {
			autoMigrate = append(autoMigrate, autoMigrat.AutoMigrate)
			records = append(records, model.TenantTableRecord{
				ConfigID:    t.Config.ID,
				StructName:  v.StructName,
				Version:     v.Version,
				Description: v.Description,
			})
		}
	}
	if err = db.AutoMigrate(autoMigrate...); err != nil {
		return fmt.Errorf("热更新：添加或更新失败: %v", err)
	}

	gvadb := globalTenant.GetTenantDB(globalTenant.ADMIN_DB_NAME)
	if gvadb == nil {
		return fmt.Errorf("热更新，保存记录失败")
	}
	for _, v := range records {
		var r model.TenantTableRecord
		err = gvadb.Where("tenant_id = ? and struct_name = ?", t.Config.TenantId, v.StructName).First(&r).Error
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				if err = gvadb.Create(&v).Error; err != nil {
					return fmt.Errorf("热更新：创建或更新失败: %v", err)
				}
			}
		}
		if err = gvadb.Where("tenant_id = ? and struct_name = ?", t.Config.TenantId, v.StructName).Omit("tenant_id", "struct_name").Updates(&v).Error; err != nil {
			return fmt.Errorf("热更新：更新失败: %v", err)
		}
	}

	return nil
}

// 热更新-表结构-历史记录
func (t *tenant) GetAddOrUpdateTableHistoryList() (list []model.TenantTableRecord, err error) {
	gvadb := globalTenant.GetTenantDB(globalTenant.ADMIN_DB_NAME)
	if gvadb == nil {
		return nil, fmt.Errorf("热更新，获取记录失败")
	}
	err = gvadb.Where("tenant_id = ?", t.Config.TenantId).Find(&list).Error
	if err != nil {
		return nil, fmt.Errorf("热更新，获取记录失败: %v", err)
	}
	return
}

// 获取数据库 所有菜单
func (t *tenant) GetAllMenu(TenantID string) (SysBaseMenu []system.SysBaseMenu, err error) {
	db := globalTenant.GetTenantDB(TenantID)
	if db == nil {
		err = fmt.Errorf("获取数据库失败")
		return
	}

	err = db.Order("sort").Preload("MenuBtn").Find(&SysBaseMenu).Error
	return
}

// 获取数据库 所有api
func (t *tenant) GetAllApi(TenantID string) (SysBaseMenu []system.SysApi, err error) {
	db := globalTenant.GetTenantDB(TenantID)
	if db == nil {
		err = fmt.Errorf("获取数据库失败")
		return
	}

	err = db.Find(&SysBaseMenu).Error
	return
}

// 获取租户角色
func (t *tenant) GetAllAuthority(TenantID string) (SysBaseMenu []system.SysAuthority, err error) {
	db := globalTenant.GetTenantDB(TenantID)
	if db == nil {
		err = fmt.Errorf("获取数据库失败")
		return
	}

	err = db.Find(&SysBaseMenu).Error
	return
}

// 获取角色权限-菜单
func (t *tenant) GetMenuAuthority(TenantID string, AuthorityID uint) (menus []system.SysMenu, err error) {
	db := globalTenant.GetTenantDB(TenantID)
	if db == nil {
		err = fmt.Errorf("获取数据库失败")
		return
	}

	var baseMenu []system.SysBaseMenu
	var SysAuthorityMenus []system.SysAuthorityMenu
	err = db.Where("sys_authority_authority_id = ?", AuthorityID).Find(&SysAuthorityMenus).Error
	if err != nil {
		return menus, err
	}
	var MenuIds []string

	for i := range SysAuthorityMenus {
		MenuIds = append(MenuIds, SysAuthorityMenus[i].MenuId)
	}
	err = db.Where("id in (?) ", MenuIds).Order("sort").Find(&baseMenu).Error

	for i := range baseMenu {
		menus = append(menus, system.SysMenu{
			SysBaseMenu: baseMenu[i],
			AuthorityId: AuthorityID,
			MenuId:      baseMenu[i].ID,
			Parameters:  baseMenu[i].Parameters,
		})
	}
	return menus, err
}

// 获取角色权限-api
func (t *tenant) GetApiAuthority(TenantID string, AuthorityID uint) (pathMaps []request.CasbinInfo, err error) {
	e := CasbinServiceApp.GetCasbin(TenantID)
	if e == nil {
		err = fmt.Errorf("获取casbin失败")
		return
	}
	authorityId := strconv.Itoa(int(AuthorityID))
	list, _ := e.GetFilteredPolicy(0, authorityId)
	for _, v := range list {
		pathMaps = append(pathMaps, request.CasbinInfo{
			Path:   v[1],
			Method: v[2],
		})
	}
	return pathMaps, nil
}

// 热更新-租户--角色-菜单
func (t *tenant) SetMenuAuthority(TenantID string, AuthorityID uint, Menus []system.SysBaseMenu) (err error) {
	db := globalTenant.GetTenantDB(TenantID)
	if db == nil {
		err = fmt.Errorf("获取数据库失败")
		return
	}
	var s system.SysAuthority
	db.Preload("SysBaseMenus").First(&s, "authority_id = ?", AuthorityID)
	err = db.Model(&s).Association("SysBaseMenus").Replace(&Menus)
	return
}

// 热更新-租户-获取按钮
func (t *tenant) GetAuthorityBtn(TenantID string, AuthorityID uint, menuID uint) (res response.SysAuthorityBtnRes, err error) {
	db := globalTenant.GetTenantDB(TenantID)
	if db == nil {
		err = fmt.Errorf("获取数据库失败")
		return
	}
	var authorityBtn []system.SysAuthorityBtn
	err = db.Find(&authorityBtn, "authority_id = ? and sys_menu_id = ?", AuthorityID, menuID).Error
	if err != nil {
		return
	}
	var selected []uint
	for _, v := range authorityBtn {
		selected = append(selected, v.SysBaseMenuBtnID)
	}
	res.Selected = selected
	return res, err
}

// 热更新-租户-角色-菜单-按钮
func (t *tenant) SetAuthorityBtn(TenantID string, AuthorityID uint, menuID uint, btnIds []uint) (err error) {
	db := globalTenant.GetTenantDB(TenantID)
	if db == nil {
		err = fmt.Errorf("获取数据库失败")
		return
	}

	return db.Transaction(func(tx *gorm.DB) error {
		var authorityBtn []system.SysAuthorityBtn
		err = tx.Delete(&[]system.SysAuthorityBtn{}, "authority_id = ? and sys_menu_id = ?", AuthorityID, menuID).Error
		if err != nil {
			return err
		}
		for _, v := range btnIds {
			authorityBtn = append(authorityBtn, system.SysAuthorityBtn{
				AuthorityId:      AuthorityID,
				SysMenuID:        menuID,
				SysBaseMenuBtnID: v,
			})
		}
		if len(authorityBtn) > 0 {
			err = tx.Create(&authorityBtn).Error
		}
		if err != nil {
			return err
		}
		return nil

	})
}
