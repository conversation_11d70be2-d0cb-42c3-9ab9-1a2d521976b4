package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	sys_global "github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/config"
	globalTenant "github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/global"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/model"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/model/request"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/model/response"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/utils"

	"gorm.io/gorm"
)

var Config = new(cf)

type cf struct{}

func (s *cf) GetConfigPublic(ctx context.Context) {

}

// 新增租户配置
func (s *cf) CreateConfig(ctx context.Context, cf model.Config) error {
	if !errors.Is(sys_global.GVA_DB.Take(&model.Config{}, "tenant_id = ?", cf.TenantId).Error, gorm.ErrRecordNotFound) {
		return fmt.Errorf("租户ID重复或其他错误")
	}
	return sys_global.GVA_DB.Create(&cf).Error
}

// 修改租户配置
func (s *cf) EditConfig(cf model.Config) error {
	return sys_global.GVA_DB.Where("id = ?", cf.ID).Updates(&cf).Error
}

// GetConfigList 分页获取配置列表
func (s *cf) GetConfigList(info request.ConfigSearch) (list []response.Config, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := sys_global.GVA_DB.Model(&response.Config{}).Preload("Database")

	err = db.Count(&total).Error
	if err != nil {
		return
	}
	db = db.Order("created_at DESC")
	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}

	err = db.Find(&list).Error
	if err != nil {
		return
	}

	for i := range list {
		list[i].IsRunning = globalTenant.GetTenantDB(list[i].TenantId) != nil
	}
	return list, total, err
}

// 打开租户
func (s *cf) OpenTenant(ConfigID uint) (err error) {
	t := TenantService.NewTenant(ConfigID)
	return t.OpenTenant()
}

// 关闭租户
func (s *cf) CloseTenant(ConfigID uint) (err error) {
	t := TenantService.NewTenant(ConfigID)
	return t.CloseTenant()
}

// 重启租户
func (s *cf) RestartTenant(ConfigID uint) (err error) {
	t := TenantService.NewTenant(ConfigID)
	return t.RestartTenant()
}

// 初始化租户数据库
func (s *cf) InitTenant(ConfigID uint) (err error) {
	t := TenantService.NewTenant(ConfigID)
	return t.TenantInit()
}

// 租户配置增加时长
func (s *cf) AddConfigExpirationDate(cfID uint, yy int, mm int, dd int) (err error) {
	var cf model.Config
	err = sys_global.GVA_DB.First(&cf, cfID).Error
	if err != nil {
		return fmt.Errorf("查询配置失败: %v", err)
	}
	if cf.ExpirationDate == nil {
		return fmt.Errorf("永久时长，无需增加")
	}
	// 获取当前时间，且去除时分秒
	loc, _ := time.LoadLocation("Asia/Shanghai")
	now := time.Now()
	now = time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, loc)

	if cf.ExpirationDate.Before(now) {
		// 如果时长已过期，则从今日起增加时长
		cf.ExpirationDate = &now
	}
	// 增加指定的年、月、日
	newTime := cf.ExpirationDate.AddDate(yy, mm, dd)
	cf.ExpirationDate = &newTime
	// 更新数据库中的配置信息
	return sys_global.GVA_DB.Model(&cf).Update("expiration_date", newTime).Error
}

// 获取热更新列表
func (s *cf) GetAddOrUpdateTabelList() (list []model.TenantTable) {
	for _, v := range config.AddOrUpdateAutoMigrate {
		list = append(list, v)
	}
	return list
}

// 热更新-表结构
func (s *cf) HotAddOrUpdateTable(req request.HotAddOrUpdateTable) (err error) {
	t := TenantService.NewTenant(req.ConfigID)
	return t.AddOrUpdateTable(req.Tables)
}

// 热更新-表结构-历史记录
func (s *cf) GetAddOrUpdateTableHistoryList(ConfigID uint) ([]model.TenantTableRecord, error) {
	t := TenantService.NewTenant(ConfigID)
	return t.GetAddOrUpdateTableHistoryList()
}

// 热更新-菜单
func (s *cf) GetHotMenuDataList(ConfigID uint) (any, error) {
	t := TenantService.NewTenant(ConfigID)

	sysBaseMenu, err := t.GetAllMenu(globalTenant.ADMIN_DB_NAME)
	if err != nil {
		return nil, err
	}
	tSysBaseMenu, err := t.GetAllMenu(t.Config.TenantId)
	if err != nil {
		return nil, err
	}
	sysBaseMenu = utils.BuildMenuTree(sysBaseMenu, 0)
	tSysBaseMenu = utils.BuildMenuTree(tSysBaseMenu, 0)

	data := map[string]any{
		"gvaMenu":    sysBaseMenu,
		"tenantMenu": tSysBaseMenu,
	}
	return data, nil
}

// 热更新-api
func (s *cf) GetHotApiDataList(ConfigID uint) (any, error) {
	t := TenantService.NewTenant(ConfigID)

	sysBaseApi, err := t.GetAllApi(globalTenant.ADMIN_DB_NAME)
	if err != nil {
		return nil, err
	}
	tSysBaseApi, err := t.GetAllApi(t.Config.TenantId)
	if err != nil {
		return nil, err
	}
	data := map[string]any{
		"gvaApi":    sysBaseApi,
		"tenantApi": tSysBaseApi,
	}
	return data, nil
}

// 热更新-数据库角色
func (s *cf) GetAllAuthority(ConfigID uint) (any, error) {
	t := TenantService.NewTenant(ConfigID)
	return t.GetAllAuthority(t.Config.TenantId)
}

// 热更新-获取角色权限
func (s *cf) GetHotAuthority(ConfigID uint, AuthorityID uint) (any, error) {
	t := TenantService.NewTenant(ConfigID)
	baseMenu, _ := t.GetAllMenu(t.Config.TenantId)
	baseApi, _ := t.GetAllApi(t.Config.TenantId)
	AuthorityMenu, _ := t.GetMenuAuthority(t.Config.TenantId, AuthorityID)
	AuthorityApi, _ := t.GetApiAuthority(t.Config.TenantId, AuthorityID)

	baseMenu = utils.BuildMenuTree(baseMenu, 0)

	data := map[string]any{
		"baseMenu":   baseMenu,
		"baseApi":    baseApi,
		"tenantMenu": AuthorityMenu,
		"tenantApi":  AuthorityApi,
	}

	return data, nil
}
