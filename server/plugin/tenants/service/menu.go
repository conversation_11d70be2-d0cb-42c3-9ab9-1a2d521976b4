package service

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system/response"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/model/request"
)

type menuService struct {
}

var MenuServiceApp = new(menuService)

// 获取服务商角色菜单
func (m *menuService) GetMenuAuthority(authorityId uint) (menus []system.SysMenu, err error) {

	var baseMenu []system.SysBaseMenu
	var SysAuthorityMenus []system.SysAuthorityMenu
	err = global.GVA_DB.Where("sys_authority_authority_id = ?", authorityId).Find(&SysAuthorityMenus).Error
	if err != nil {
		return
	}

	var MenuIds []string

	for i := range SysAuthorityMenus {
		MenuIds = append(MenuIds, SysAuthorityMenus[i].MenuId)
	}

	err = global.GVA_DB.Where("id in (?) ", MenuIds).Order("sort").Find(&baseMenu).Error

	for i := range baseMenu {
		menus = append(menus, system.SysMenu{
			SysBaseMenu: baseMenu[i],
			AuthorityId: authorityId,
			MenuId:      baseMenu[i].ID,
			Parameters:  baseMenu[i].Parameters,
		})
	}
	return menus, err

}

// 热更新-租户--角色-菜单
func (m *menuService) SetMenuAuthority(req request.SetMenuAuthority) (err error) {
	t := TenantService.NewTenant(req.ConfigID)
	return t.SetMenuAuthority(t.Config.TenantId, req.AuthorityId, req.Menus)
}

// 热更新-获取租户--角色-菜单 -按钮
func (m *menuService) GetAuthorityBtn(req request.GetAuthorityBtn) (res response.SysAuthorityBtnRes, err error) {
	t := TenantService.NewTenant(req.ConfigID)
	return t.GetAuthorityBtn(t.Config.TenantId, req.AuthorityId, req.MenuID)
}

// 热更新-设置租户--角色-菜单-按钮
func (m *menuService) SetAuthorityBtn(req request.SetAuthorityBtn) (err error) {
	t := TenantService.NewTenant(req.ConfigID)
	return t.SetAuthorityBtn(t.Config.TenantId, req.AuthorityId, req.MenuID, req.Selected)
}
