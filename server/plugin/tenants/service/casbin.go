package service

import (
	"fmt"
	"strconv"
	"sync"

	"github.com/casbin/casbin/v2"
	"github.com/casbin/casbin/v2/model"
	gormadapter "github.com/casbin/gorm-adapter/v3"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/model/request"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type casbinService struct{}

var (
	CasbinServiceApp        = new(casbinService)
	syncedCachedEnforcerMap sync.Map
)

// 添加casbin
func (c *casbinService) AddCasbin(db *gorm.DB, TenantID string) (err error) {
	q := c.Get<PERSON>asbin(TenantID)
	if q != nil {
		return fmt.Errorf("已存在该casbin实例,请勿重复添加")
	}

	a, err := gormadapter.NewAdapterByDB(db)
	if err != nil {
		zap.L().Error("适配数据库失败请检查casbin表是否为InnoDB引擎!", zap.Error(err))
		return err
	}
	text := `
		[request_definition]
		r = sub, obj, act
		
		[policy_definition]
		p = sub, obj, act
		
		[role_definition]
		g = _, _
		
		[policy_effect]
		e = some(where (p.eft == allow))
		
		[matchers]
		m = r.sub == p.sub && keyMatch2(r.obj,p.obj) && r.act == p.act
		`
	m, err := model.NewModelFromString(text)
	if err != nil {
		zap.L().Error("字符串加载模型失败!", zap.Error(err))
		return err
	}
	b, _ := casbin.NewSyncedCachedEnforcer(m, a)
	b.SetExpireTime(60 * 60)
	b.LoadPolicy()
	syncedCachedEnforcerMap.Store(TenantID, b)
	return nil
}

// 获取casbin
func (c *casbinService) GetCasbin(TenantID string) *casbin.SyncedCachedEnforcer {
	if v, ok := syncedCachedEnforcerMap.Load(TenantID); ok {
		return v.(*casbin.SyncedCachedEnforcer)
	}
	return nil
}

// 删除Casbin
func (c *casbinService) DeleteCasbin(TenantID string) {
	syncedCachedEnforcerMap.Delete(TenantID)
}

// 清除授权
func (c *casbinService) ClearCasbin(TenantID string, v int, p ...string) bool {
	e := c.GetCasbin(TenantID)
	if e == nil {
		return false
	}
	success, _ := e.RemoveFilteredPolicy(v, p...)
	return success
}

// 更新权限
func (c *casbinService) UpdateCasbin(ConfigID uint, authorityId uint, casbinInfos []request.CasbinInfo) (err error) {
	t := TenantService.NewTenant(ConfigID)
	if t == nil {
		return fmt.Errorf("未找到该租户")
	}

	e := c.GetCasbin(t.Config.TenantId)
	if e == nil {
		return fmt.Errorf("未找到该租户的casbin实例")
	}
	authorityID := strconv.Itoa(int(authorityId))
	ok, _ := e.RemoveFilteredPolicy(0, authorityID)
	if !ok {
		return fmt.Errorf("删除旧权限失败")
	}
	deduplicateMap := make(map[string]bool)
	rules := [][]string{}
	for _, v := range casbinInfos {
		key := authorityID + v.Path + v.Method
		if _, ok := deduplicateMap[key]; !ok {
			deduplicateMap[key] = true
			rules = append(rules, []string{authorityID, v.Path, v.Method})
		}
	}
	if len(rules) == 0 {
		return nil
	}
	success, _ := e.AddPolicies(rules)
	if !success {
		return fmt.Errorf("存在相同api,添加失败,请联系管理员")
	}
	return nil
}
