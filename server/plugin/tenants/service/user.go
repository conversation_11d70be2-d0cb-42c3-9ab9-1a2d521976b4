package service

import (
	"context"
	"errors"

	"github.com/google/uuid"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/model"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/model/request"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"gorm.io/gorm"
)

var User = new(use)

type use struct{}

// CreateUser 创建租户记录
// Author [yourname](https://github.com/yourname)
func (s *use) CreateUser(ctx context.Context, use *model.User) (err error) {
	// 全局用户名唯一性校验
	var count int64
	err = global.GVA_DB.Model(&system.GlobalUser{}).Where("username = ?", *use.UserName).Count(&count).Error
	if err != nil {
		return err
	}
	if count > 0 {
		return errors.New("用户名已存在，请使用其他用户名")
	}

	//查重（保留原有的租户内检查作为双重保障）
	if !errors.Is(global.GVA_DB.Where("user_name = ?", use.UserName).First(&model.User{}).Error, gorm.ErrRecordNotFound) {
		return errors.New("用户名已存在")
	}

	// 否则 附加uuid 密码hash加密 注册
	Password := utils.BcryptHash(*use.Password)
	use.Password = &Password
	use.UUID = uuid.New()

	// 创建租户用户
	err = global.GVA_DB.Create(use).Error
	if err != nil {
		return err
	}

	// TODO: 这里需要获取当前租户ID，可能需要从context或其他方式获取
	// 暂时跳过全局用户的创建，因为租户用户管理通常是在特定租户上下文中操作的
	// 如果需要，可以通过API层传递租户ID

	return nil
}

// DeleteUser 删除租户记录
// Author [yourname](https://github.com/yourname)
func (s *use) DeleteUser(ctx context.Context, ID string) (err error) {
	err = global.GVA_DB.Delete(&model.User{}, "id = ?", ID).Error
	return err
}

// DeleteUserByIds 批量删除租户记录
// Author [yourname](https://github.com/yourname)
func (s *use) DeleteUserByIds(ctx context.Context, IDs []string) (err error) {
	err = global.GVA_DB.Delete(&[]model.User{}, "id in ?", IDs).Error
	return err
}

// UpdateUser 更新租户记录
// Author [yourname](https://github.com/yourname)
func (s *use) UpdateUser(ctx context.Context, use model.User) (err error) {
	// 获取原始用户信息以比较用户名是否有变化
	var originalUser model.User
	err = global.GVA_DB.Where("id = ?", use.ID).First(&originalUser).Error
	if err != nil {
		return err
	}
	
	// 如果用户名有变化，进行全局唯一性校验
	if use.UserName != nil && *use.UserName != *originalUser.UserName {
		var count int64
		err = global.GVA_DB.Model(&system.GlobalUser{}).Where("username = ?", *use.UserName).Count(&count).Error
		if err != nil {
			return err
		}
		if count > 0 {
			return errors.New("用户名已存在，请使用其他用户名")
		}
	}
	
	err = global.GVA_DB.Model(&model.User{}).Where("id = ?", use.ID).Updates(&use).Error
	return err
}

// GetUser 根据ID获取租户记录
// Author [yourname](https://github.com/yourname)
func (s *use) GetUser(ctx context.Context, ID string) (use model.User, err error) {
	err = global.GVA_DB.Where("id = ?", ID).First(&use).Error
	return
}

// GetUserInfoList 分页获取租户记录
// Author [yourname](https://github.com/yourname)
func (s *use) GetUserInfoList(ctx context.Context, info request.UserSearch) (list []model.User, total int64, err error) {
	limit := info.PageSize
	offset := info.PageSize * (info.Page - 1)
	// 创建db
	db := global.GVA_DB.Model(&model.User{})
	var uses []model.User
	// 如果有条件搜索 下方会自动创建搜索语句
	if len(info.CreatedAtRange) == 2 {
		db = db.Where("created_at BETWEEN ? AND ?", info.CreatedAtRange[0], info.CreatedAtRange[1])
	}

	if info.UserName != nil && *info.UserName != "" {
		db = db.Where("user_name LIKE ?", "%"+*info.UserName+"%")
	}
	err = db.Count(&total).Error
	if err != nil {
		return
	}

	if limit != 0 {
		db = db.Limit(limit).Offset(offset)
	}
	err = db.Find(&uses).Error
	return uses, total, err
}
