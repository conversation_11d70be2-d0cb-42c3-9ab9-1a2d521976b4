package utils

import (
	sys_model_system "github.com/flipped-aurora/gin-vue-admin/server/model/system"
)

func BuildMenuTree(menus []sys_model_system.SysBaseMenu, parentId uint) []sys_model_system.SysBaseMenu {
	var tree []sys_model_system.SysBaseMenu
	for _, menu := range menus {
		if menu.ParentId == parentId {
			children := BuildMenuTree(menus, menu.ID)
			menu.Children = children
			tree = append(tree, menu)
		}
	}
	return tree
}
