aliyun-oss:
    endpoint: yourEndpoint
    access-key-id: yourAccessKeyId
    access-key-secret: yourAccessKeySecret
    bucket-name: yourBucketName
    bucket-url: yourBucketUrl
    base-path: yourBasePath
autocode:
    web: web/src
    root: /Users/<USER>/php/gva-tenants2
    server: server
    module: github.com/flipped-aurora/gin-vue-admin/server
    ai-path: ""
aws-s3:
    bucket: xxxxx-********
    region: ap-shanghai
    endpoint: ""
    secret-id: your-secret-id
    secret-key: your-secret-key
    base-url: https://gin.vue.admin
    path-prefix: github.com/flipped-aurora/gin-vue-admin/server
    s3-force-path-style: false
    disable-ssl: false
captcha:
    key-long: 6
    img-width: 240
    img-height: 80
    open-captcha: 0
    open-captcha-timeout: 3600
cloudflare-r2:
    bucket: xxxx0bucket
    base-url: https://gin.vue.admin.com
    path: uploads
    account-id: xxx_account_id
    access-key-id: xxx_key_id
    secret-access-key: xxx_secret_key
cors:
    mode: strict-whitelist
    whitelist:
        - allow-origin: http://localhost:3000
          allow-methods: POST, GET, OPTIONS, DELETE, PUT
          allow-headers: Content-Type,AccessToken,X-CSRF-Token, Authorization, Token,X-Token,X-User-Id,user_id
          expose-headers: Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type, New-Token, New-Expires-At
          allow-credentials: true
        - allow-origin: example1.com
          allow-methods: POST, GET
          allow-headers: Content-Type,AccessToken,X-CSRF-Token, Authorization, Token,X-Token,X-User-Id,user_id
          expose-headers: Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type
          allow-credentials: true
        - allow-origin: example2.com
          allow-methods: GET, POST
          allow-headers: content-type,user_id
          expose-headers: Content-Length, Access-Control-Allow-Origin, Access-Control-Allow-Headers, Content-Type
          allow-credentials: true
db-list:
    - type: ""
      alias-name: ""
      prefix: ""
      port: ""
      config: ""
      db-name: ""
      username: ""
      password: ""
      path: ""
      engine: ""
      log-mode: ""
      max-idle-conns: 10
      max-open-conns: 100
      singular: false
      log-zap: false
      disable: true
disk-list:
    - mount-point: /
email:
    to: <EMAIL>
    from: <EMAIL>
    host: smtp.163.com
    secret: xxx
    nickname: test
    port: 465
    is-ssl: true
    is-loginauth: false
excel:
    dir: ./resource/excel/
hua-wei-obs:
    path: you-path
    bucket: you-bucket
    endpoint: you-endpoint
    access-key: you-access-key
    secret-key: you-secret-key
jwt:
    signing-key: fPiMcrpgB1qv98PSsmnwY6L9zSvkNbRj7EaNUi8M87k3XQTfiXdeFw5I
    expires-time: 1d
    buffer-time: 1d
    issuer: CLOUD
local:
    path: uploads/file
    store-path: uploads/file
mcp:
    name: GVA_MCP
    version: v1.0.0
    sse_path: /sse
    message_path: /message
    url_prefix: ""
minio:
    endpoint: yourEndpoint
    access-key-id: yourAccessKeyId
    access-key-secret: yourAccessKeySecret
    bucket-name: yourBucketName
    use-ssl: false
    base-path: ""
    bucket-url: http://host:9000/yourBucketName
mongo:
    coll: ""
    options: ""
    database: ""
    username: ""
    password: ""
    auth-source: ""
    min-pool-size: 0
    max-pool-size: 100
    socket-timeout-ms: 0
    connect-timeout-ms: 0
    is-zap: false
    hosts:
        - host: ""
          port: ""
mssql:
    prefix: ""
    port: ""
    config: ""
    db-name: ""
    username: ""
    password: ""
    path: ""
    engine: ""
    log-mode: ""
    max-idle-conns: 10
    max-open-conns: 100
    singular: false
    log-zap: false
mysql:
    prefix: ""
    port: "3306"
    config: charset=utf8mb4&parseTime=True&loc=Local
    db-name: gaia_local
    username: gaia
    password: gaia123*
    path: gaiadb.dev.yafex.cn
    engine: ""
    log-mode: error
    max-idle-conns: 10
    max-open-conns: 100
    singular: false
    log-zap: false
oracle:
    prefix: ""
    port: ""
    config: ""
    db-name: ""
    username: ""
    password: ""
    path: ""
    engine: ""
    log-mode: ""
    max-idle-conns: 10
    max-open-conns: 100
    singular: false
    log-zap: false
pgsql:
    prefix: ""
    port: ""
    config: ""
    db-name: ""
    username: ""
    password: ""
    path: ""
    engine: ""
    log-mode: ""
    max-idle-conns: 10
    max-open-conns: 100
    singular: false
    log-zap: false
qiniu:
    zone: ZoneHuaDong
    bucket: ""
    img-path: ""
    access-key: ""
    secret-key: ""
    use-https: false
    use-cdn-domains: false
redis:
    name: ""
    addr: 127.0.0.1:6379
    password: "difyai123456"
    db: 0
    useCluster: false
    clusterAddrs:
        - **********:7000
        - **********:7001
        - **********:7002
redis-list:
    - name: cache
      addr: 127.0.0.1:6379
      password: "difyai123456"
      db: 0
      useCluster: false
      clusterAddrs:
        - **********:7000
        - **********:7001
        - **********:7002
sqlite:
    prefix: ""
    port: ""
    config: ""
    db-name: ""
    username: ""
    password: ""
    path: ""
    engine: ""
    log-mode: ""
    max-idle-conns: 10
    max-open-conns: 100
    singular: false
    log-zap: false
system:
    environment: test                # 环境类型：test（测试）|production（正式）
    db-type: mysql
    oss-type: local
    router-prefix: ""
    addr: 8888
    iplimit-count: 15000
    iplimit-time: 3600
    use-multipoint: false
    use-redis: true
    use-mongo: false
    use-strict-auth: false
tencent-cos:
    bucket: xxxxx-********
    region: ap-shanghai
    secret-id: your-secret-id
    secret-key: your-secret-key
    base-url: https://gin.vue.admin
    path-prefix: github.com/flipped-aurora/gin-vue-admin/server
zap:
    level: info
    prefix: '[github.com/flipped-aurora/gin-vue-admin/server]'
    format: console
    director: log
    encode-level: LowercaseColorLevelEncoder
    stacktrace-key: stacktrace
    show-line: true
    log-in-console: true
    retention-day: -1

# Asynq队列服务配置
asynq:
  redis:
    addr: "127.0.0.1:6379"      # Redis地址（单节点模式时使用）
    password: "difyai123456"     # Redis密码（如果有）
    db: 4                        # 使用Redis数据库4（避免与缓存混用）
    useCluster: false            # 是否使用Redis集群模式
    clusterAddrs:                # Redis集群节点地址列表
      - **********:7000
      - **********:7001
      - **********:7002
      # 根据实际集群节点情况增减地址
  queues:
    critical: 6                  # 关键任务队列，权重6（处理60%的时间）
    default: 3                   # 默认任务队列，权重3（处理30%的时间）
    low: 1                       # 低优先级队列，权重1（处理10%的时间）
  concurrency: 10                # 并发工作协程数
  max_retry: 3                   # 任务失败最大重试次数
  timeout: 300                   # 任务超时时间（秒）
  metrics:                       # Prometheus指标监控配置
    enabled: true                # 是否启用指标监控
    listenAddr: ":2112"          # 指标服务器监听地址

# gaia-x域名地址
gaia-x:
  base-url: "http://gaia-x.dev.yafex.cn"

# 外部API配置
external-apis:
  pdm:
    base-url: "http://pdm.dev.yafex.cn"  # PDM系统API地址
    timeout: 30                          # 超时时间（秒）
    retry-times: 3                       # 重试次数
    enable-log: false                     # 是否启用日志
  image:
    base-url: "http://img.dev.yafex.cn"  # 图片系统API地址
    timeout: 30                          # 超时时间（秒）
    retry-times: 3                       # 重试次数
    enable-log: false                     # 是否启用日志
  ai-draw:
    base-url: "https://ai-draw.yafex.cn"
    timeout: 30
    retry-times: 3
    enable-log: false
  gaia-admin:
    timeout: 30
    default_authorities: 1
    url: "http://gaia2-admin.dev.yafex.cn/api"
    default_avatar: "https://qmplusimg.henrongyi.top/1576554439myAvatar.png"

# 工作流并发控制配置
workflow-concurrency:
  max-global-workers: 2           # 全局最大并发工作线程数（推荐2-5）
  default-batch-concurrency: 2    # 默认批量并发数（不能超过全局最大值）
  enable-metrics: true            # 是否启用指标收集
  metrics-reset-interval: 24      # 指标重置间隔（小时）
  queue-timeout-ms: 30000         # 队列等待超时时间（毫秒）
  enable-health-check: true       # 是否启用健康检查
