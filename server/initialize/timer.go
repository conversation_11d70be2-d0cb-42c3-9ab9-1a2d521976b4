package initialize

import (
	"context"
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/service/gaia"
	"github.com/flipped-aurora/gin-vue-admin/server/task"
	"github.com/robfig/cron/v3"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/gin-gonic/gin"
)

func Timer() {

	//todo 临时 判断环境是否为生产环境，只有生产环境才启动定时任务
	if gin.Mode() != gin.ReleaseMode {
		fmt.Printf("当前环境为 %s，跳过定时任务初始化（仅在生产环境启动）\n", gin.Mode())
		return
	}

	go func() {
		var option []cron.Option
		option = append(option, cron.WithSeconds())
		// 清理DB定时任务
		_, err := global.GVA_Timer.AddTaskByFunc("ClearDB", "@daily", func() {
			err := task.ClearTable(global.GVA_DB) // 定时任务方法定在task文件包中
			if err != nil {
				fmt.Println("timer error:", err)
			}
		}, "定时清理数据库【日志，黑名单】内容", option...)
		if err != nil {
			fmt.Println("add timer error:", err)
		}

		// 同步gaia用户
		_, err = global.GVA_Timer.AddTaskByFunc("SyncUser", "0 */1 * * * *", func() {
			user := gaia.GaiaUserService{}
			user.SyncUser()
		}, "每分钟同步所有用户数据", option...)
		if err != nil {
			fmt.Println("add timer error:", err)
		}

		// 每两分钟定时，拉取pdm图片列表，进入流程中
		_, err = global.GVA_Timer.AddTaskByFunc("GetPdmSpuImage", "0 */2 * * * *", func() {
			err := task.GetPdmSpuImage(context.TODO(), global.GVA_DB)
			if err != nil {
				fmt.Println("GetPdmSpuImage timer error:", err)
			}
		}, "每两分钟定时，拉取pdm图片列表进入流程中", option...)
		if err != nil {
			fmt.Println("add timer error:", err)
		}

		// 每两分钟定时,同步PDM图片处理状态
		_, err = global.GVA_Timer.AddTaskByFunc("SyncPdmImageStatus", "0 */3 * * * *", func() {
			err := task.SyncPdmImageStatus(context.TODO(), global.GVA_DB, global.GVA_REDIS)
			if err != nil {
				fmt.Println("SyncPdmImageStatus timer error:", err)
			}
		}, "每两分钟定时,同步PDM图片处理状态", option...)
		if err != nil {
			fmt.Println("add timer error:", err)
		}
		// 其他定时任务定在这里 参考上方使用方法

		//_, err := global.GVA_Timer.AddTaskByFunc("定时任务标识", "corn表达式", func() {
		//	具体执行内容...
		//  ......
		//}, option...)
		//if err != nil {
		//	fmt.Println("add timer error:", err)
		//}
	}()
}
