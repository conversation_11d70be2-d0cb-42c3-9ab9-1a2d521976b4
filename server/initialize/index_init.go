package initialize

import (
	"context"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/service/system"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

const initOrderIndexes = initOrderEnsureTables + 1

type indexInit struct{}

// auto run
func init() {
	system.RegisterInit(initOrderIndexes, &indexInit{})
}

func (i *indexInit) InitializerName() string {
	return "index_checker"
}

func (i *indexInit) InitializeData(ctx context.Context) (next context.Context, err error) {
	db, ok := ctx.Value("db").(*gorm.DB)
	if !ok {
		return ctx, system.ErrMissingDBContext
	}

	// 执行索引检查和创建
	if err := utils.CheckAndCreateIndexes(db); err != nil {
		global.GVA_LOG.Error("索引初始化失败", zap.Error(err))
		return ctx, err
	}

	global.GVA_LOG.Info("索引初始化完成")
	return ctx, nil
}

func (i *indexInit) DataInserted(ctx context.Context) bool {
	return true
}

func (i *indexInit) MigrateTable(ctx context.Context) (context.Context, error) {
	return ctx, nil
}

func (i *indexInit) TableCreated(ctx context.Context) bool {
	return true
}
