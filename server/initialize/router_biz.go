package initialize

import (
	"github.com/flipped-aurora/gin-vue-admin/server/router"
	"github.com/gin-gonic/gin"
)

func holder(routers ...*gin.RouterGroup) {
	_ = routers
	_ = router.RouterGroupApp
}
func initBizRouter(routers ...*gin.RouterGroup) {
	privateGroup := routers[0]
	publicGroup := routers[1]
	// 租户私有路由
	tenantPrivateGroup := routers[2]
	holder(publicGroup, privateGroup) // 占位方法，保证文件可以正确加载，避免go空变量检测报错，请勿删除。
	{
		autoProcessRouter := router.RouterGroupApp.AutoProcess
		autoProcessRouter.InitTaskRouter(privateGroup, publicGroup)
		autoProcessRouter.InitImageScreenshotTestRouter(privateGroup, publicGroup)    // 图片截图测试路由
		autoProcessRouter.InitWorkflowBatchRunsRouter(privateGroup, publicGroup)      // 工作流批量执行记录路由
		autoProcessRouter.InitWorkflowsRouter(privateGroup, publicGroup)              // 工作流路由
		autoProcessRouter.InitWorkflowRunsRouter(privateGroup, publicGroup)           // 工作流执行记录路由
		autoProcessRouter.InitWorkflowNodeExecutionsRouter(privateGroup, publicGroup) // 工作流节点执行记录路由
	}
	{
		// 租户私有路由
		mcpRouter := router.RouterGroupApp.MCP
		mcpRouter.InitMCPServiceRouter(tenantPrivateGroup, publicGroup) // MCP服务管理路由
		mcpRouter.InitMCPToolRouter(tenantPrivateGroup, publicGroup)    // MCP工具管理和动态API路由
		mcpRouter.InitMCPHealthRouter(tenantPrivateGroup, publicGroup)  // MCP健康检查路由
		mcpRouter.InitMCPCallLogRouter(tenantPrivateGroup, publicGroup) // MCP调用日志路由
	}
	{
		queueRouter := router.RouterGroupApp.Queue
		queueRouter.InitQueueRouter(privateGroup, publicGroup) // 队列管理路由
	}
	{
		aiDrawRouter := router.RouterGroupApp.AiDraw
		aiDrawRouter.InitAiDrawRouter(privateGroup, publicGroup) // AI Draw 抠图服务路由
	}
	{
		workflowRouter := router.RouterGroupApp.Workflow
		workflowRouter.InitWorkflowRouter(privateGroup, publicGroup) // 工作流批量执行路由
	}
}
