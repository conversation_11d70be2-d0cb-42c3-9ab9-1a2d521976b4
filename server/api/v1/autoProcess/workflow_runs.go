package autoProcess

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/autoProcess"
	autoProcessReq "github.com/flipped-aurora/gin-vue-admin/server/model/autoProcess/request"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type WorkflowRunsApi struct{}

// GetWorkflowRunsList 分页获取工作流执行记录列表
// @Tags WorkflowRuns
// @Summary 分页获取工作流执行记录列表
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query autoProcessReq.WorkflowRunsSearch true "分页获取工作流执行记录列表"
// @Success 200 {object} response.Response{data=response.PageResult,msg=string} "获取成功"
// @Router /workflowRuns/getWorkflowRunsList [get]
func (workflowRunsApi *WorkflowRunsApi) GetWorkflowRunsList(c *gin.Context) {
	ctx := c.Request.Context()
	var pageInfo autoProcessReq.WorkflowRunsSearch
	err := c.ShouldBindQuery(&pageInfo)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = utils.Verify(pageInfo.PageInfo, utils.PageInfoVerify)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	list, total, err := workflowRunsService.GetWorkflowRunsList(ctx, pageInfo)
	if err != nil {
		global.GVA_LOG.Error("获取失败!", zap.Error(err))
		response.FailWithMessage("获取失败:"+err.Error(), c)
		return
	}
	response.OkWithDetailed(response.PageResult{
		List:     list,
		Total:    total,
		Page:     pageInfo.Page,
		PageSize: pageInfo.PageSize,
	}, "获取成功", c)
}

// FindWorkflowRuns 用id查询工作流执行记录
// @Tags WorkflowRuns
// @Summary 用id查询工作流执行记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data query autoProcess.WorkflowRuns true "用id查询工作流执行记录"
// @Success 200 {object} response.Response{data=autoProcess.WorkflowRuns,msg=string} "查询成功"
// @Router /workflowRuns/findWorkflowRuns [get]
func (workflowRunsApi *WorkflowRunsApi) FindWorkflowRuns(c *gin.Context) {
	ctx := c.Request.Context()
	ID := c.Query("ID")
	uintID, err := strconv.ParseUint(ID, 10, 32)
	if err != nil {
		response.FailWithMessage("ID格式错误", c)
		return
	}
	workflowRuns, err := workflowRunsService.GetWorkflowRuns(ctx, autoProcess.WorkflowRunId(uintID))
	if err != nil {
		global.GVA_LOG.Error("查询失败!", zap.Error(err))
		response.FailWithMessage("查询失败:"+err.Error(), c)
		return
	}
	response.OkWithData(workflowRuns, c)
}

// DeleteWorkflowRuns 删除工作流执行记录
// @Tags WorkflowRuns
// @Summary 删除工作流执行记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body autoProcess.WorkflowRuns true "删除工作流执行记录"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /workflowRuns/deleteWorkflowRuns [delete]
func (workflowRunsApi *WorkflowRunsApi) DeleteWorkflowRuns(c *gin.Context) {
	ctx := c.Request.Context()
	ID := c.Query("ID")
	uintID, err := strconv.ParseUint(ID, 10, 32)
	if err != nil {
		response.FailWithMessage("ID格式错误", c)
		return
	}
	err = workflowRunsService.DeleteWorkflowRuns(ctx, autoProcess.WorkflowRunId(uintID))
	if err != nil {
		global.GVA_LOG.Error("删除失败!", zap.Error(err))
		response.FailWithMessage("删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("删除成功", c)
}

// DeleteWorkflowRunsByIds 批量删除工作流执行记录
// @Tags WorkflowRuns
// @Summary 批量删除工作流执行记录
// @Security ApiKeyAuth
// @Accept application/json
// @Produce application/json
// @Param data body []autoProcess.WorkflowRunId true "批量删除工作流执行记录"
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /workflowRuns/deleteWorkflowRunsByIds [delete]
func (workflowRunsApi *WorkflowRunsApi) DeleteWorkflowRunsByIds(c *gin.Context) {
	ctx := c.Request.Context()
	var IDs []autoProcess.WorkflowRunId
	err := c.ShouldBindJSON(&IDs)
	if err != nil {
		response.FailWithMessage(err.Error(), c)
		return
	}
	err = workflowRunsService.DeleteWorkflowRunsByIds(ctx, IDs)
	if err != nil {
		global.GVA_LOG.Error("批量删除失败!", zap.Error(err))
		response.FailWithMessage("批量删除失败:"+err.Error(), c)
		return
	}
	response.OkWithMessage("批量删除成功", c)
}
