package mcp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	t_utils "github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type MCPHealthApi struct{}

// CheckServiceHealth 检查服务健康状态
// @Tags MCP健康检查
// @Summary 检查MCP服务健康状态
// @Description 获取指定服务的健康状态
// @Accept json
// @Produce json
// @Param server path string true "服务器名称"
// @Success 200 {object} response.Response{data=mcpRes.HealthCheckResponse,msg=string} "检查成功"
// @Router /mcp/services/{server}/health [get]
func (a *MCPHealthApi) CheckServiceHealth(c *gin.Context) {
	serverName := c.Param("server")
	if serverName == "" {
		response.FailWithMessage("服务器名称不能为空", c)
		return
	}

	result, err := mcpHealthService.CheckServiceHealth(t_utils.GetTenantDB(c), c.Request.Context(), serverName)
	if err != nil {
		global.GVA_LOG.Error("健康检查失败", zap.Error(err))
		response.FailWithMessage("检查失败: "+err.Error(), c)
		return
	}

	response.OkWithData(result, c)
}

// CheckAllServicesHealth 检查所有服务健康状态
// @Tags MCP健康检查
// @Summary 检查所有MCP服务健康状态
// @Description 触发所有服务的健康检查
// @Accept json
// @Produce json
// @Success 200 {object} response.Response{msg=string} "检查完成"
// @Router /mcp/services/health/check-all [post]
func (a *MCPHealthApi) CheckAllServicesHealth(c *gin.Context) {
	if err := mcpHealthService.CheckAllServicesHealth(t_utils.GetTenantDB(c), c.Request.Context()); err != nil {
		global.GVA_LOG.Error("批量健康检查失败", zap.Error(err))
		response.FailWithMessage("检查失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("健康检查已触发", c)
}
