package mcp

import (
	"strconv"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	mcpReq "github.com/flipped-aurora/gin-vue-admin/server/model/mcp/request"
	t_utils "github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type MCPCallLogApi struct{}

// GetCallLogList 获取调用日志列表
// @Tags MCP调用日志
// @Summary 获取MCP调用日志列表
// @Description 获取MCP工具调用日志列表，支持多条件搜索和分页
// @Accept json
// @Produce json
// @Param data query mcpReq.CallLogSearchRequest true "搜索条件"
// @Success 200 {object} response.Response{data=mcpRes.CallLogListResponse,msg=string} "获取成功"
// @Router /mcp/call-logs [get]
func (a *MCPCallLogApi) GetCallLogList(c *gin.Context) {
	var req mcpReq.CallLogSearchRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	// 设置默认分页参数
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}

	result, err := mcpCallLogService.GetCallLogList(t_utils.GetTenantDB(c), c.Request.Context(), req)
	if err != nil {
		global.GVA_LOG.Error("获取调用日志列表失败", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithData(result, c)
}

// GetCallLogDetail 获取调用日志详情
// @Tags MCP调用日志
// @Summary 获取MCP调用日志详情
// @Description 获取指定ID的MCP调用日志详细信息
// @Accept json
// @Produce json
// @Param id path int true "日志ID"
// @Success 200 {object} response.Response{data=mcpRes.CallLogDetailResponse,msg=string} "获取成功"
// @Router /mcp/call-logs/{id} [get]
func (a *MCPCallLogApi) GetCallLogDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的日志ID", c)
		return
	}

	result, err := mcpCallLogService.GetCallLogDetail(t_utils.GetTenantDB(c), c.Request.Context(), uint(id))
	if err != nil {
		global.GVA_LOG.Error("获取调用日志详情失败", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithData(result, c)
}

// GetCallLogStats 获取调用日志统计
// @Tags MCP调用日志
// @Summary 获取MCP调用日志统计
// @Description 获取MCP工具调用的统计信息
// @Accept json
// @Produce json
// @Param data query mcpReq.CallLogSearchRequest true "搜索条件"
// @Success 200 {object} response.Response{data=mcpRes.CallLogStatsResponse,msg=string} "获取成功"
// @Router /mcp/call-logs/stats [get]
func (a *MCPCallLogApi) GetCallLogStats(c *gin.Context) {
	var req mcpReq.CallLogSearchRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	result, err := mcpCallLogService.GetCallLogStats(t_utils.GetTenantDB(c), c.Request.Context(), req)
	if err != nil {
		global.GVA_LOG.Error("获取调用日志统计失败", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithData(result, c)
}

// DeleteCallLog 删除调用日志
// @Tags MCP调用日志
// @Summary 删除MCP调用日志
// @Description 删除指定ID的MCP调用日志
// @Accept json
// @Produce json
// @Param id path int true "日志ID"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /mcp/call-logs/{id} [delete]
func (a *MCPCallLogApi) DeleteCallLog(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的日志ID", c)
		return
	}

	err = mcpCallLogService.DeleteCallLog(t_utils.GetTenantDB(c), c.Request.Context(), uint(id))
	if err != nil {
		global.GVA_LOG.Error("删除调用日志失败", zap.Error(err))
		response.FailWithMessage("删除失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// BatchDeleteCallLogs 批量删除调用日志
// @Tags MCP调用日志
// @Summary 批量删除MCP调用日志
// @Description 批量删除多个MCP调用日志
// @Accept json
// @Produce json
// @Param data body []uint true "日志ID列表"
// @Success 200 {object} response.Response{msg=string} "批量删除成功"
// @Router /mcp/call-logs/batch-delete [post]
func (a *MCPCallLogApi) BatchDeleteCallLogs(c *gin.Context) {
	var ids []uint
	if err := c.ShouldBindJSON(&ids); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	if len(ids) == 0 {
		response.FailWithMessage("删除ID列表不能为空", c)
		return
	}

	err := mcpCallLogService.BatchDeleteCallLogs(t_utils.GetTenantDB(c), c.Request.Context(), ids)
	if err != nil {
		global.GVA_LOG.Error("批量删除调用日志失败", zap.Error(err))
		response.FailWithMessage("批量删除失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("批量删除成功", c)
}

// CleanOldLogs 清理旧日志
// @Tags MCP调用日志
// @Summary 清理旧的MCP调用日志
// @Description 清理指定天数之前的MCP调用日志
// @Accept json
// @Produce json
// @Param data body map[string]int true "保留天数 {\"days\": 30}"
// @Success 200 {object} response.Response{msg=string} "清理成功"
// @Router /mcp/call-logs/clean [post]
func (a *MCPCallLogApi) CleanOldLogs(c *gin.Context) {
	var req map[string]int
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	days, exists := req["days"]
	if !exists || days <= 0 {
		response.FailWithMessage("保留天数必须大于0", c)
		return
	}

	err := mcpCallLogService.CleanOldLogs(t_utils.GetTenantDB(c), c.Request.Context(), days)
	if err != nil {
		global.GVA_LOG.Error("清理旧日志失败", zap.Error(err))
		response.FailWithMessage("清理失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("清理成功", c)
}
