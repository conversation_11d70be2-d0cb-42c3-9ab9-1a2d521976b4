package mcp

import (
	"io"
	"strconv"
	"strings"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	mcpReq "github.com/flipped-aurora/gin-vue-admin/server/model/mcp/request"
	t_utils "github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type MCPServiceApi struct{}

// RegisterService 注册MCP服务
// @Tags MCP服务管理
// @Summary 注册MCP服务
// @Description 注册新的MCP服务器，自动发现工具并生成API
// @Accept json
// @Produce json
// @Param data body mcpReq.RegisterServiceRequest true "服务注册信息"
// @Success 200 {object} response.Response{data=mcpRes.RegisterServiceResponse,msg=string} "注册成功"
// @Router /mcp/services/register [post]
func (a *MCPServiceApi) RegisterService(c *gin.Context) {
	var req mcpReq.RegisterServiceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	result, err := mcpServiceService.RegisterService(t_utils.GetTenantDB(c), c.Request.Context(), req)
	if err != nil {
		global.GVA_LOG.Error("注册MCP服务失败", zap.Error(err))
		response.FailWithMessage("注册失败: "+err.Error(), c)
		return
	}

	response.OkWithData(result, c)
}

// GetServiceList 获取服务列表
// @Tags MCP服务管理
// @Summary 获取MCP服务列表
// @Description 获取已注册的MCP服务器列表
// @Accept json
// @Produce json
// @Param data query mcpReq.MCPServiceSearch true "搜索条件"
// @Success 200 {object} response.Response{data=mcpRes.ServiceListResponse,msg=string} "获取成功"
// @Router /mcp/services [get]
func (a *MCPServiceApi) GetServiceList(c *gin.Context) {
	var req mcpReq.MCPServiceSearch
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	result, err := mcpServiceService.GetServiceList(t_utils.GetTenantDB(c), c.Request.Context(), req)
	if err != nil {
		global.GVA_LOG.Error("获取MCP服务列表失败", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithData(result, c)
}

// UpdateService 更新服务
// @Tags MCP服务管理
// @Summary 更新MCP服务
// @Description 更新指定的MCP服务器信息
// @Accept json
// @Produce json
// @Param data body mcpReq.UpdateServiceRequest true "服务更新信息"
// @Success 200 {object} response.Response{msg=string} "更新成功"
// @Router /mcp/services/{id} [put]
func (a *MCPServiceApi) UpdateService(c *gin.Context) {
	var req mcpReq.UpdateServiceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	err := mcpServiceService.UpdateService(t_utils.GetTenantDB(c), c.Request.Context(), req)
	if err != nil {
		global.GVA_LOG.Error("更新MCP服务失败", zap.Error(err))
		response.FailWithMessage("更新失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("更新成功", c)
}

// DeleteService 删除服务
// @Tags MCP服务管理
// @Summary 删除MCP服务
// @Description 删除指定的MCP服务器
// @Accept json
// @Produce json
// @Param id path int true "服务ID"
// @Success 200 {object} response.Response{msg=string} "删除成功"
// @Router /mcp/services/{id} [delete]
func (a *MCPServiceApi) DeleteService(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		response.FailWithMessage("无效的服务ID", c)
		return
	}

	err = mcpServiceService.DeleteService(t_utils.GetTenantDB(c), c.Request.Context(), uint(id))
	if err != nil {
		global.GVA_LOG.Error("删除MCP服务失败", zap.Error(err))
		response.FailWithMessage("删除失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("删除成功", c)
}

// ToggleServiceStatus 切换服务状态
// @Tags MCP服务管理
// @Summary 切换MCP服务状态
// @Description 启用或禁用MCP服务
// @Accept json
// @Produce json
// @Param data body mcpReq.ToggleServiceStatusRequest true "状态切换请求"
// @Success 200 {object} response.Response{msg=string} "状态切换成功"
// @Router /mcp/services/toggle-status [post]
func (a *MCPServiceApi) ToggleServiceStatus(c *gin.Context) {
	var req mcpReq.ToggleServiceStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	// 解析body中的数据
	body, err := io.ReadAll(c.Request.Body)
	if err != nil {
		response.FailWithMessage("读取请求体失败: "+err.Error(), c)
		return
	}

	// 检查是否包含多个ID（批量操作）
	if strings.Contains(string(body), "ids") {
		response.FailWithMessage("批量操作请使用批量接口", c)
		return
	}

	err = mcpServiceService.ToggleServiceStatus(t_utils.GetTenantDB(c), c.Request.Context(), req)
	if err != nil {
		global.GVA_LOG.Error("切换MCP服务状态失败", zap.Error(err))
		response.FailWithMessage("状态切换失败: "+err.Error(), c)
		return
	}

	response.OkWithMessage("状态切换成功", c)
}

// BatchUpdateServices 批量更新服务
// @Tags MCP服务管理
// @Summary 批量更新MCP服务
// @Description 批量更新多个MCP服务器信息
// @Accept json
// @Produce json
// @Param data body mcpReq.BatchUpdateServicesRequest true "批量更新请求"
// @Success 200 {object} response.Response{data=mcpRes.BatchOperationResponse,msg=string} "批量更新成功"
// @Router /mcp/services/batch-update [post]
func (a *MCPServiceApi) BatchUpdateServices(c *gin.Context) {
	var req mcpReq.BatchUpdateServicesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	result, err := mcpServiceService.BatchUpdateServices(t_utils.GetTenantDB(c), c.Request.Context(), req)
	if err != nil {
		global.GVA_LOG.Error("批量更新MCP服务失败", zap.Error(err))
		response.FailWithMessage("批量更新失败: "+err.Error(), c)
		return
	}

	response.OkWithData(result, c)
}

// BatchDeleteServices 批量删除服务
// @Tags MCP服务管理
// @Summary 批量删除MCP服务
// @Description 批量删除多个MCP服务器
// @Accept json
// @Produce json
// @Param data body mcpReq.BatchDeleteServicesRequest true "批量删除请求"
// @Success 200 {object} response.Response{data=mcpRes.BatchOperationResponse,msg=string} "批量删除成功"
// @Router /mcp/services/batch-delete [post]
func (a *MCPServiceApi) BatchDeleteServices(c *gin.Context) {
	var req mcpReq.BatchDeleteServicesRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	result, err := mcpServiceService.BatchDeleteServices(t_utils.GetTenantDB(c), c.Request.Context(), req)
	if err != nil {
		global.GVA_LOG.Error("批量删除MCP服务失败", zap.Error(err))
		response.FailWithMessage("批量删除失败: "+err.Error(), c)
		return
	}

	response.OkWithData(result, c)
}

// SyncTools 同步工具
// @Tags MCP服务管理
// @Summary 同步MCP服务工具
// @Description 从指定的MCP服务器同步工具信息
// @Accept json
// @Produce json
// @Param data body mcpReq.SyncToolsRequest true "同步工具请求"
// @Success 200 {object} response.Response{data=mcpRes.SyncToolsResponse,msg=string} "同步成功"
// @Router /mcp/services/sync-tools [post]
func (a *MCPServiceApi) SyncTools(c *gin.Context) {
	var req mcpReq.SyncToolsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	result, err := mcpServiceService.SyncToolsFromMCPEnhanced(t_utils.GetTenantDB(c), c.Request.Context(), req)
	if err != nil {
		global.GVA_LOG.Error("同步MCP工具失败", zap.Error(err))
		response.FailWithMessage("同步失败: "+err.Error(), c)
		return
	}

	response.OkWithData(result, c)
}

// BatchSyncTools 批量同步工具
// @Tags MCP服务管理
// @Summary 批量同步MCP服务工具
// @Description 批量同步多个MCP服务器的工具信息
// @Accept json
// @Produce json
// @Param data body mcpReq.BatchSyncToolsRequest true "批量同步工具请求"
// @Success 200 {object} response.Response{data=mcpRes.BatchSyncToolsResponse,msg=string} "批量同步成功"
// @Router /mcp/services/batch-sync-tools [post]
func (a *MCPServiceApi) BatchSyncTools(c *gin.Context) {
	var req mcpReq.BatchSyncToolsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	result, err := mcpServiceService.BatchSyncToolsFromMCP(t_utils.GetTenantDB(c), c.Request.Context(), req)
	if err != nil {
		global.GVA_LOG.Error("批量同步MCP工具失败", zap.Error(err))
		response.FailWithMessage("批量同步失败: "+err.Error(), c)
		return
	}

	response.OkWithData(result, c)
}

// SyncAllTools 同步所有工具
// @Tags MCP服务管理
// @Summary 同步所有MCP服务工具
// @Description 同步所有已注册MCP服务器的工具信息
// @Accept json
// @Produce json
// @Param data body mcpReq.SyncAllToolsRequest true "同步所有工具请求"
// @Success 200 {object} response.Response{data=mcpRes.SyncAllToolsResponse,msg=string} "同步成功"
// @Router /mcp/services/sync-all-tools [post]
func (a *MCPServiceApi) SyncAllTools(c *gin.Context) {
	var req mcpReq.SyncAllToolsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	result, err := mcpServiceService.SyncAllToolsFromMCP(t_utils.GetTenantDB(c), c.Request.Context(), req)
	if err != nil {
		global.GVA_LOG.Error("同步所有MCP工具失败", zap.Error(err))
		response.FailWithMessage("同步失败: "+err.Error(), c)
		return
	}

	response.OkWithData(result, c)
}
