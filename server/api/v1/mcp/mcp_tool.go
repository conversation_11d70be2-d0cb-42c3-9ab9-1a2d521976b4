package mcp

import (
	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	mcpReq "github.com/flipped-aurora/gin-vue-admin/server/model/mcp/request"
	t_utils "github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/utils"
	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type MCPToolApi struct{}

// GetToolList 获取工具列表
// @Tags MCP工具管理
// @Summary 获取MCP工具列表
// @Description 获取所有可用的MCP工具列表，包含动态生成的API路径
// @Accept json
// @Produce json
// @Param data query mcpReq.MCPToolSearch true "搜索条件"
// @Success 200 {object} response.Response{data=mcpRes.ToolListResponse,msg=string} "获取成功"
// @Router /mcp/tools [get]
func (a *MCPToolApi) GetToolList(c *gin.Context) {
	var req mcpReq.MCPToolSearch
	if err := c.ShouldBindQuery(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	// 设置默认分页参数
	if req.Page == 0 {
		req.Page = 1
	}
	if req.PageSize == 0 {
		req.PageSize = 10
	}

	result, err := mcpToolService.GetToolList(t_utils.GetTenantDB(c), c.Request.Context(), req)
	if err != nil {
		global.GVA_LOG.Error("获取工具列表失败", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithData(result, c)
}

// GetToolSchema 获取工具Schema
// @Tags MCP工具管理
// @Summary 获取工具参数Schema
// @Description 获取指定服务器和工具的参数定义
// @Accept json
// @Produce json
// @Param server path string true "服务器名称"
// @Param tool path string true "工具名称"
// @Success 200 {object} response.Response{data=mcpRes.ToolSchemaResponse,msg=string} "获取成功"
// @Router /mcp/{server}/{tool}/schema [get]
func (a *MCPToolApi) GetToolSchema(c *gin.Context) {
	serverName := c.Param("server")
	toolName := c.Param("tool")

	if serverName == "" || toolName == "" {
		response.FailWithMessage("服务器名称和工具名称不能为空", c)
		return
	}

	result, err := mcpToolService.GetToolSchema(t_utils.GetTenantDB(c), c.Request.Context(), serverName, toolName)
	if err != nil {
		global.GVA_LOG.Error("获取工具Schema失败", zap.Error(err))
		response.FailWithMessage("获取失败: "+err.Error(), c)
		return
	}

	response.OkWithData(result, c)
}

// CallTool 调用MCP工具
// @Tags MCP工具管理
// @Summary 动态调用MCP工具
// @Description 调用指定的MCP工具，这是动态生成的API端点
// @Accept json
// @Produce json
// @Param server path string true "服务器名称"
// @Param tool path string true "工具名称"
// @Param data body mcpReq.ToolCallRequest true "工具参数"
// @Success 200 {object} response.Response{data=mcpRes.ToolCallResponse,msg=string} "调用成功"
// @Router /mcp/{server}/{tool} [post]
func (a *MCPToolApi) CallTool(c *gin.Context) {
	serverName := c.Param("server")
	toolName := c.Param("tool")

	if serverName == "" || toolName == "" {
		response.FailWithMessage("服务器名称和工具名称不能为空", c)
		return
	}

	var req mcpReq.ToolCallRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.FailWithMessage("参数错误: "+err.Error(), c)
		return
	}

	result, err := mcpToolService.CallTool(t_utils.GetTenantDB(c), c.Request.Context(), serverName, toolName, req)
	if err != nil {
		global.GVA_LOG.Error("调用工具失败", zap.Error(err))
		response.FailWithMessage("调用失败: "+err.Error(), c)
		return
	}

	// 根据工具执行结果返回不同的HTTP状态
	if result.Success {
		response.OkWithData(result, c)
	} else {
		// 工具执行失败，但HTTP请求成功
		response.FailWithDetailed(result, "工具执行失败", c)
	}
}
