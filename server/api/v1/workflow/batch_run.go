package workflow

import (
	"github.com/gin-gonic/gin"
	"github.com/go-playground/validator/v10"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/common/response"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system/request"
	workflowService "github.com/flipped-aurora/gin-vue-admin/server/service/workflow"
)

// BatchRunWorkflowReq 批量执行工作流请求
type BatchRunWorkflowReq struct {
	WorkflowID  int                      `json:"workflow_id" binding:"required" validate:"min=1"`         // 工作流ID
	Inputs      []map[string]interface{} `json:"inputs" binding:"required" validate:"min=1,max=500,dive"` // 输入数据数组
	AssignedTo  *int                     `json:"assigned_to,omitempty" validate:"omitempty,min=1"`        // 指定任务分配用户ID
	Priority    *int                     `json:"priority,omitempty" validate:"omitempty,min=1,max=5"`     // 优先级(1-5)
	CallbackURL *string                  `json:"callback_url,omitempty" validate:"omitempty,url"`         // 完成回调地址
}

// BatchRunWorkflowResp 批量执行工作流响应
type BatchRunWorkflowResp struct {
	BatchID      uint   `json:"batch_id"`      // 批量执行ID
	Status       string `json:"status"`        // 状态
	Message      string `json:"message"`       // 消息
	TotalCount   int    `json:"total_count"`   // 总任务数
	SuccessCount int    `json:"success_count"` // 成功任务数
	FailedCount  int    `json:"failed_count"`  // 失败任务数
	RunningCount int    `json:"running_count"` // 运行中任务数
	PendingCount int    `json:"pending_count"` // 待执行任务数
}

// CancelBatchExecutionReq 取消批量执行请求
type CancelBatchExecutionReq struct {
	BatchID uint   `json:"batch_id" binding:"required" validate:"min=1"` // 批量执行ID
	Reason  string `json:"reason" validate:"max=500"`                    // 取消原因
}

// CancelBatchExecutionResp 取消批量执行响应
type CancelBatchExecutionResp struct {
	BatchID uint   `json:"batch_id"` // 批量执行ID
	Status  string `json:"status"`   // 状态
	Message string `json:"message"`  // 消息
}

// BatchRunWorkflow 批量执行工作流
// @Tags Workflow
// @Summary 批量执行工作流
// @Description 批量触发指定工作流，多条输入一次性执行
// @Accept application/json
// @Produce application/json
// @Param data body BatchRunWorkflowReq true "批量执行请求参数"
// @Success 200 {object} response.Response{data=BatchRunWorkflowResp} "{"success":true,"data":{},"msg":"执行成功"}"
// @Router /workflows/batch_run [post]
func (w *WorkflowApi) BatchRunWorkflow(c *gin.Context) {
	var req BatchRunWorkflowReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.ReturnWithDataCodeInfo(nil, 500, "参数绑定失败: "+err.Error(), c)
		return
	}

	// 参数验证
	validate := validator.New()
	err = validate.Struct(req)
	if err != nil {
		response.ReturnWithDataCodeInfo(nil, 500, "参数验证失败: "+err.Error(), c)
		return
	}

	// 获取用户ID（从JWT token中获取）
	userID := getUserIDFromContext(c)

	// 调用服务层
	serviceReq := &workflowService.BatchWorkflowReq{
		WorkflowID:  req.WorkflowID,
		Inputs:      req.Inputs,
		AssignedTo:  req.AssignedTo,
		Priority:    req.Priority,
		CallbackURL: req.CallbackURL,
		ExecutedBy:  userID,
	}

	resp, err := workflowService.BatchRunWorkflow(c.Request.Context(), serviceReq)
	if err != nil {
		global.GVA_LOG.Error("批量执行工作流失败: " + err.Error())
		response.ReturnWithDataCodeInfo(nil, 500, "执行失败: "+err.Error(), c)
		return
	}

	// 转换响应
	apiResp := BatchRunWorkflowResp{
		BatchID:      resp.BatchID,
		Status:       resp.Status,
		Message:      resp.Message,
		TotalCount:   resp.TotalCount,
		SuccessCount: resp.SuccessCount,
		FailedCount:  resp.FailedCount,
		RunningCount: resp.RunningCount,
		PendingCount: resp.PendingCount,
	}

	response.ReturnWithDataCodeInfo(apiResp, 200, "success", c)
}

// CancelBatchExecution 取消批量执行
// @Tags Workflow
// @Summary 取消批量执行
// @Description 取消正在进行的批量工作流执行
// @Accept application/json
// @Produce application/json
// @Param data body CancelBatchExecutionReq true "取消批量执行请求参数"
// @Success 200 {object} response.Response{data=CancelBatchExecutionResp} "{"success":true,"data":{},"msg":"取消成功"}"
// @Router /workflows/batch_cancel [post]
func (w *WorkflowApi) CancelBatchExecution(c *gin.Context) {
	var req CancelBatchExecutionReq
	err := c.ShouldBindJSON(&req)
	if err != nil {
		response.FailWithMessage("参数绑定失败: "+err.Error(), c)
		return
	}

	// 参数验证
	validate := validator.New()
	err = validate.Struct(req)
	if err != nil {
		response.FailWithMessage("参数验证失败: "+err.Error(), c)
		return
	}

	// 设置默认取消原因
	reason := req.Reason
	if reason == "" {
		reason = "用户手动取消"
	}

	// 调用服务层
	err = workflowService.CancelBatchExecution(req.BatchID, reason)
	if err != nil {
		global.GVA_LOG.Error("取消批量执行失败: " + err.Error())
		response.FailWithMessage("取消失败: "+err.Error(), c)
		return
	}

	// 构建响应
	apiResp := CancelBatchExecutionResp{
		BatchID: req.BatchID,
		Status:  "cancelled",
		Message: "批量执行已取消",
	}

	response.OkWithData(apiResp, c)
}

// getUserIDFromContext 从上下文获取用户ID
func getUserIDFromContext(c *gin.Context) uint {
	if claims, exists := c.Get("claims"); exists {
		if waitUse, ok := claims.(*request.CustomClaims); ok {
			return waitUse.BaseClaims.ID
		}
	}
	return 1 // 默认用户ID，实际应该从JWT中获取
}

// WorkflowApi 工作流API结构体
type WorkflowApi struct{}

// 实例化API
var WorkflowApiInstance = new(WorkflowApi)
