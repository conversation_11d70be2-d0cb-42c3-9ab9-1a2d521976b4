package utils

import (
	"errors"
	"fmt"
	"time"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"github.com/flipped-aurora/gin-vue-admin/server/model/system"
	"github.com/flipped-aurora/gin-vue-admin/server/plugin/tenants/model"
	"github.com/google/uuid"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

// MigrationResult 迁移结果
type MigrationResult struct {
	TotalUsers        int      `json:"total_users"`
	MigratedUsers     int      `json:"migrated_users"`
	SkippedUsers      int      `json:"skipped_users"`
	ErrorUsers        int      `json:"error_users"`
	TotalRelations    int      `json:"total_relations"`
	MigratedRelations int      `json:"migrated_relations"`
	Errors            []string `json:"errors"`
}

// MigrateTenantUsersToGlobal 将租户用户数据迁移到全局用户表
func MigrateTenantUsersToGlobal() (*MigrationResult, error) {
	result := &MigrationResult{
		Errors: make([]string, 0),
	}

	global.GVA_LOG.Info("开始数据迁移：将租户用户数据迁移到全局用户表")

	// 获取所有租户配置
	var tenantConfigs []model.Config
	if err := global.GVA_DB.Find(&tenantConfigs).Error; err != nil {
		return result, fmt.Errorf("获取租户配置失败: %v", err)
	}

	global.GVA_LOG.Info("找到租户配置", zap.Int("count", len(tenantConfigs)))

	// 遍历每个租户
	for _, config := range tenantConfigs {
		if err := migrateTenantUsers(config, result); err != nil {
			global.GVA_LOG.Error("迁移租户用户失败",
				zap.String("tenantId", config.TenantId),
				zap.Error(err))
			result.Errors = append(result.Errors, fmt.Sprintf("租户%s: %v", config.TenantId, err))
		}
	}

	global.GVA_LOG.Info("数据迁移完成",
		zap.Int("总用户数", result.TotalUsers),
		zap.Int("迁移用户数", result.MigratedUsers),
		zap.Int("跳过用户数", result.SkippedUsers),
		zap.Int("错误用户数", result.ErrorUsers),
		zap.Int("总关系数", result.TotalRelations),
		zap.Int("迁移关系数", result.MigratedRelations))

	return result, nil
}

// migrateTenantUsers 迁移单个租户的用户数据
func migrateTenantUsers(config model.Config, result *MigrationResult) error {
	// 根据租户配置中的UserId获取对应的用户（一个租户配置对应一个用户）
	if config.UserId == 0 {
		global.GVA_LOG.Info("租户配置没有关联用户，跳过", zap.String("tenantId", config.TenantId))
		return nil
	}

	var tenantUser model.User
	if err := global.GVA_DB.Where("id = ?", config.UserId).First(&tenantUser).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			global.GVA_LOG.Warn("租户配置关联的用户不存在",
				zap.String("tenantId", config.TenantId),
				zap.Uint("userId", config.UserId))
			return nil
		}
		return fmt.Errorf("获取租户用户失败: %v", err)
	}

	global.GVA_LOG.Info("处理租户用户",
		zap.String("tenantId", config.TenantId),
		zap.String("username", *tenantUser.UserName))

	result.TotalUsers++

	// 迁移这个用户
	if err := migrateSingleUser(tenantUser, config, result); err != nil {
		global.GVA_LOG.Error("迁移单个用户失败",
			zap.String("tenantId", config.TenantId),
			zap.String("username", *tenantUser.UserName),
			zap.Error(err))
		result.ErrorUsers++
		result.Errors = append(result.Errors,
			fmt.Sprintf("租户%s用户%s: %v", config.TenantId, *tenantUser.UserName, err))
	}

	return nil
}

// migrateSingleUser 迁移单个用户
func migrateSingleUser(tenantUser model.User, config model.Config, result *MigrationResult) error {
	// 生成全局唯一用户名（防止冲突）
	globalUsername := fmt.Sprintf("%s@%s", *tenantUser.UserName, config.TenantId)

	// 检查全局用户是否已存在
	var existingGlobalUser system.GlobalUser
	if !errors.Is(global.GVA_DB.Where("username = ?", globalUsername).First(&existingGlobalUser).Error, gorm.ErrRecordNotFound) {
		global.GVA_LOG.Info("全局用户已存在，跳过", zap.String("username", globalUsername))
		result.SkippedUsers++
		return nil
	}

	// 创建全局用户
	// 生成默认邮箱地址，避免空字符串导致唯一索引冲突
	defaultEmail := fmt.Sprintf("%s@%s.local", *tenantUser.UserName, config.TenantId)
	globalUser := system.GlobalUser{
		UUID:     uuid.New(),
		Username: globalUsername,
		Password: *tenantUser.Password, // 密码已经是加密的
		Email:    defaultEmail,
		TenantId: config.TenantId,
		IsActive: *tenantUser.Enable,
	}
	globalUser.CreatedAt = tenantUser.CreatedAt
	globalUser.UpdatedAt = tenantUser.UpdatedAt

	if err := global.GVA_DB.Create(&globalUser).Error; err != nil {
		return fmt.Errorf("创建全局用户失败: %v", err)
	}

	result.MigratedUsers++
	global.GVA_LOG.Info("全局用户创建成功", zap.String("username", globalUsername))

	// 创建用户租户关系
	relation := system.UserTenantRelation{
		GlobalUserId: globalUser.ID,
		TenantId:     config.TenantId,
		TenantUserId: tenantUser.ID,
		IsPrimary:    true, // 设为主租户
		CreatedAt:    time.Now(),
		UpdatedAt:    time.Now(),
	}

	if err := global.GVA_DB.Create(&relation).Error; err != nil {
		// 回滚：删除已创建的全局用户
		global.GVA_DB.Delete(&globalUser)
		return fmt.Errorf("创建用户租户关系失败: %v", err)
	}

	result.TotalRelations++
	result.MigratedRelations++
	global.GVA_LOG.Info("用户租户关系创建成功",
		zap.String("username", globalUsername),
		zap.String("tenantId", config.TenantId))

	return nil
}

// ValidateMigrationData 验证迁移数据的完整性
func ValidateMigrationData() (*MigrationResult, error) {
	result := &MigrationResult{
		Errors: make([]string, 0),
	}

	global.GVA_LOG.Info("开始验证迁移数据完整性")

	// 统计全局用户数量
	var globalUserCount int64
	if err := global.GVA_DB.Model(&system.GlobalUser{}).Count(&globalUserCount).Error; err != nil {
		return result, fmt.Errorf("统计全局用户失败: %v", err)
	}

	// 统计用户租户关系数量
	var relationCount int64
	if err := global.GVA_DB.Model(&system.UserTenantRelation{}).Count(&relationCount).Error; err != nil {
		return result, fmt.Errorf("统计用户租户关系失败: %v", err)
	}

	// 统计原始租户用户数量
	var tenantUserCount int64
	if err := global.GVA_DB.Model(&model.User{}).Count(&tenantUserCount).Error; err != nil {
		return result, fmt.Errorf("统计租户用户失败: %v", err)
	}

	result.TotalUsers = int(tenantUserCount)
	result.MigratedUsers = int(globalUserCount)
	result.TotalRelations = int(relationCount)
	result.MigratedRelations = int(relationCount)

	// 检查数据一致性
	if globalUserCount != relationCount {
		result.Errors = append(result.Errors,
			fmt.Sprintf("数据不一致：全局用户数量(%d) != 用户租户关系数量(%d)", globalUserCount, relationCount))
	}

	global.GVA_LOG.Info("数据验证完成",
		zap.Int64("全局用户数", globalUserCount),
		zap.Int64("用户租户关系数", relationCount),
		zap.Int64("原租户用户数", tenantUserCount))

	return result, nil
}

// RollbackMigration 回滚迁移（删除所有迁移的数据）
func RollbackMigration() error {
	global.GVA_LOG.Info("开始回滚迁移数据")

	// 删除用户租户关系
	if err := global.GVA_DB.Exec("DELETE FROM user_tenant_relations").Error; err != nil {
		return fmt.Errorf("删除用户租户关系失败: %v", err)
	}

	// 删除全局用户
	if err := global.GVA_DB.Exec("DELETE FROM global_users").Error; err != nil {
		return fmt.Errorf("删除全局用户失败: %v", err)
	}

	global.GVA_LOG.Info("迁移数据回滚完成")
	return nil
}

// GetMigrationStatus 获取迁移状态
func GetMigrationStatus() (map[string]interface{}, error) {
	status := make(map[string]interface{})

	// 检查表是否存在
	status["global_users_table_exists"] = global.GVA_DB.Migrator().HasTable(&system.GlobalUser{})
	status["user_tenant_relations_table_exists"] = global.GVA_DB.Migrator().HasTable(&system.UserTenantRelation{})

	// 统计数据
	var globalUserCount int64
	global.GVA_DB.Model(&system.GlobalUser{}).Count(&globalUserCount)
	status["global_users_count"] = globalUserCount

	var relationCount int64
	global.GVA_DB.Model(&system.UserTenantRelation{}).Count(&relationCount)
	status["user_tenant_relations_count"] = relationCount

	var tenantUserCount int64
	global.GVA_DB.Model(&model.User{}).Count(&tenantUserCount)
	status["tenant_users_count"] = tenantUserCount

	// 检查索引状态
	status["indexes"] = CheckIndexStatus(global.GVA_DB)

	return status, nil
}
