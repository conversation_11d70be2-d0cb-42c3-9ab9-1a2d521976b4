package utils

import (
	"fmt"

	"github.com/flipped-aurora/gin-vue-admin/server/global"
	"go.uber.org/zap"
	"gorm.io/gorm"
)

type IndexInfo struct {
	TableName  string
	IndexName  string
	ColumnName string
	IsUnique   bool
	Required   bool
}

// CheckAndCreateIndexes 检查并创建缺失的索引
func CheckAndCreateIndexes(db *gorm.DB) error {
	requiredIndexes := []IndexInfo{
		{TableName: "global_users", IndexName: "uk_username", ColumnName: "username", IsUnique: true, Required: true},
		{TableName: "global_users", IndexName: "uk_email", ColumnName: "email", IsUnique: true, Required: false},
		{TableName: "global_users", IndexName: "idx_tenant_id", ColumnName: "tenant_id", IsUnique: false, Required: true},
		{TableName: "t_user", IndexName: "uk_user_name", ColumnName: "user_name", IsUnique: true, Required: true},
		{TableName: "t_config", IndexName: "uk_tenant_id", ColumnName: "tenant_id", IsUnique: true, Required: true},
		{TableName: "sys_users", IndexName: "uk_username", ColumnName: "username", IsUnique: true, Required: true},
	}

	for _, indexInfo := range requiredIndexes {
		if err := ensureIndexExists(db, indexInfo); err != nil {
			global.GVA_LOG.Error("创建索引失败",
				zap.String("table", indexInfo.TableName),
				zap.String("index", indexInfo.IndexName),
				zap.Error(err))
			if indexInfo.Required {
				return err
			}
		}
	}

	return nil
}

func ensureIndexExists(db *gorm.DB, indexInfo IndexInfo) error {
	// 检查索引是否存在
	var count int64
	query := `
		SELECT COUNT(*) FROM information_schema.statistics 
		WHERE table_schema = DATABASE() 
		AND table_name = ? 
		AND index_name = ?
	`

	if err := db.Raw(query, indexInfo.TableName, indexInfo.IndexName).Scan(&count).Error; err != nil {
		return err
	}

	if count > 0 {
		global.GVA_LOG.Info("索引已存在",
			zap.String("table", indexInfo.TableName),
			zap.String("index", indexInfo.IndexName))
		return nil
	}

	// 创建索引
	var sql string
	if indexInfo.IsUnique {
		sql = fmt.Sprintf("CREATE UNIQUE INDEX %s ON %s (%s)",
			indexInfo.IndexName, indexInfo.TableName, indexInfo.ColumnName)
	} else {
		sql = fmt.Sprintf("CREATE INDEX %s ON %s (%s)",
			indexInfo.IndexName, indexInfo.TableName, indexInfo.ColumnName)
	}

	if err := db.Exec(sql).Error; err != nil {
		return err
	}

	global.GVA_LOG.Info("索引创建成功",
		zap.String("table", indexInfo.TableName),
		zap.String("index", indexInfo.IndexName))

	return nil
}

// CheckTableExists 检查表是否存在
func CheckTableExists(db *gorm.DB, tableName string) bool {
	return db.Migrator().HasTable(tableName)
}

// CheckIndexStatus 检查所有索引状态
func CheckIndexStatus(db *gorm.DB) map[string]bool {
	requiredIndexes := []IndexInfo{
		{TableName: "global_users", IndexName: "uk_username", ColumnName: "username", IsUnique: true, Required: true},
		{TableName: "global_users", IndexName: "uk_email", ColumnName: "email", IsUnique: true, Required: false},
		{TableName: "global_users", IndexName: "idx_tenant_id", ColumnName: "tenant_id", IsUnique: false, Required: true},
		{TableName: "t_user", IndexName: "uk_user_name", ColumnName: "user_name", IsUnique: true, Required: true},
		{TableName: "t_config", IndexName: "uk_tenant_id", ColumnName: "tenant_id", IsUnique: true, Required: true},
		{TableName: "sys_users", IndexName: "uk_username", ColumnName: "username", IsUnique: true, Required: true},
	}

	indexStatus := make(map[string]bool)

	for _, indexInfo := range requiredIndexes {
		var count int64
		query := `
			SELECT COUNT(*) FROM information_schema.statistics 
			WHERE table_schema = DATABASE() 
			AND table_name = ? 
			AND index_name = ?
		`

		if err := db.Raw(query, indexInfo.TableName, indexInfo.IndexName).Scan(&count).Error; err != nil {
			indexStatus[fmt.Sprintf("%s.%s", indexInfo.TableName, indexInfo.IndexName)] = false
			continue
		}

		indexStatus[fmt.Sprintf("%s.%s", indexInfo.TableName, indexInfo.IndexName)] = count > 0
	}

	return indexStatus
}
