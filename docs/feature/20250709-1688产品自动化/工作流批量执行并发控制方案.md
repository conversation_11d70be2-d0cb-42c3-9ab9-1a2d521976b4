# 工作流批量执行并发控制方案

## 1. 概述

### 1.1 问题背景

在Gaia-Admin系统中，工作流批量执行功能面临着底层服务并发能力不强的挑战。当大规模请求到达时，会导致底层服务过载甚至崩溃。这种大规模请求主要分为两种情况：

1. **单次请求多个任务**：一次批量执行包含大量工作流任务
2. **多次请求累积**：多个批量执行同时进行，累积并发数过高

### 1.2 解决方案

本方案采用**双层并发控制架构**，结合Go语言最佳实践，实现了：
- 全局系统级别的并发控制
- 单个批量请求内部的并发控制
- 完善的监控和指标系统
- 配置化的并发管理

## 2. 架构设计

### 2.1 双层并发控制架构

```
┌─────────────────────────────────────────────────────────────┐
│                    请求入口                                   │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│              全局并发控制器                                   │
│          GlobalConcurrencyController                        │
│          (最大2个全局工作线程)                                │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│             批量执行控制器                                    │
│          BatchExecutionController                           │
│          (单批次最大2个并发)                                   │
└─────────────────────┬───────────────────────────────────────┘
                      │
┌─────────────────────▼───────────────────────────────────────┐
│              工作流核心执行                                   │
│             ExecuteWorkflowCore                             │
│            (调用底层AI服务)                                   │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 核心组件

#### 2.2.1 GlobalConcurrencyController
- **功能**：全局系统级并发控制
- **实现**：单例模式，使用Channel信号量
- **特点**：确保整个系统不超过配置的最大并发数

#### 2.2.2 BatchExecutionController  
- **功能**：单个批量请求内部并发控制
- **实现**：每个批量请求一个实例
- **特点**：控制单次批量执行的内部并发

#### 2.2.3 ConcurrencyMetricsService
- **功能**：并发控制监控和指标收集
- **实现**：提供REST API和健康检查
- **特点**：实时监控系统负载状态

## 3. 核心实现

### 3.1 全局并发控制

```go
// ExecuteWorkflowCore 核心工作流执行逻辑
func ExecuteWorkflowCore(ctx context.Context, workflowRunID uint, inputData map[string]interface{}) error {
    // 1. 获取全局并发控制器
    globalController := GetGlobalConcurrencyController()
    
    // 2. 请求获取工作线程资源
    err := globalController.AcquireWorker(ctx, workflowRunID)
    if err != nil {
        return fmt.Errorf("获取全局工作线程资源失败: %w", err)
    }
    
    // 3. 确保在函数退出时释放资源
    defer globalController.ReleaseWorker(workflowRunID)
    
    // 4. 执行具体的工作流逻辑
    return runAiImage.RunWorkflow(ctx, ...)
}
```

### 3.2 并发控制流程

```
请求到达 → 全局并发队列 → 获取资源 → 执行工作流 → 释放资源
     ↓           ↓           ↓          ↓          ↓
   排队等待   → 信号量控制  → 执行限制   → 处理任务   → 资源回收
```

### 3.3 关键特性

#### 3.3.1 Go语言最佳实践
- **Channel + Select**：优雅的并发控制和超时处理
- **Context传播**：支持取消和超时机制
- **sync.RWMutex**：线程安全的指标统计
- **单例模式**：全局统一的并发控制
- **defer模式**：确保资源正确释放

#### 3.3.2 错误处理和超时
```go
select {
case gc.semaphore <- struct{}{}:
    // 成功获取资源
    return nil
case <-ctx.Done():
    // 上下文取消或超时
    return fmt.Errorf("获取工作线程超时: %w", ctx.Err())
}
```

## 4. 配置管理

### 4.1 配置文件结构

```yaml
# 工作流并发控制配置
workflow-concurrency:
  max-global-workers: 2           # 全局最大并发工作线程数（推荐2-5）
  default-batch-concurrency: 2    # 默认批量并发数（不能超过全局最大值）
  enable-metrics: true            # 是否启用指标收集
  metrics-reset-interval: 24      # 指标重置间隔（小时）
  queue-timeout-ms: 30000         # 队列等待超时时间（毫秒）
  enable-health-check: true       # 是否启用健康检查
```

### 4.2 配置说明

| 配置项 | 类型 | 默认值 | 说明 |
|--------|------|--------|------|
| max-global-workers | int | 2 | 全局最大并发工作线程数，建议根据底层服务能力设置 |
| default-batch-concurrency | int | 2 | 默认批量并发数，不能超过全局最大值 |
| enable-metrics | bool | true | 是否启用指标收集，建议生产环境开启 |
| metrics-reset-interval | int | 24 | 指标重置间隔（小时），避免数据累积过多 |
| queue-timeout-ms | int | 30000 | 队列等待超时时间（毫秒），防止请求无限等待 |
| enable-health-check | bool | true | 是否启用健康检查，便于监控系统状态 |

## 5. 监控和指标

### 5.1 系统状态监控

#### 5.1.1 实时指标
- **活跃工作线程数**：当前正在执行的工作流数量
- **排队请求数**：等待执行的请求数量
- **容量利用率**：当前并发使用率百分比
- **系统负载状态**：low/medium/high/critical

#### 5.1.2 性能指标
- **总请求数**：累计处理的请求总数
- **成功请求数**：成功完成的请求数量
- **失败请求数**：执行失败的请求数量
- **平均等待时间**：请求在队列中的平均等待时间
- **最大等待时间**：请求在队列中的最长等待时间

### 5.2 健康检查

```json
{
  "status": "healthy",
  "concurrency": {
    "max_workers": 2,
    "active_workers": 1,
    "queued_requests": 0,
    "utilization": 50.0
  },
  "metrics": {
    "total_requests": 150,
    "processed_requests": 145,
    "failed_requests": 5,
    "average_wait_time": 1200,
    "max_wait_time": 5000
  },
  "timestamp": **********
}
```

### 5.3 系统建议

系统会根据当前负载状况自动生成运维建议：

- **负载过高**：建议暂停新任务，等待当前任务完成
- **负载正常**：可以正常提交任务
- **资源充足**：可以考虑增加并发数提高效率
- **有排队**：预计等待时间较长，建议合理安排

## 6. 使用指南

### 6.1 系统部署

1. **配置文件设置**
   ```yaml
   workflow-concurrency:
     max-global-workers: 2
     default-batch-concurrency: 2
   ```

2. **服务启动**
   - 系统启动时自动初始化全局并发控制器
   - 配置验证和默认值设置
   - 指标服务启动

### 6.2 运维监控

1. **实时状态查询**
   ```bash
   GET /api/workflow/concurrency/status
   ```

2. **健康检查**
   ```bash
   GET /api/workflow/health
   ```

3. **指标重置**
   ```bash
   POST /api/workflow/concurrency/reset-metrics
   ```

### 6.3 性能调优

#### 6.3.1 并发数调整原则
- **底层服务能力**：根据AI服务的实际处理能力设置
- **系统资源**：考虑服务器CPU、内存等资源限制
- **业务需求**：平衡处理速度和系统稳定性

#### 6.3.2 推荐配置
- **小规模部署**：max-global-workers = 1-2
- **中等规模部署**：max-global-workers = 2-3  
- **大规模部署**：max-global-workers = 3-5

#### 6.3.3 监控告警
- **队列堆积**：queued_requests > 10 时告警
- **等待时间过长**：average_wait_time > 60秒时告警
- **失败率过高**：failure_rate > 10% 时告警

## 7. 故障排查

### 7.1 常见问题

#### 7.1.1 请求排队时间过长
**现象**：客户端请求响应慢，系统负载显示high/critical
**原因**：并发数设置过低或底层服务处理慢
**解决**：
1. 检查底层AI服务状态
2. 适当增加max-global-workers
3. 优化工作流执行逻辑

#### 7.1.2 系统资源不足
**现象**：服务器CPU/内存使用率过高
**原因**：并发数设置过高，超出系统承载能力
**解决**：
1. 降低max-global-workers设置
2. 优化代码性能
3. 增加服务器资源

#### 7.1.3 工作流执行失败
**现象**：failed_requests 指标持续增加
**原因**：底层服务不稳定或网络问题
**解决**：
1. 检查底层服务日志
2. 检查网络连接状况
3. 增加重试机制

### 7.2 调试方法

#### 7.2.1 日志分析
```bash
# 查看并发控制日志
grep "🔧\|🎯\|✅\|❌" server/log/server.log

# 查看工作流执行日志
grep "WorkflowRunID" server/log/server.log
```

#### 7.2.2 指标监控
```bash
# 查询实时状态
curl http://localhost:8888/api/workflow/concurrency/status

# 检查健康状态
curl http://localhost:8888/api/workflow/health
```

## 8. 性能测试

### 8.1 测试场景

#### 8.1.1 单次大批量测试
- **测试数据**：单次提交100个工作流任务
- **预期行为**：并发数限制在2个，其余排队等待
- **监控指标**：queue_requests、average_wait_time

#### 8.1.2 多次并发测试
- **测试数据**：同时提交5个批量请求，每个包含20个任务
- **预期行为**：全局并发限制在2个，批量间有序执行
- **监控指标**：active_workers、utilization

#### 8.1.3 压力测试
- **测试数据**：持续提交大量批量请求
- **预期行为**：系统稳定运行，无内存泄漏或崩溃
- **监控指标**：memory_usage、cpu_usage、error_rate

### 8.2 性能基准

| 指标 | 目标值 | 告警阈值 |
|------|--------|----------|
| 平均等待时间 | < 30秒 | > 60秒 |
| 系统利用率 | 70-90% | > 95% |
| 失败率 | < 5% | > 10% |
| 内存使用 | < 1GB | > 2GB |

## 9. 版本更新

### 9.1 当前版本 v1.0.0

**新增功能**：
- ✅ 双层并发控制架构
- ✅ 全局并发控制器
- ✅ 配置化管理
- ✅ 实时监控指标
- ✅ 健康检查机制
- ✅ 系统负载评估

**技术特点**：
- ✅ Go语言并发最佳实践
- ✅ Channel + Select 模式
- ✅ Context 传播机制
- ✅ 线程安全设计
- ✅ 优雅的资源管理

### 9.2 后续规划

**v1.1.0 计划**：
- 🔄 动态并发数调整
- 🔄 更详细的性能分析
- 🔄 集群模式支持
- 🔄 Prometheus集成

## 10. 总结

本并发控制方案通过双层架构设计，有效解决了工作流批量执行中的并发控制问题：

1. **解决核心问题**：防止底层服务过载，确保系统稳定运行
2. **技术先进性**：采用Go语言并发最佳实践，代码优雅高效
3. **运维友好**：完善的监控指标和健康检查机制
4. **配置灵活**：支持根据实际环境调整并发参数
5. **扩展性强**：架构设计支持后续功能扩展

该方案已在测试环境验证，能够有效控制并发数在2个以内，确保底层AI服务的稳定运行。 