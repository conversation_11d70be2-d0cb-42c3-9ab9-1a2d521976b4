# 工作流回调机制详解

| 修订时间 | 修改人 | 修改内容说明 |
| --- | --- | --- |
| 2025.07.20 | $\color{#0089FF}{@麦友铭}$ | 创建回调机制专门文档，修复失败回调和数据存储问题 |

## 1. 概述

### 1.1 回调机制目标
工作流回调机制确保外部系统能够及时获得工作流执行结果，支持成功和失败两种情况的统一处理。

### 1.2 核心特性
- **统一回调格式**：成功和失败使用相同的数据结构
- **失败数据填充**：失败时从inputs获取数据，确保回调数据完整
- **数据持久化**：回调数据存储到 `workflow_runs.callback_data` 字段
- **重试机制**：最多3次重试，确保回调送达
- **批量和单独执行**：支持两种执行模式的回调

## 2. 回调时机

### 2.1 单独执行工作流
- **成功时**：工作流执行完成立即回调
- **失败时**：最终失败时回调（重试结束后）
- **中断时**：正常中断（等待用户确认）不发送回调

### 2.2 批量执行工作流
- **单个工作流完成**：每个工作流完成（成功或失败）立即发送单个回调
- **批量执行完成**：整个批量执行完成后不再发送额外回调

## 3. 回调数据格式

### 3.1 HTTP回调格式
回调采用 `application/x-www-form-urlencoded` 格式：

```http
POST /callback/url HTTP/1.1
Content-Type: application/x-www-form-urlencoded

data={"spu":"SPU123",...}&code=200&info=处理成功
```

### 3.2 成功回调数据结构
```json
{
  "code": 200,
  "info": "处理成功",
  "data": {
    "spu": "SPU123456",
    "unqId": "abc123def456",
    "isSku": true,
    "skuNumber": "SKU789012",
    "picId": "pic123456",
    "imageUrl": "https://example.com/processed.jpg",
    "noBackGroundImageUrl": "https://example.com/nobg.jpg",
    "resolutionFilter": "PASS",
    "marketingFilter": "PASS",
    "extractedText": ["检测到的文本1", "检测到的文本2"],
    "status": 200,
    "watermarkRemoved": true,
    "logoRemoved": false
  }
}
```

### 3.3 失败回调数据结构
```json
{
  "code": 500,
  "info": "图片处理失败：网络超时",
  "data": {
    "spu": "SPU123456",
    "unqId": "abc123def456", 
    "isSku": true,
    "skuNumber": "SKU789012",
    "picId": "pic123456",
    "imageUrl": "https://example.com/original.jpg",
    "noBackGroundImageUrl": "",
    "resolutionFilter": "",
    "marketingFilter": "",
    "extractedText": [],
    "status": 500,
    "watermarkRemoved": false,
    "logoRemoved": false
  }
}
```

## 4. 数据来源逻辑

### 4.1 成功时数据来源
1. **优先使用 outputs**：工作流执行成功后的输出数据
2. **备用 inputs**：如果 outputs 为空，使用 inputs 数据

### 4.2 失败时数据来源
1. **主要使用 inputs**：从工作流输入数据获取基础信息
2. **默认值填充**：处理结果相关字段使用默认空值
3. **状态码设置**：status 设为 500，表示处理失败

### 4.3 字段映射规则
| 字段名 | 成功时来源 | 失败时来源 | 默认值 |
|--------|------------|------------|--------|
| spu | outputs > inputs | inputs | "" |
| unqId | outputs > inputs | inputs | "" |
| isSku | outputs > inputs | inputs | false |
| imageUrl | outputs > inputs | inputs | "" |
| noBackGroundImageUrl | outputs | - | "" |
| resolutionFilter | outputs | - | "" |
| marketingFilter | outputs | - | "" |
| extractedText | outputs | - | [] |
| watermarkRemoved | outputs | - | false |
| logoRemoved | outputs | - | false |

## 5. 重试机制

### 5.1 重试策略
- **最大重试次数**：3次
- **重试间隔**：1秒、2秒、3秒（线性递增）
- **重试条件**：HTTP请求失败或响应状态码非2xx

### 5.2 重试逻辑
```go
for retry := 0; retry < maxRetries; retry++ {
    err := s.sendCallback(url, data, code, info)
    if err == nil {
        return nil // 成功，退出重试
    }
    
    if retry < maxRetries-1 {
        waitTime := time.Duration(retry+1) * time.Second
        time.Sleep(waitTime) // 等待后重试
    }
}
```

## 6. 数据存储机制

### 6.1 存储位置
回调数据存储在 `workflow_runs.callback_data` 字段中，格式为JSON字符串。

### 6.2 存储内容
```json
{
  "code": 200,
  "info": "处理成功",
  "data": { ... } // 完整的回调数据
}
```

### 6.3 存储时机
- **处理完成时**：生成回调数据后立即存储
- **发送前存储**：先存储后发送，确保数据不丢失
- **存储失败处理**：存储失败不阻止回调发送，只记录错误日志

## 7. 工作流128专用处理

### 7.1 支持的工作流
当前专门支持工作流ID 128（智能抠图工作流）的回调处理。

### 7.2 字段兼容性
- 支持 `imageUrl` 和 `image_url` 两种格式
- 支持 `noBackGroundImageUrl` 和 `no_background_image_url` 两种格式
- 自动类型转换：`[]interface{}` → `[]string`

### 7.3 扩展性设计
通过 `CallbackProcessorFactory` 支持其他工作流的回调处理器注册：

```go
// 注册新的工作流回调处理器
factory.RegisterProcessor(NewWorkflowXXXCallbackProcessor())
```

## 8. 错误处理

### 8.1 处理器错误
- **工作流ID不匹配**：返回错误，不发送回调
- **数据处理失败**：记录错误，尝试发送错误回调

### 8.2 网络错误
- **连接超时**：30秒超时，重试机制处理
- **HTTP错误**：记录详细错误信息，重试机制处理

### 8.3 存储错误
- **数据库连接失败**：记录错误，不阻止回调发送
- **JSON序列化失败**：记录错误，跳过存储步骤

## 9. 监控与日志

### 9.1 关键日志
```
✅ 单个工作流回调发送成功: WorkflowRun ID=123
❌ 发送单个工作流回调失败: WorkflowRun ID=123, 错误: 网络超时
⚠️ 存储回调数据失败: JSON序列化错误
📤 发送回调请求到: https://example.com/callback
📥 回调响应状态码: 200
```

### 9.2 性能指标
- **回调成功率**：成功发送 / 总发送次数
- **平均响应时间**：回调HTTP请求平均耗时
- **重试次数分布**：各重试次数的统计

## 10. 最佳实践

### 10.1 接收端实现
- **幂等性**：支持重复接收同一回调
- **快速响应**：尽快返回2xx状态码，避免超时重试
- **错误处理**：妥善处理异常数据格式

### 10.2 URL配置
- **HTTPS优先**：生产环境使用HTTPS回调URL
- **路径规范**：使用专门的回调接收路径
- **参数验证**：验证data、code、info三个必需参数

### 10.3 开发调试
- **本地测试**：使用ngrok等工具暴露本地服务
- **日志监控**：关注回调发送日志，及时发现问题
- **数据验证**：检查 `workflow_runs.callback_data` 字段确认存储

## 11. 故障排查

### 11.1 常见问题
1. **回调未收到**：检查URL可访问性、网络连接
2. **数据格式错误**：检查JSON解析、字段类型
3. **重复回调**：检查接收端响应状态码

### 11.2 排查步骤
1. 查看工作流执行日志
2. 检查回调发送日志
3. 验证URL可访问性
4. 检查数据库存储记录
5. 分析接收端日志

### 11.3 调试工具
- **日志查询**：`grep "回调" server/log/*.log`
- **数据库查询**：检查 `workflow_runs.callback_data` 字段
- **网络测试**：使用curl测试回调URL连通性

---

## 总结

工作流回调机制通过统一的数据格式、可靠的重试机制和完善的错误处理，确保了工作流执行结果的及时、准确传递。特别是修复了失败时的数据填充和存储问题，使整个回调系统更加健壮和可靠。 