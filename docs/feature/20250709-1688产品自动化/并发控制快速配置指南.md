# 工作流并发控制快速配置指南

## 🚀 快速开始

### 1. 配置文件设置

在 `server/config.yaml` 文件中添加以下配置：

```yaml
# 工作流并发控制配置
workflow-concurrency:
  max-global-workers: 2           # 全局最大并发工作线程数（推荐2-5）
  default-batch-concurrency: 2    # 默认批量并发数（不能超过全局最大值）
  enable-metrics: true            # 是否启用指标收集
  metrics-reset-interval: 24      # 指标重置间隔（小时）
  queue-timeout-ms: 30000         # 队列等待超时时间（毫秒）
  enable-health-check: true       # 是否启用健康检查
```

### 2. 重启服务

```bash
# 重启后端服务以应用配置
cd server
go run main.go
```

### 3. 验证配置

```bash
# 检查系统健康状态
curl http://localhost:8888/api/workflow/health

# 查看并发控制状态
curl http://localhost:8888/api/workflow/concurrency/status
```

## ⚙️ 配置说明

| 配置项 | 推荐值 | 说明 |
|--------|--------|------|
| `max-global-workers` | 2 | 最关键配置，控制全局最大并发数 |
| `default-batch-concurrency` | 2 | 单批次内部并发数，不超过全局值 |
| `queue-timeout-ms` | 30000 | 30秒超时，防止请求无限等待 |

## 🔧 根据环境调整

### 小规模测试环境
```yaml
workflow-concurrency:
  max-global-workers: 1
  default-batch-concurrency: 1
```

### 生产环境
```yaml
workflow-concurrency:
  max-global-workers: 2
  default-batch-concurrency: 2
```

### 高性能环境
```yaml
workflow-concurrency:
  max-global-workers: 3
  default-batch-concurrency: 2
```

## 📊 监控检查

### 查看实时状态
```bash
curl http://localhost:8888/api/workflow/concurrency/status | jq
```

### 健康检查
```bash
curl http://localhost:8888/api/workflow/health | jq
```

### 重置指标
```bash
curl -X POST http://localhost:8888/api/workflow/concurrency/reset-metrics
```

## ⚠️ 注意事项

1. **并发数不宜过高**：建议不超过5个，避免底层服务过载
2. **监控系统负载**：关注 `utilization` 和 `queued_requests` 指标
3. **及时调整配置**：根据实际使用情况动态调整并发数
4. **定期重置指标**：避免指标数据累积过多影响性能

## 🆘 故障排查

### 问题1：请求排队时间过长
```bash
# 检查当前并发状态
curl http://localhost:8888/api/workflow/concurrency/status

# 解决方案：适当增加 max-global-workers
```

### 问题2：系统负载过高
```bash
# 检查系统负载
curl http://localhost:8888/api/workflow/health

# 解决方案：降低 max-global-workers
```

### 问题3：配置不生效
```bash
# 重启服务
cd server && go run main.go

# 检查配置加载
grep "全局并发控制器初始化" server/log/server.log
```

## 📝 日志查看

```bash
# 查看并发控制相关日志
tail -f server/log/server.log | grep -E "🔧|🎯|✅|❌"

# 查看工作流执行日志
tail -f server/log/server.log | grep "WorkflowRunID"
```

## 🎯 效果验证

配置正确后，您应该看到：

1. **日志显示**：`🔧 全局并发控制器初始化完成: MaxWorkers=2`
2. **并发限制**：同时执行的工作流不超过配置的数量
3. **排队机制**：超出并发限制的请求会排队等待
4. **监控指标**：可以通过API查看实时并发状态

## 🔗 相关文档

- [完整技术文档](./工作流批量执行并发控制方案.md)
- [批量执行接口文档](./接口文档：工作流批量执行接口.md)
- [工作流回调机制](./20250720-工作流回调机制详解.md) 