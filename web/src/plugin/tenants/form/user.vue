
<template>
  <div>
    <div class="gva-form-box">
      <el-form :model="formData" ref="elFormRef" label-position="right" :rules="rule" label-width="80px">
        <el-form-item label="租户登录名:" prop="user_name">
          <el-input v-model="formData.user_name" :clearable="true"  placeholder="请输入租户登录名" />
       </el-form-item>
        <el-form-item label="租户登录密码:" prop="password">
          <el-input v-model="formData.password" :clearable="true"  placeholder="请输入租户登录密码" />
       </el-form-item>
        <el-form-item label="启用状态:" prop="enable">
          <el-switch v-model="formData.enable" active-color="#13ce66" inactive-color="#ff4949" active-text="是" inactive-text="否" clearable ></el-switch>
       </el-form-item>
        <el-form-item>
          <el-button :loading="btnLoading" type="primary" @click="save">保存</el-button>
          <el-button type="primary" @click="back">返回</el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script setup>
import {
  createUser,
  updateUser,
  findUser
} from '@/plugin/tenants/api/user'

defineOptions({
    name: 'UserForm'
})

// 自动获取字典
import { getDictFunc } from '@/utils/format'
import { useRoute, useRouter } from "vue-router"
import { ElMessage } from 'element-plus'
import { ref, reactive } from 'vue'


const route = useRoute()
const router = useRouter()

// 提交按钮loading
const btnLoading = ref(false)

const type = ref('')
const formData = ref({
            user_name: '',
            password: '',
            enable: false,
        })
// 验证规则
const rule = reactive({
               user_name : [{
                   required: true,
                   message: '请输入租户登录名',
                   trigger: ['input','blur'],
               },
               {
                   whitespace: true,
                   message: '不能只输入空格',
                   trigger: ['input','blur'],
               },
               { min: 5, max: 50, message: '长度在 5 到 50 个字符', trigger: 'blur' }],
               password : [{
                   required: true,
                   message: '请输入租户登录密码',
                   trigger: ['input','blur'],
               },
               {
                   whitespace: true,
                   message: '不能只输入空格',
                   trigger: ['input','blur'],
               },
               { min: 6, max: 50, message: '长度在 6 到 50 个字符', trigger: 'blur' }],
               enable : [{
                   required: true,
                   message: '',
                   trigger: ['input','blur'],
               }],
})

const elFormRef = ref()

// 初始化方法
const init = async () => {
 // 建议通过url传参获取目标数据ID 调用 find方法进行查询数据操作 从而决定本页面是create还是update 以下为id作为url参数示例
    if (route.query.id) {
      const res = await findUser({ ID: route.query.id })
      if (res.code === 0) {
        formData.value = res.data
        type.value = 'update'
      }
    } else {
      type.value = 'create'
    }
}

init()
// 保存按钮
const save = async() => {
      btnLoading.value = true
      elFormRef.value?.validate( async (valid) => {
         if (!valid) return btnLoading.value = false
            let res
           switch (type.value) {
             case 'create':
               res = await createUser(formData.value)
               break
             case 'update':
               res = await updateUser(formData.value)
               break
             default:
               res = await createUser(formData.value)
               break
           }
           btnLoading.value = false
           if (res.code === 0) {
             ElMessage({
               type: 'success',
               message: '创建/更改成功'
             })
           }
       })
}

// 返回按钮
const back = () => {
    router.go(-1)
}

</script>

<style>
</style>
