<template>
  <div>
    <!-- 添加按钮 -->
    <el-button type="primary" @click="dialogVisible = true">添加租户配置</el-button>
    <el-table ref="multipleTable" style="width: 100%" tooltip-effect="dark" :data="tableData" row-key="ID">
      <el-table-column type="selection" width="55" />
      <el-table-column label="租户ID" prop="tenant_id" />
      <el-table-column label="自启动" prop="is_auto_start"/>

      <el-table-column label="有效期" width="180">
        <template #default="scope">
          {{ formatDate(scope.row.expiration_date) }}
        </template>
      </el-table-column>
      <el-table-column label="配置描述" prop="description" />
      <el-table-column label="主数据库描述" prop="database_id">
        <template #default="scope">
          <span v-if="scope.row.database">{{ scope.row.database.description }}</span>
        </template>

      </el-table-column>
      <el-table-column label="数据库名称" prop="db_name" />
      <el-table-column label="角色ID" prop="authority_id">
        <template #default="scope">
          {{ GetAuthorityName(scope.row.authority_id) }}
        </template>
      </el-table-column>
      <el-table-column label="是否自定义角色" prop="is_customized_authority" />
      <el-table-column label="启动状态">
        <template #default="scope">
          <el-tag :type="scope.row.is_running ? 'success' : ''" link>
            {{ scope.row.is_running ? '已启动' : '未启动' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column align="left" label="操作" fixed="right" min-width="240">
        <template #default="scope">
          <el-button type="primary" link class="table-button" @click="openTenantRow(scope.row)">开启</el-button>
          <el-button type="primary" link class="table-button" @click="restartTenantRow(scope.row)">重启</el-button>
          <el-button type="primary" link class="table-button" @click="closeTenantRow(scope.row)">关闭</el-button>
          <el-button type="primary" link class="table-button" @click="InitTenantRow(scope.row)">初始化服务器</el-button>
          <el-button type="primary" link class="table-button" @click="EditTenantRow(scope.row)">修改配置</el-button>
          <el-button type="primary" link class="table-button" @click="hotupdataDialogVisibleOpen(scope.row)">热更新</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="gva-pagination">
      <el-pagination layout="total, sizes, prev, pager, next, jumper" :current-page="page" :page-size="pageSize"
        :page-sizes="[10, 30, 50, 100]" :total="total" @current-change="handleCurrentChange"
        @size-change="handleSizeChange" />
    </div>


    <!-- 弹窗 -->
    <el-dialog v-model="dialogVisible" title="租户配置" width="70%" :close-on-click-modal="false"
      :close-on-press-escape="false" @closed="handleDialogClosed">
      <el-form ref="rulesRef" :model="formData" :rules="rules" label-width="120px">

        <el-tabs v-model="activeTab" type="card">
          <!-- 租户配置 -->
          <el-tab-pane label="租户配置" name="tenant">
            <el-form-item label="租户" prop="user_id">
              <el-select v-model="formData.user_id" placeholder="请选择租户" style="width: 360px">
                <el-option v-for="user in UserList" :key="user.ID" :label="user.user_name" :value="user.ID" />
              </el-select>
            </el-form-item>
            <el-form-item label="租户登录ID" prop="tenant_id">
              <el-input v-model="formData.tenant_id" placeholder="请输入租户登录ID，租户登录唯一标识" />
            </el-form-item>
            <el-form-item label="随系统启动" prop="is_auto_start">
              <el-switch v-model="formData.is_auto_start" />
            </el-form-item>
            <el-form-item label="有效期">
              <el-date-picker v-model="formData.expiration_date" type="datetime" placeholder="选择有效期"></el-date-picker>
            </el-form-item>
            <el-form-item label="描述" prop="description">
              <el-input v-model="formData.description" type="textarea" :rows="3" placeholder="请输入描述" />
            </el-form-item>
          </el-tab-pane>

          <!-- 数据库配置 -->
          <el-tab-pane label="数据库配置" name="database">
            <el-form-item label="数据库ID" prop="database_id">
              <el-select v-model="formData.database_id" placeholder="请选择数据库主机" style="width: 360px">
                <el-option v-for="database in DatabaseList" :key="database.value" :label="database.label"
                  :value="database.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="库名" prop="db_name">
              <el-input v-model="formData.db_name" placeholder="请输入数据库名称" />
            </el-form-item>
          </el-tab-pane>

          <!-- JWT配置 -->
          <el-tab-pane label="JWT配置" name="jwt">
            <el-form-item label="签名密钥" prop="signing_key">
              <el-input v-model="formData.signing_key" placeholder="请输入JWT签名密钥">
                <template #append>
                  <el-button @click="getUUID">生成</el-button>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="缓冲时间" prop="buffer_time">
              <el-input v-model="formData.buffer_time" placeholder="请输入缓冲时间" />
            </el-form-item>
            <el-form-item label="过期时间" prop="expires_time">
              <el-input v-model="formData.expires_time" placeholder="请输入过期时间" />
            </el-form-item>
            <el-form-item label="签发者" prop="issuer">
              <el-input v-model="formData.issuer" placeholder="请输入签发者" />
            </el-form-item>
          </el-tab-pane>

          <!-- 角色选择 -->
          <el-tab-pane label="角色选择" name="role">
            <el-form-item label="角色ID" prop="authority_id">
              <el-cascader v-model="formData.authority_id" placeholder="请选择角色" :options="AuthorityList"
                :show-all-levels="false" collapse-tags :props="{
                  multiple: false,
                  checkStrictly: true,
                  label: 'authorityName',
                  value: 'authorityId',
                  disabled: 'disabled',
                  emitPath: false
                }" />
            </el-form-item>
            <el-form-item label=" 自定义角色">
              <el-switch v-model="formData.is_customized_authority" />
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取消</el-button>
          <el-button type="primary" @click="handleSubmit">提交</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 修改配置弹窗 -->
    <el-dialog v-model="EditDialogVisible" title="修改租户配置" width="70%" :close-on-click-modal="false"
      :close-on-press-escape="false" @closed="CloseEditDialog">
      <el-form ref="EditRulesRef" :model="EditFormData" :rules="EditRules" label-width="120px">

        <el-tabs v-model="activeTab" type="card">
          <!-- 租户配置 -->
          <el-tab-pane label="租户配置" name="tenant">
            <el-form-item label="租户" prop="user_id">
              <el-select v-model="EditFormData.user_id" placeholder="请选择租户" style="width: 360px" disabled>
                <el-option v-for="user in UserList" :key="user.ID" :label="user.user_name" :value="user.ID" />
              </el-select>
            </el-form-item>
            <el-form-item label="租户登录ID" prop="tenant_id">
              <el-input v-model="EditFormData.tenant_id" placeholder="请输入租户登录ID，租户登录唯一标识" style="width: 360px;" />
              <span class="ml-5" style="color: #909399;">修改后需重启租户服务器</span>
            </el-form-item>
            <el-form-item label="随系统启动" prop="is_auto_start">
              <el-switch v-model="EditFormData.is_auto_start" />
            </el-form-item>
            <el-form-item label="有效期">
              <el-date-picker v-model="EditFormData.expiration_date" type="datetime" placeholder="选择有效期"></el-date-picker>
            </el-form-item>
            <el-form-item label="描述" prop="description">
              <el-input v-model="EditFormData.description" type="textarea" :rows="3" placeholder="请输入描述" />
            </el-form-item>
          </el-tab-pane>

          <!-- 数据库配置 -->
          <el-tab-pane label="数据库配置" name="database">
            <el-form-item label="数据库ID" prop="database_id">
              <el-select v-model="EditFormData.database_id" placeholder="请选择数据库主机" style="width: 360px" disabled>
                <el-option v-for="database in DatabaseList" :key="database.value" :label="database.label"
                  :value="database.value" />
              </el-select>
            </el-form-item>
            <el-form-item label="库名" prop="db_name" >
              <el-input v-model="EditFormData.db_name" placeholder="请输入数据库名称" disabled/>
            </el-form-item>
          </el-tab-pane>

          <!-- JWT配置 -->
          <el-tab-pane label="JWT配置" name="jwt">
            <el-form-item label="签名密钥" prop="signing_key">
              <el-input v-model="EditFormData.signing_key" placeholder="请输入JWT签名密钥">
                <template #append>
                  <el-button @click="getUUID">生成</el-button>
                </template>
              </el-input>
            </el-form-item>
            <el-form-item label="缓冲时间" prop="buffer_time">
              <el-input v-model="EditFormData.buffer_time" placeholder="请输入缓冲时间" />
            </el-form-item>
            <el-form-item label="过期时间" prop="expires_time">
              <el-input v-model="EditFormData.expires_time" placeholder="请输入过期时间" />
            </el-form-item>
            <el-form-item label="签发者" prop="issuer">
              <el-input v-model="EditFormData.issuer" placeholder="请输入签发者" />
            </el-form-item>
          </el-tab-pane>

          <!-- 角色选择 -->
          <el-tab-pane label="角色选择" name="role">
            <el-form-item label="角色ID" prop="authority_id">
              <el-cascader v-model="EditFormData.authority_id" placeholder="请选择角色" :options="AuthorityList"
                :show-all-levels="false" collapse-tags :props="{
                  multiple: false,
                  checkStrictly: true,
                  label: 'authorityName',
                  value: 'authorityId',
                  disabled: 'disabled',
                  emitPath: false
                }" />
                <span class="ml-5" style="color: #909399;">修改后需要重新初始化才生效，注意！初始化会清空租户数据库所有数据</span>
            </el-form-item>
            <el-form-item label=" 自定义角色">
              <el-switch v-model="EditFormData.is_customized_authority" />
            </el-form-item>
          </el-tab-pane>
        </el-tabs>
      </el-form>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="CloseEditDialog">取消</el-button>
          <el-button type="primary" @click="EditSubmit">提交</el-button>
        </span>
      </template>
    </el-dialog>


    <el-dialog v-model="hotupdataDialogVisible" title="热更新" width="70%" :close-on-click-modal="false">
      <hotupdata v-model="currentRow" />
    </el-dialog>


  </div>
</template>

<script setup>
import {
  getDatabaseList,
} from '@/plugin/tenants/api/database'
import {
  createConfig,
  getConfigList,
  openTenant,
  closeTenant,
  restartTenant,
  initTenant,
  editConfig,
} from '@/plugin/tenants/api/config'
import {
  getUserList
} from '@/plugin/tenants/api/user'
import hotupdata from '@/plugin/tenants/components/hotupdata.vue'
import { getDictFunc, formatDate, formatBoolean, filterDict, filterDataSource, returnArrImg, onDownloadFile } from '@/utils/format'
import { getAuthorityList } from '@/api/authority'
import { ref, reactive, nextTick } from 'vue'
import { CreateUUID } from '@/utils/format'
import { ElMessage, ElMessageBox } from 'element-plus'


const dialogVisible = ref(false)
const activeTab = ref('tenant')



const AuthorityList = ref([])
const GetAuthorityListFunc = async () => {
  const res = await getAuthorityList()
  res.code == 0 ? AuthorityList.value = res.data : AuthorityList.value = []
}
GetAuthorityListFunc()

const UserList = ref([])
const GetUserListFunc = async () => {
  const res = await getUserList({ page: 0, pageSize: 1000, })
  res.code == 0 ? UserList.value = res.data.list : UserList.value = []
}
GetUserListFunc()

const GetAuthorityName = (ID) => {
  let name = ''
  AuthorityList.value.forEach(item => {
    if (item.authorityId == ID) {
      name = item.authorityName
    }
  })
  return name
}


const DatabaseList = ref([])
const GetDatabaseListFunc = async () => {
  const res = await getDatabaseList()
  if (res.code === 0) {
    DatabaseList.value = res.data.list.map(item => ({
      value: item.ID,
      label: "type:" + item.type + "   host:" + item.host + ":" + item.port + "   user:" + item.username,
    }))
  }
}
GetDatabaseListFunc()

// 表单引用
const rulesRef = ref()
// 表单数据
const formData = reactive({
  user_id: '',
  tenant_id: '',
  description: '',
  expiration_date: null,
  database_id: '',
  db_name: '',
  authority_id: '',
  is_customized_authority: false,
  signing_key: '',
  buffer_time: '7d',
  expires_time: '1d',
  issuer: 'qmPlus',
  is_auto_start: false,
})

// 表单验证规则
const rules = reactive({
  user_id: [
    { required: true, message: '请选择租户', trigger: 'change' }
  ],
  tenant_id: [
    { required: true, message: '请输入租户ID', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入描述', trigger: 'blur' },
    { max: 500, message: '长度不能超过 500 个字符', trigger: 'blur' }
  ],
  database_id: [
    { required: true, message: '请输入数据库ID', trigger: 'blur' }
  ],
  db_name: [
    { required: true, message: '请输入数据库名称', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  signing_key: [
    { required: true, message: '请输入签名密钥', trigger: 'blur' },
    { min: 32, message: '长度不能少于 32 个字符', trigger: 'blur' }
  ],
  buffer_time: [
    { required: true, message: '请输入缓冲时间', trigger: 'blur' }
  ],
  expires_time: [
    { required: true, message: '请输入过期时间', trigger: 'blur' }
  ],
  issuer: [
    { required: true, message: '请输入签发者', trigger: 'blur' }
  ],
  authority_id: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
})


// 提交表单
const handleSubmit = async () => {
  rulesRef.value?.validate(async (valid) => {
    if (!valid) return
    let res = await createConfig(formData)
    if (res.code === 0) {
      ElMessage.success(res.msg)
      dialogVisible.value = false
      GetConfigListFunc()
    }
  })
}

// 弹窗关闭时重置表单
const handleDialogClosed = () => {
  resetForm()
  activeTab.value = 'tenant'
}


// 生成UUID
const getUUID = () => {
  formData.signing_key = CreateUUID()
  nextTick(() => {
    jwtForm.value.validateField('signing_key')
  })
}

// =========== 表格控制部分 ===========
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])

const GetConfigListFunc = async () => {
  const res = await getConfigList({
    page: page.value,
    pageSize: pageSize.value,
  })
  if (res.code === 0) {
    tableData.value = res.data.list
    total.value = res.data.total
    page.value = res.data.page
    pageSize.value = res.data.pageSize
  }
}
GetConfigListFunc()

const handleCurrentChange = (val) => {
  page.value = val
  GetConfigListFunc()
}

const handleSizeChange = (val) => {
  pageSize.value = val
  GetConfigListFunc()
}

//开启租户服务器
const openTenantRow = (row) => {
  ElMessageBox.confirm('确定要打开ID：' + row.ID + '的服务器吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    let res = await openTenant({ ID: row.ID })
    if (res.code == 0) {
      ElMessage({
        type: 'success',
        message: '操作成功'
      })
      GetConfigListFunc()
    }
  })
}
//关闭租户服务器
const closeTenantRow = (row) => {
  ElMessageBox.confirm('确定要关闭ID：' + row.ID + '的服务器吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    let res = await closeTenant({ ID: row.ID })
    if (res.code == 0) {
      ElMessage({
        type: 'success',
        message: '操作成功'
      })
      GetConfigListFunc()
    }
  })
}

//重启租户服务器
const restartTenantRow = (row) => {
  ElMessageBox.confirm('确定要重启ID：' + row.ID + '的服务器吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    let res = await restartTenant({ ID: row.ID })
    if (res.code == 0) {
      ElMessage({
        type: 'success',
        message: '操作成功'
      })
      GetConfigListFunc()
    }
  })
}

//初始化租户服务器
const InitTenantRow = async (row) => {
  ElMessageBox.confirm('初始化会清空所有数据库，确定清空ID：' + row.ID + '的服务器吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    let res = await initTenant({ ID: row.ID })
    if (res.code == 0) {
      ElMessage({
        type: 'success',
        message: '操作成功'
      })
      GetConfigListFunc()
    }
  })
}

//修改配置

const EditDialogVisible = ref(false)
const EditFormData = ref({
   user_id: '',
  tenant_id: '',
  description: '',
  expiration_date: null,
  database_id: '',
  db_name: '',
  authority_id: '',
  is_customized_authority: false,
  signing_key: '',
  buffer_time: '',
  expires_time: '',
  issuer: ''
})
const EditRulesRef = ref(null)
const EditRules = reactive({
  user_id: [
    { required: true, message: '请选择租户', trigger: 'change' }
  ],
  tenant_id: [
    { required: true, message: '请输入租户ID', trigger: 'blur' },
    { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
  ],
  description: [
    { required: true, message: '请输入描述', trigger: 'blur' },
    { max: 500, message: '长度不能超过 500 个字符', trigger: 'blur' }
  ],
  database_id: [
    { required: true, message: '请输入数据库ID', trigger: 'blur' }
  ],
  db_name: [
    { required: true, message: '请输入数据库名称', trigger: 'blur' },
    { pattern: /^[a-zA-Z0-9_]+$/, message: '只能包含字母、数字和下划线', trigger: 'blur' }
  ],
  signing_key: [
    { required: true, message: '请输入签名密钥', trigger: 'blur' },
    { min: 32, message: '长度不能少于 32 个字符', trigger: 'blur' }
  ],
  buffer_time: [
    { required: true, message: '请输入缓冲时间', trigger: 'blur' }
  ],
  expires_time: [
    { required: true, message: '请输入过期时间', trigger: 'blur' }
  ],
  issuer: [
    { required: true, message: '请输入签发者', trigger: 'blur' }
  ],
  authority_id: [
    { required: true, message: '请选择角色', trigger: 'change' }
  ]
})

//打开修改弹窗
const EditTenantRow = (row) => {
  EditDialogVisible.value = true
  EditFormData.value = row
}
//关闭修改弹窗
const CloseEditDialog = () => {
  EditDialogVisible.value = false
  EditFormData.value = {
    user_id: '',
    tenant_id: '',
    description: '',
    expiration_date: null,
    database_id: '',
    db_name: '',
    authority_id: '',
    is_customized_authority: false,
    signing_key: '',
    buffer_time: '',
    expires_time: '',
    issuer: ''
  }
}
//提交修改
const EditSubmit = async () => {
  EditRulesRef.value?.validate(async (valid) => {
    if (!valid) return
    let res = await editConfig(EditFormData.value)
    if (res.code == 0) {
      ElMessage({
        type: 'success',
        message: '操作成功'
      })
      GetConfigListFunc()
      EditDialogVisible.value = false
    }
  })
}

// 定义当前选择的 rowid
const currentRow = ref(null)
//热更新弹窗
const hotupdataDialogVisible =ref(false)
const hotupdataDialogVisibleOpen = (row)=>{ 
  currentRow.value = row
  hotupdataDialogVisible.value = true
}





</script>

<style scoped>
.dialog-footer {
  display: flex;
  justify-content: flex-end;
}
</style>