<template>
    <div>
        <div v-if="model">
            <el-descriptions title="配置信息" border>
                <el-descriptions-item label="租户ID" :span="1">
                    {{ model.tenant_id }}
                </el-descriptions-item>
                <el-descriptions-item label="描述" :span="1">
                    {{ model.description }}
                </el-descriptions-item>
                <el-descriptions-item label="数据库名称" :span="1">
                    {{ model.db_name }}
                </el-descriptions-item>
            </el-descriptions>
        </div>
        <div class="mt-5">
            <el-tabs v-model="activeTab" type="card" @tab-change="tabChange">
                <el-tab-pane label="数据库" name="table">
                    <!-- 左右分栏布局 -->
                    <el-row :gutter="20">
                        <!-- 左边表格：TabelList -->
                        <el-col :span="12">
                            <div class="table-container">
                                <h3>待更新列表</h3>
                                <el-table :data="TabelList" style="width: 100%" border
                                    @selection-change="handleSelectionChange">
                                    <el-table-column type="selection" width="55" align="center"
                                        :selectable="isSelectable" />
                                    <el-table-column prop="struct_name" label="表名" width="180" />
                                    <el-table-column prop="version" label="版本" width="180" />
                                    <el-table-column prop="description" label="描述" width="180" />
                                </el-table>
                                <el-button type="primary" @click="handleUpdate" style="margin-top: 10px;">更新</el-button>
                            </div>
                        </el-col>

                        <!-- 右边表格：HistoryList -->
                        <el-col :span="12">
                            <div class="table-container">
                                <h3>历史更新记录</h3>
                                <el-table :data="HistoryList" style="width: 100%" border>
                                    <el-table-column prop="struct_name" label="表名" width="180" />
                                    <el-table-column prop="version" label="版本" width="180" />
                                    <el-table-column prop="description" label="描述" width="180" />
                                    <!-- <el-table-column prop="updated_at" label="更新时间" width="180" /> -->
                                </el-table>
                            </div>
                        </el-col>
                    </el-row>
                </el-tab-pane>
                <el-tab-pane label="菜单" name="menu">
                    <el-row>
                        <el-col :span="12">
                            <div class="table-container">
                                <h3>服务商菜单</h3>
                                <el-table :data="menuData.gvaMenu" row-key="ID"
                                    @selection-change="handleMenuSelectionChange">
                                    <el-table-column type="selection" width="55"></el-table-column>
                                    <el-table-column align="left" label="ID" min-width="100" prop="ID" />
                                    <el-table-column align="left" label="展示名称" min-width="120" prop="authorityName">
                                        <template #default="scope">
                                            <span>
                                                <el-icon>
                                                    <component :is="scope.row.meta.icon" />
                                                </el-icon>
                                                {{ scope.row.meta.title }}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column align="left" label="路由Name" show-overflow-tooltip min-width="160"
                                        prop="name" />
                                    <el-table-column align="left" label="文件路径" min-width="360" prop="component" />
                                </el-table>
                            </div>
                        </el-col>
                        <el-col :span="12">
                            <div class="table-container">
                                <h3>租户菜单</h3>
                                <el-table :data="menuData.tenantMenu" row-key="ID">
                                    <el-table-column align="left" label="ID" min-width="100" prop="ID" />
                                    <el-table-column align="left" label="展示名称" min-width="120" prop="authorityName">
                                        <template #default="scope">
                                            <span>
                                                <el-icon>
                                                    <component :is="scope.row.meta.icon" />
                                                </el-icon>
                                                {{ scope.row.meta.title }}</span>
                                        </template>
                                    </el-table-column>
                                    <el-table-column align="left" label="路由Name" show-overflow-tooltip min-width="160"
                                        prop="name" />
                                    <el-table-column align="left" label="文件路径" min-width="360" prop="component" />
                                </el-table>
                            </div>
                        </el-col>
                    </el-row>
                </el-tab-pane>
                <el-tab-pane label="api" name="api">
                    <el-row>
                        <el-col :span="12">
                            <el-table :data="apiData.gvaApi">
                                <!-- GVA API表格列定义 -->
                                <el-table-column prop="ID" label="ID" width="55"></el-table-column>
                                <el-table-column prop="path" label="API路径"></el-table-column>
                                <el-table-column prop="method" label="请求方法"></el-table-column>
                                <el-table-column prop="description" label="描述"></el-table-column>
                            </el-table>
                        </el-col>
                        <el-col :span="12">
                            <el-table :data="apiData.tenantApi">
                                <!-- 租户API表格列定义 -->
                                <el-table-column prop="ID" label="ID" width="55"></el-table-column>
                                <el-table-column prop="path" label="API路径"></el-table-column>
                                <el-table-column prop="method" label="请求方法"></el-table-column>
                                <el-table-column prop="description" label="描述"></el-table-column>
                            </el-table>
                        </el-col>
                    </el-row>
                </el-tab-pane>
                <el-tab-pane label="租户角色" name="authority">
                    <el-row>
                        <el-col :span="4">
                            <el-table :data="authorityData">
                                <!-- GVA 角色表格列定义 -->
                                <el-table-column  label="ID" width="100">
                                  <template #default="scope">
                                    <span @click="getHotAuthorityDataFunc(scope.row)">{{ scope.row.authorityId }}</span>
                                  </template>
                                </el-table-column>
                                <el-table-column  label="角色名称" width="100">
                                  <template #default="scope">
                                    <span @click="getHotAuthorityDataFunc(scope.row)">{{ scope.row.authorityName }}</span>
                                  </template>
                                </el-table-column>
                            </el-table>
                        </el-col>
                        <el-col :span="10">
                            <menusApi v-model="hotAuthorityData" />
                        </el-col>
                        <el-col :span="10">
                            <apisApi v-model="hotAuthorityData" />
                        </el-col>
                    </el-row>
                </el-tab-pane>
            </el-tabs>
        </div>
    </div>
</template>
<style scoped>
.table-container {
    padding: 10px;
    background-color: #f9f9f9;
    border-radius: 4px;
}
</style>
<script setup>
import { ref, onMounted, watch } from 'vue'
import apisApi from '@/plugin/tenants/components/apis.vue'
import menusApi from '@/plugin/tenants/components/menus.vue'

import {
    getAddOrUpdateTabelList,
    hotAddOrUpdateTable,
    getAddOrUpdateTableHistoryList,
    getHotMenuDataList,
    getHotApiDataList,
    getAllAuthority,
    getHotAuthority
} from '@/plugin/tenants/api/config'
import { ElMessage, ElMessageBox } from 'element-plus'

const model = defineModel()

const TabelList = ref([])
const HistoryList = ref([])
const menuData = ref({})
const apiData = ref({})
const authorityData = ref([])
const hotAuthorityData = ref({})
const activeTab = ref("table")

const getTabelListFunc = async () => {
    let res = await getAddOrUpdateTabelList()
    res.code == 0 ? TabelList.value = res.data : TabelList.value = []
}
const getHistoryListFunc = async () => {
    let res = await getAddOrUpdateTableHistoryList({ ID: model.value.ID })
    res.code == 0 ? HistoryList.value = res.data : HistoryList.value = []
}

const multipleSelection = ref([])
const handleSelectionChange = (val) => {
    multipleSelection.value = val
}

// 新增：判断是否可选的函数
const isSelectable = (row) => {
    // 检查HistoryList中是否存在相同的struct_name
    return !HistoryList.value.some(item => item.struct_name === row.struct_name)
}

const handleUpdate = async () => {
    if (multipleSelection.value.length === 0) {
        ElMessage.error('请至少选择一项！')
        return
    }
    let res = await hotAddOrUpdateTable({
        configID: model.value.ID,
        tables: multipleSelection.value
    })
    if (res.code == 0) {
        ElMessage.success('更新成功')
    }
}

const menuMultipleSelection = ref([])
const handleMenuSelectionChange = (val) => {
    menuMultipleSelection.value = val
}

const getMenuDataFunc = async () => {
    let res = await getHotMenuDataList({ ID: model.value.ID })
    if (res.code == 0) {
        menuData.value = res.data
    }else{
        menuData.value ={}
    }
}

const getApiDataFunc = async () => {
    let res = await getHotApiDataList({ ID: model.value.ID })
    if (res.code == 0) {
        apiData.value = res.data
    }else{
        apiData.value ={}
    }
}

const getAuthorityDataFunc = async () => {
    let res = await getAllAuthority({ ID: model.value.ID })
    if (res.code == 0) {
        authorityData.value = res.data
    }else{
        authorityData.value =[]
    }
        hotAuthorityData.value =false
}

const getHotAuthorityDataFunc = async (row) => {
    let res = await getHotAuthority({ ID: model.value.ID, AuthorityID: row.authorityId })
    if (res.code == 0) {
        hotAuthorityData.value = res.data
        hotAuthorityData.value.ID =model.value.ID
        hotAuthorityData.value.authorityId =row.authorityId
    }else{
        hotAuthorityData.value =false
    }
}

const tabChange = (val) => {
    switch (val) {
        case "table":
            getTabelListFunc()
            getHistoryListFunc()
            break;
        case "menu":
            getMenuDataFunc()
            break;
        case "api":
            getApiDataFunc()
            break;
        case "authority":
            getAuthorityDataFunc()
            break;
        default:
            break;
    }


}

watch(() => model.value.ID, async (newVal, oldVal) => {
    if (newVal) {
        activeTab.value = "table"
        getTabelListFunc()
        getHistoryListFunc()
    }
})
onMounted(() => {
    if (model.value.ID) {
        activeTab.value = "table"
        getTabelListFunc()
        getHistoryListFunc()
    }
})


</script>