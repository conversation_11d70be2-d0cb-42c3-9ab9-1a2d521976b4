import service from '@/utils/request'
// @Tags Config
// @Summary 不需要鉴权的租户配置接口
// @Accept application/json
// @Produce application/json
// @Param data query request.ConfigSearch true "分页获取租户配置列表"
// @Success 200 {object} response.Response{data=object,msg=string} "获取成功"
// @Router /cf/getConfigPublic [get]
export const getConfigPublic = () => {
  return service({
    url: '/cf/getConfigPublic',
    method: 'get',
  })
}

//新增配置
export const createConfig = (data) => {
  return service({
    url: '/cf/createConfig',
    method: 'post',
    data
  })
}

//分页获取配置列表
export const getConfigList = (params) => {
  return service({
    url: '/cf/getConfigList',
    method: 'get',
    params
  })
}

//打开租户
export const openTenant = (params) => {
  return service({
    url: '/cf/openTenant',
    method: 'put',
    params
  })
}

//关闭租户
export const closeTenant = (params) => {
  return service({
    url: '/cf/closeTenant',
    method: 'put',
    params
  })
}
//重启租户
export const restartTenant = (params) => {
  return service({
    url: '/cf/restartTenant',
    method: 'put',
    params
  })
}
//初始化租户服务器
export const initTenant = (params) => {
  return service({
    url: '/cf/initTenant',
    method: 'put',
    params
  })
}

//修改配置
export const editConfig = (data) => {
  return service({
    url: '/cf/editConfig',
    method: 'put',
    data
  })
}

//获取热更新列表-数据库
export const getAddOrUpdateTabelList = () => {
  return service({
    url: '/cf/getAddOrUpdateTabelList',
    method: 'get',
  })
}

//热更新-表结构
export const hotAddOrUpdateTable = (data) => {
  return service({
    url: '/cf/hotAddOrUpdateTable',
    method: 'put',
    data
  })
}
//获取热更新历史记录
export const getAddOrUpdateTableHistoryList = (params) => {
  return service({
    url: '/cf/getAddOrUpdateTableHistoryList',
    method: 'get',
    params
  })
}
//热更新-菜单-获取列表
export const getHotMenuDataList = (params) => {
  return service({
    url: '/cf/getHotMenuDataList',
    method: 'get',
    params
  })
}
//热更新-API-获取列表
export const getHotApiDataList = (params) => {
  return service({
    url: '/cf/getHotApiDataList',
    method: 'get',
    params
  })
}
//热更新-角色-获取列表
export const getAllAuthority = (params) => {
  return service({
    url: '/cf/getAllAuthority',
    method: 'get',
    params
  })
}
//热更新-角色权限
export const getHotAuthority = (params) => {
  return service({
    url: '/cf/getHotAuthority',
    method: 'get',
    params
  })
}