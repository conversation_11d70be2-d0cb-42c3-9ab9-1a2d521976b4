import service from '@/utils/request'

//更新租户角色菜单
export const setMenuAuthority = (data) => {
  return service({
    url: '/menu/setMenuAuthority',
    method: 'put',
    data
  })
}

//更新租户角色菜单按钮
export const setAuthorityBtn = (data) => {
  return service({
    url: '/menu/setAuthorityBtn',
    method: 'put',
    data
  })
}

//获取租户角色菜单按钮
export const getAuthorityBtn = (data) => {
  return service({
    url: '/menu/getAuthorityBtn',
    method: 'get',
    params: data
  })
}