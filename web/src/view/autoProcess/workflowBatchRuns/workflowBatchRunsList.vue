<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" @keyup.enter="onSubmit">
        <el-form-item label="工作流ID" prop="workflow_id">
          <el-input v-model="searchInfo.workflow_id" placeholder="工作流ID" clearable />
        </el-form-item>
        <el-form-item label="执行用户ID" prop="executed_by">
          <el-input v-model="searchInfo.executed_by" placeholder="执行用户ID" clearable />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="searchInfo.status" placeholder="请选择状态" clearable>
            <el-option label="等待执行" value="pending" />
            <el-option label="执行中" value="running" />
            <el-option label="全部完成" value="completed" />
            <el-option label="全部失败" value="failed" />
            <el-option label="部分失败" value="partial_failed" />
            <el-option label="已取消" value="cancelled" />
          </el-select>
        </el-form-item>
        <el-form-item label="开始时间" prop="start_time">
          <el-date-picker
            v-model="searchTimeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button type="danger" icon="delete" :disabled="!multipleSelection.length" @click="onDelete">删除</el-button>
      </div>
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="id"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column align="left" label="ID" prop="id" width="80" />
        <el-table-column align="left" label="工作流ID" prop="workflow_id" width="100" />
        <el-table-column align="left" label="执行用户ID" prop="executed_by" width="100" />
        <el-table-column align="left" label="总任务数" prop="total_count" width="100" />
        <el-table-column align="left" label="成功数" prop="success_count" width="80" />
        <el-table-column align="left" label="失败数" prop="failed_count" width="80" />
        <el-table-column align="left" label="运行中" prop="running_count" width="80" />
        <el-table-column align="left" label="取消数" prop="cancelled_count" width="80" />
        <el-table-column align="left" label="状态" prop="status" width="120">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="left" label="进度" width="150">
          <template #default="scope">
            <el-progress 
              :percentage="getProgressPercentage(scope.row)" 
              :color="getProgressColor(scope.row)"
              :status="getProgressStatus(scope.row)"
            />
            <div class="progress-text">
              {{ scope.row.success_count + scope.row.failed_count + scope.row.cancelled_count }}/{{ scope.row.total_count }}
            </div>
          </template>
        </el-table-column>
        <el-table-column align="left" label="开始时间" prop="start_time" width="160">
          <template #default="scope">
            {{ formatDate(scope.row.start_time) }}
          </template>
        </el-table-column>
        <el-table-column align="left" label="结束时间" prop="end_time" width="160">
          <template #default="scope">
            {{ formatDate(scope.row.end_time) }}
          </template>
        </el-table-column>
        <el-table-column align="left" label="操作" fixed="right" width="200">
          <template #default="scope">
            <el-button type="primary" link icon="view" size="small" @click="viewDetail(scope.row)">
              查看详情
            </el-button>
            <el-button 
              v-if="scope.row.status === 'pending' || scope.row.status === 'running'"
              type="warning" 
              link 
              icon="close" 
              size="small" 
              @click="cancelBatch(scope.row)"
            >
              取消批量
            </el-button>
            <el-button type="danger" link icon="delete" size="small" @click="deleteRow(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>

    <!-- 批量执行详情弹窗 -->
    <el-dialog 
      v-model="batchDetailDialogVisible" 
      title="批量执行详情" 
      width="90%" 
      @close="batchWorkflowRuns = []"
    >
      <div v-if="currentBatchDetail">
        <!-- 批量执行基本信息 -->
        <el-descriptions :column="3" border style="margin-bottom: 20px;">
          <el-descriptions-item label="批量ID">{{ currentBatchDetail.id }}</el-descriptions-item>
          <el-descriptions-item label="工作流ID">{{ currentBatchDetail.workflow_id }}</el-descriptions-item>
          <el-descriptions-item label="执行用户ID">{{ currentBatchDetail.executed_by }}</el-descriptions-item>
          <el-descriptions-item label="总任务数">{{ currentBatchDetail.total_count }}</el-descriptions-item>
          <el-descriptions-item label="成功数">
            <el-tag type="success" size="small">{{ currentBatchDetail.success_count }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="失败数">
            <el-tag type="danger" size="small">{{ currentBatchDetail.failed_count }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="运行中">
            <el-tag type="warning" size="small">{{ currentBatchDetail.running_count }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="取消数">
            <el-tag type="info" size="small">{{ currentBatchDetail.cancelled_count }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentBatchDetail.status)" size="small">
              {{ getStatusText(currentBatchDetail.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ formatDate(currentBatchDetail.start_time) }}</el-descriptions-item>
          <el-descriptions-item label="结束时间">{{ formatDate(currentBatchDetail.end_time) }}</el-descriptions-item>
          <el-descriptions-item label="创建时间">{{ formatDate(currentBatchDetail.created_at) }}</el-descriptions-item>
        </el-descriptions>

        <!-- 工作流执行记录列表 -->
        <el-divider content-position="left">工作流执行记录</el-divider>
        <el-table
          v-loading="batchRunsLoading"
          :data="batchWorkflowRuns"
          style="width: 100%"
          tooltip-effect="dark"
          max-height="400"
        >
          <el-table-column align="left" label="执行ID" prop="ID" width="80" />
          <el-table-column align="left" label="批量索引" prop="batch_index" width="100">
            <template #default="scope">
              {{ scope.row.batch_index !== null ? scope.row.batch_index : '-' }}
            </template>
          </el-table-column>
          <el-table-column align="left" label="状态" prop="status" width="100">
            <template #default="scope">
              <el-tag :type="getWorkflowRunStatusType(scope.row.status)" size="small">
                {{ getWorkflowRunStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column align="left" label="重试次数" prop="retry_count" width="100">
            <template #default="scope">
              {{ scope.row.retry_count || 0 }}
            </template>
          </el-table-column>
          <el-table-column align="left" label="执行时长" prop="duration" width="120">
            <template #default="scope">
              {{ formatWorkflowDuration(scope.row.duration) }}
            </template>
          </el-table-column>
          <el-table-column align="left" label="开始时间" prop="created_at" width="160">
            <template #default="scope">
              {{ formatDate(scope.row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column align="left" label="结束时间" prop="end_time" width="160">
            <template #default="scope">
              {{ formatDate(scope.row.end_time) }}
            </template>
          </el-table-column>
          <el-table-column align="left" label="错误信息" prop="error" min-width="200" show-overflow-tooltip>
            <template #default="scope">
              <span v-if="scope.row.error" class="error-text">{{ scope.row.error }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  getWorkflowBatchRunsList,
  deleteWorkflowBatchRuns,
  deleteWorkflowBatchRunsByIds
} from '@/api/autoProcess/workflowBatchRuns'
import { getWorkflowRunsList } from '@/api/autoProcess/workflowRuns'
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

defineOptions({
  name: 'WorkflowBatchRunsList'
})

// 响应式数据
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})
const searchTimeRange = ref([])

// 批量详情相关数据
const batchDetailDialogVisible = ref(false)
const currentBatchDetail = ref(null)
const batchWorkflowRuns = ref([])
const batchRunsLoading = ref(false)

// 多选
const multipleSelection = ref([])
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

// 重置
const onReset = () => {
  searchInfo.value = {}
  searchTimeRange.value = []
  getTableData()
}

// 搜索
const onSubmit = () => {
  // 处理时间范围
  if (searchTimeRange.value && searchTimeRange.value.length === 2) {
    searchInfo.value.start_time_start = searchTimeRange.value[0]
    searchInfo.value.start_time_end = searchTimeRange.value[1]
  } else {
    delete searchInfo.value.start_time_start
    delete searchInfo.value.start_time_end
  }
  
  page.value = 1
  getTableData()
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async () => {
  const table = await getWorkflowBatchRunsList({ 
    page: page.value, 
    pageSize: pageSize.value, 
    ...searchInfo.value 
  })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

// 删除行
const deleteRow = (row) => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteWorkflowBatchRunsFunc(row)
  })
}

// 多选删除
const onDelete = async () => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    const IDs = []
    if (multipleSelection.value.length === 0) {
      ElMessage({
        type: 'warning',
        message: '请选择要删除的数据'
      })
      return
    }
    multipleSelection.value &&
      multipleSelection.value.map(item => {
        IDs.push(item.id)
      })
    const res = await deleteWorkflowBatchRunsByIds(IDs)
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '删除成功'
      })
      if (tableData.value.length === IDs.length && page.value > 1) {
        page.value--
      }
      getTableData()
    }
  })
}

// 删除单行
const deleteWorkflowBatchRunsFunc = async (row) => {
  const res = await deleteWorkflowBatchRuns({ ID: row.id })
  if (res.code === 0) {
    ElMessage({
      type: 'success',
      message: '删除成功'
    })
    if (tableData.value.length === 1 && page.value > 1) {
      page.value--
    }
    getTableData()
  }
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN', { 
    year: 'numeric', 
    month: '2-digit', 
    day: '2-digit', 
    hour: '2-digit', 
    minute: '2-digit', 
    second: '2-digit' 
  })
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    pending: 'info',
    running: 'warning', 
    completed: 'success',
    failed: 'danger',
    partial_failed: 'warning',
    cancelled: 'info'
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    pending: '等待执行',
    running: '执行中',
    completed: '全部完成',
    failed: '全部失败',
    partial_failed: '部分失败',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

// 获取进度百分比
const getProgressPercentage = (row) => {
  if (row.total_count === 0) return 0
  const completedCount = row.success_count + row.failed_count + row.cancelled_count
  return Math.round((completedCount / row.total_count) * 100)
}

// 获取进度条颜色
const getProgressColor = (row) => {
  const percentage = getProgressPercentage(row)
  if (percentage === 100) {
    if (row.failed_count === 0 && row.cancelled_count === 0) {
      return '#67c23a' // 全部成功 - 绿色
    } else if (row.success_count === 0) {
      return '#f56c6c' // 全部失败 - 红色
    } else {
      return '#e6a23c' // 部分失败 - 橙色
    }
  }
  return '#409eff' // 进行中 - 蓝色
}

// 获取进度条状态
const getProgressStatus = (row) => {
  const percentage = getProgressPercentage(row)
  if (percentage === 100 && row.failed_count === 0 && row.cancelled_count === 0) {
    return 'success'
  }
  return null
}

// 查看详情
const viewDetail = (row) => {
  currentBatchDetail.value = row
  batchDetailDialogVisible.value = true
  // 加载该批量执行的工作流执行记录
  getBatchWorkflowRuns(row.id)
}

// 获取批量执行的工作流执行记录
const getBatchWorkflowRuns = async (batchId) => {
  batchRunsLoading.value = true
  try {
    const res = await getWorkflowRunsList({
      batch_id: batchId,
      page: 1,
      pageSize: 1000 // 获取该批量的所有记录
    })
    if (res.code === 0) {
      batchWorkflowRuns.value = res.data.list || []
    }
  } catch (error) {
    console.error('获取批量工作流执行记录失败:', error)
    ElMessage.error('获取批量工作流执行记录失败')
  } finally {
    batchRunsLoading.value = false
  }
}

// 工作流执行状态格式化
const getWorkflowRunStatusType = (status) => {
  const statusMap = {
    1: 'warning', // 运行中
    2: 'success', // 成功
    3: 'danger'   // 失败
  }
  return statusMap[status] || 'info'
}

const getWorkflowRunStatusText = (status) => {
  const statusMap = {
    1: '运行中',
    2: '成功',
    3: '失败'
  }
  return statusMap[status] || status
}

// 格式化工作流执行时长
const formatWorkflowDuration = (milliseconds) => {
  if (!milliseconds) return '-'
  if (milliseconds < 1000) {
    return `${milliseconds}ms`
  } else if (milliseconds < 60000) {
    return `${(milliseconds / 1000).toFixed(2)}s`
  } else {
    const minutes = Math.floor(milliseconds / 60000)
    const seconds = ((milliseconds % 60000) / 1000).toFixed(0)
    return `${minutes}m ${seconds}s`
  }
}

// 取消批量执行
const cancelBatch = (row) => {
  ElMessageBox.confirm('确定要取消这个批量执行吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    // 调用取消批量执行的API
    ElMessage.info('取消批量执行功能待实现')
  })
}

onMounted(() => {
  getTableData()
})
</script>

<style scoped>
.progress-text {
  font-size: 12px;
  color: #666;
  text-align: center;
  margin-top: 2px;
}
</style> 