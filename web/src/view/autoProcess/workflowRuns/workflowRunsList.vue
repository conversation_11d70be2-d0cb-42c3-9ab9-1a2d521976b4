<template>
  <div>
    <div class="gva-search-box">
      <el-form ref="elSearchFormRef" :inline="true" :model="searchInfo" class="demo-form-inline" @keyup.enter="onSubmit">
        <el-form-item label="工作流ID" prop="workflow_id">
          <el-input v-model="searchInfo.workflow_id" placeholder="工作流ID" clearable />
        </el-form-item>
        <el-form-item label="执行用户ID" prop="executed_by">
          <el-input v-model="searchInfo.executed_by" placeholder="执行用户ID" clearable />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-select v-model="searchInfo.status" placeholder="请选择状态" clearable>
            <el-option label="运行中" :value="1" />
            <el-option label="成功" :value="2" />
            <el-option label="失败" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="批量执行ID" prop="batch_id">
          <el-input v-model="searchInfo.batch_id" placeholder="批量执行ID" clearable />
        </el-form-item>
        <el-form-item label="创建时间" prop="created_time">
          <el-date-picker
            v-model="searchTimeRange"
            type="datetimerange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            clearable
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="search" @click="onSubmit">查询</el-button>
          <el-button icon="refresh" @click="onReset">重置</el-button>
        </el-form-item>
      </el-form>
    </div>
    <div class="gva-table-box">
      <div class="gva-btn-list">
        <el-button type="danger" icon="delete" :disabled="!multipleSelection.length" @click="onDelete">删除</el-button>
      </div>
      <el-table
        ref="multipleTable"
        style="width: 100%"
        tooltip-effect="dark"
        :data="tableData"
        row-key="ID"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column align="left" label="ID" prop="ID" width="80" />
        <el-table-column align="left" label="工作流ID" prop="workflowId" width="100" />
        <el-table-column align="left" label="执行用户ID" prop="executedBy" width="100" />
        <el-table-column align="left" label="状态" prop="status" width="100">
          <template #default="scope">
            <el-tag :type="getStatusType(scope.row.status)" size="small">
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column align="left" label="批量执行ID" prop="batchId" width="120">
          <template #default="scope">
            {{ scope.row.batchId || '-' }}
          </template>
        </el-table-column>
        <el-table-column align="left" label="批量索引" prop="batchIndex" width="100">
          <template #default="scope">
            {{ scope.row.batchIndex !== null ? scope.row.batchIndex : '-' }}
          </template>
        </el-table-column>
        <el-table-column align="left" label="重试次数" prop="retryCount" width="100">
          <template #default="scope">
            {{ scope.row.retryCount || 0 }}
          </template>
        </el-table-column>
        <el-table-column align="left" label="执行时长" prop="elapsedTime" width="120">
          <template #default="scope">
            {{ formatDuration(scope.row.elapsedTime) }}
          </template>
        </el-table-column>
        <el-table-column align="left" label="开始时间" prop="created_at" width="160">
          <template #default="scope">
            {{ formatDate(scope.row.created_at) }}
          </template>
        </el-table-column>
        <el-table-column align="left" label="结束时间" prop="endTime" width="160">
          <template #default="scope">
            {{ formatDate(scope.row.endTime) }}
          </template>
        </el-table-column>
        <el-table-column align="left" label="操作" fixed="right" width="200">
          <template #default="scope">
            <el-button type="primary" link icon="view" size="small" @click="viewDetail(scope.row)">
              查看详情
            </el-button>
            <el-button 
              type="success" 
              link 
              icon="refresh-right" 
              size="small" 
              @click="retryWorkflow(scope.row)"
              :disabled="scope.row.status !== 3"
              :title="scope.row.status === 3 ? '重试失败的工作流' : (scope.row.status === 1 ? '运行中无法重试' : '成功状态无需重试')"
            >
              重试
            </el-button>
            <el-button type="danger" link icon="delete" size="small" @click="deleteRow(scope.row)">
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="gva-pagination">
        <el-pagination
          layout="total, sizes, prev, pager, next, jumper"
          :current-page="page"
          :page-size="pageSize"
          :page-sizes="[10, 30, 50, 100]"
          :total="total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>

    <!-- 详情弹窗 -->
    <el-dialog v-model="detailDialogVisible" title="工作流执行详情" width="80%">
      <div v-if="currentDetail">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="执行ID">{{ currentDetail.ID }}</el-descriptions-item>
          <el-descriptions-item label="工作流ID">{{ currentDetail.workflowId }}</el-descriptions-item>
          <el-descriptions-item label="执行用户ID">{{ currentDetail.executedBy }}</el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusType(currentDetail.status)" size="small">
              {{ getStatusText(currentDetail.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="批量执行ID">{{ currentDetail.batchId || '-' }}</el-descriptions-item>
          <el-descriptions-item label="批量索引">{{ currentDetail.batchIndex !== null ? currentDetail.batchIndex : '-' }}</el-descriptions-item>
          <el-descriptions-item label="重试次数">{{ currentDetail.retryCount || 0 }}</el-descriptions-item>
          <el-descriptions-item label="执行时长">{{ formatDuration(currentDetail.elapsedTime) }}</el-descriptions-item>
          <el-descriptions-item label="开始时间">{{ formatDate(currentDetail.created_at) }}</el-descriptions-item>
          <el-descriptions-item label="结束时间">{{ formatDate(currentDetail.endTime) }}</el-descriptions-item>
          <el-descriptions-item label="回调URL" span="2">{{ currentDetail.callbackURL || '-' }}</el-descriptions-item>
          <el-descriptions-item v-if="currentDetail.error" label="错误信息" span="2">
            <span class="error-text">{{ currentDetail.error }}</span>
          </el-descriptions-item>
        </el-descriptions>
        
        <el-divider content-position="left">输入数据</el-divider>
        <el-input
          v-model="inputDataStr"
          type="textarea"
          :rows="8"
          readonly
          placeholder="暂无输入数据"
        />
        
        <el-divider content-position="left">输出数据</el-divider>
        <el-input
          v-model="outputDataStr"
          type="textarea"
          :rows="8"
          readonly
          placeholder="暂无输出数据"
        />

        <!-- 节点执行记录列表 -->
        <el-divider content-position="left">节点执行记录</el-divider>
        <el-table
          v-loading="nodeExecutionsLoading"
          :data="nodeExecutions"
          style="width: 100%"
          tooltip-effect="dark"
          max-height="400"
        >
          <el-table-column align="left" label="节点ID" prop="ID" width="80" />
          <el-table-column align="left" label="节点名称" prop="nodeName" width="150" />
          <el-table-column align="left" label="节点类型" prop="nodeType" width="120" />
          <el-table-column align="left" label="执行顺序" prop="index" width="100" />
          <el-table-column align="left" label="状态" prop="status" width="100">
            <template #default="scope">
              <el-tag :type="getNodeStatusType(scope.row.status)" size="small">
                {{ getNodeStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column align="left" label="执行时长" prop="duration" width="120">
            <template #default="scope">
              {{ formatNodeDuration(scope.row.duration) }}
            </template>
          </el-table-column>
          <el-table-column align="left" label="开始时间" prop="created_at" width="160">
            <template #default="scope">
              {{ formatDate(scope.row.created_at) }}
            </template>
          </el-table-column>
          <el-table-column align="left" label="结束时间" prop="endTime" width="160">
            <template #default="scope">
              {{ formatDate(scope.row.endTime) }}
            </template>
          </el-table-column>
          <el-table-column align="left" label="错误信息" prop="error" min-width="200" show-overflow-tooltip>
            <template #default="scope">
              <span v-if="scope.row.error" class="error-text">{{ scope.row.error }}</span>
              <span v-else>-</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import {
  getWorkflowRunsList,
  deleteWorkflowRuns,
  deleteWorkflowRunsByIds,
  batchRunWorkflow,
  getWorkflowNodeExecutionsByRunID
} from '@/api/autoProcess/workflowRuns'
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'

defineOptions({
  name: 'WorkflowRunsList'
})

// 响应式数据
const page = ref(1)
const total = ref(0)
const pageSize = ref(10)
const tableData = ref([])
const searchInfo = ref({})
const searchTimeRange = ref([])
const detailDialogVisible = ref(false)
const currentDetail = ref(null)

// 节点执行记录相关数据
const nodeExecutions = ref([])
const nodeExecutionsLoading = ref(false)

// 多选
const multipleSelection = ref([])
const handleSelectionChange = (val) => {
  multipleSelection.value = val
}

// 计算属性
const inputDataStr = computed(() => {
  return currentDetail.value?.inputs ? JSON.stringify(currentDetail.value.inputs, null, 2) : ''
})

const outputDataStr = computed(() => {
  return currentDetail.value?.outputs ? JSON.stringify(currentDetail.value.outputs, null, 2) : ''
})

// 重置
const onReset = () => {
  searchInfo.value = {}
  searchTimeRange.value = []
  getTableData()
}

// 搜索
const onSubmit = () => {
  // 处理时间范围
  if (searchTimeRange.value && searchTimeRange.value.length === 2) {
    searchInfo.value.created_start = searchTimeRange.value[0]
    searchInfo.value.created_end = searchTimeRange.value[1]
  } else {
    delete searchInfo.value.created_start
    delete searchInfo.value.created_end
  }
  
  page.value = 1
  getTableData()
}

// 分页
const handleSizeChange = (val) => {
  pageSize.value = val
  getTableData()
}

const handleCurrentChange = (val) => {
  page.value = val
  getTableData()
}

// 查询
const getTableData = async () => {
  const table = await getWorkflowRunsList({ 
    page: page.value, 
    pageSize: pageSize.value, 
    ...searchInfo.value 
  })
  if (table.code === 0) {
    tableData.value = table.data.list
    total.value = table.data.total
    page.value = table.data.page
    pageSize.value = table.data.pageSize
  }
}

// 删除行
const deleteRow = (row) => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    deleteWorkflowRunsFunc(row)
  })
}

// 多选删除
const onDelete = async () => {
  ElMessageBox.confirm('确定要删除吗?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    const IDs = []
    if (multipleSelection.value.length === 0) {
      ElMessage({
        type: 'warning',
        message: '请选择要删除的数据'
      })
      return
    }
    multipleSelection.value &&
      multipleSelection.value.map(item => {
        IDs.push(item.ID)
      })
    const res = await deleteWorkflowRunsByIds(IDs)
    if (res.code === 0) {
      ElMessage({
        type: 'success',
        message: '删除成功'
      })
      if (tableData.value.length === IDs.length && page.value > 1) {
        page.value--
      }
      getTableData()
    }
  })
}

// 删除单行
const deleteWorkflowRunsFunc = async (row) => {
  const res = await deleteWorkflowRuns({ ID: row.ID })
  if (res.code === 0) {
    ElMessage({
      type: 'success',
      message: '删除成功'
    })
    if (tableData.value.length === 1 && page.value > 1) {
      page.value--
    }
    getTableData()
  }
}

// 格式化日期
const formatDate = (date) => {
  if (!date) return '-'
  return new Date(date).toLocaleString('zh-CN', { 
    year: 'numeric', 
    month: '2-digit', 
    day: '2-digit', 
    hour: '2-digit', 
    minute: '2-digit', 
    second: '2-digit' 
  })
}

// 格式化时长
const formatDuration = (milliseconds) => {
  if (!milliseconds) return '-'
  if (milliseconds < 1000) {
    return `${milliseconds}ms`
  } else if (milliseconds < 60000) {
    return `${(milliseconds / 1000).toFixed(2)}s`
  } else {
    const minutes = Math.floor(milliseconds / 60000)
    const seconds = ((milliseconds % 60000) / 1000).toFixed(0)
    return `${minutes}m ${seconds}s`
  }
}

// 获取状态类型
const getStatusType = (status) => {
  const statusMap = {
    1: 'warning', // 运行中
    2: 'success', // 成功
    3: 'danger'   // 失败
  }
  return statusMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    1: '运行中',
    2: '成功',
    3: '失败'
  }
  return statusMap[status] || status
}

// 查看详情
const viewDetail = (row) => {
  currentDetail.value = row
  detailDialogVisible.value = true
  // 加载节点执行记录
  getNodeExecutions(row.ID)
}

// 获取节点执行记录
const getNodeExecutions = async (workflowRunID) => {
  nodeExecutionsLoading.value = true
  try {
    const res = await getWorkflowNodeExecutionsByRunID({
      workflow_run_id: workflowRunID
    })
    if (res.code === 0) {
      nodeExecutions.value = res.data || []
    }
  } catch (error) {
    console.error('获取节点执行记录失败:', error)
    ElMessage.error('获取节点执行记录失败')
  } finally {
    nodeExecutionsLoading.value = false
  }
}

// 节点执行状态格式化
const getNodeStatusType = (status) => {
  const statusMap = {
    1: 'info',    // 等待
    2: 'warning', // 运行中
    3: 'success', // 成功
    4: 'danger',  // 失败
    5: 'info'     // 跳过
  }
  return statusMap[status] || 'info'
}

const getNodeStatusText = (status) => {
  const statusMap = {
    1: '等待',
    2: '运行中',
    3: '成功',
    4: '失败',
    5: '跳过'
  }
  return statusMap[status] || status
}

// 格式化节点执行时长
const formatNodeDuration = (milliseconds) => {
  if (!milliseconds) return '-'
  if (milliseconds < 1000) {
    return `${milliseconds}ms`
  } else if (milliseconds < 60000) {
    return `${(milliseconds / 1000).toFixed(2)}s`
  } else {
    const minutes = Math.floor(milliseconds / 60000)
    const seconds = ((milliseconds % 60000) / 1000).toFixed(0)
    return `${minutes}m ${seconds}s`
  }
}

// 重试工作流
const retryWorkflow = async (row) => {
  ElMessageBox.confirm('确定要重试此工作流吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    try {
      // 构建重试请求参数，参考 BatchRunWorkflowReq 结构
      const retryData = {
        workflow_id: row.workflowId,
        inputs: [row.inputs || {}] // 批量执行需要数组格式
        // 可选字段如 assigned_to, priority, callback_url 在重试时不添加
      }
      
      const res = await batchRunWorkflow(retryData)
      // 注意：批量执行API使用ReturnWithDataCodeInfo，返回格式为{code, data, info}
      if (res.code === 200) {
        ElMessage({
          type: 'success',
          message: '重试请求已提交'
        })
        // 刷新列表
        getTableData()
      } else {
        ElMessage({
          type: 'error',
          message: res.info || res.msg || '重试失败'
        })
      }
    } catch (error) {
      console.error('重试失败:', error)
      ElMessage({
        type: 'error',
        message: '重试失败'
      })
    }
  })
}

onMounted(() => {
  getTableData()
})
</script>

<style scoped>
.error-text {
  color: #f56c6c;
  word-break: break-all;
}

.gva-search-box {
  padding: 16px;
  background: #fff;
  border-radius: 4px;
  margin-bottom: 16px;
}

.gva-table-box {
  padding: 16px;
  background: #fff;
  border-radius: 4px;
}

.gva-btn-list {
  margin-bottom: 16px;
}

.gva-pagination {
  display: flex;
  justify-content: flex-end;
  margin-top: 16px;
}
</style> 