{"/src/view/about/index.vue": "About", "/src/view/autoProcess/task/processTaskDemo.vue": "ProcessTaskDemo", "/src/view/autoProcess/task/task.vue": "Task", "/src/view/autoProcess/task/taskForm.vue": "TaskForm", "/src/view/autoProcess/task/taskList.vue": "TaskList", "/src/view/autoProcess/workflowBatchRuns/workflowBatchRunsList.vue": "WorkflowBatchRunsList", "/src/view/autoProcess/workflowRuns/workflowRunsList.vue": "WorkflowRunsList", "/src/view/autoProcess/workflows/workflowsList.vue": "WorkflowsList", "/src/view/dashboard/components/banner.vue": "Banner", "/src/view/dashboard/components/card.vue": "Card", "/src/view/dashboard/components/charts-content-numbers.vue": "ChartsContentNumbers", "/src/view/dashboard/components/charts-people-numbers.vue": "ChartsPeopleNumbers", "/src/view/dashboard/components/charts.vue": "Charts", "/src/view/dashboard/components/notice.vue": "Notice", "/src/view/dashboard/components/pluginTable.vue": "PluginTable", "/src/view/dashboard/components/quickLinks.vue": "QuickLinks", "/src/view/dashboard/components/table.vue": "Table", "/src/view/dashboard/components/wiki.vue": "Wiki", "/src/view/dashboard/index.vue": "Dashboard", "/src/view/error/index.vue": "Error", "/src/view/error/reload.vue": "Reload", "/src/view/example/breakpoint/breakpoint.vue": "BreakPoint", "/src/view/example/customer/customer.vue": "Customer", "/src/view/example/index.vue": "Example", "/src/view/example/upload/scanUpload.vue": "scanUpload", "/src/view/example/upload/upload.vue": "Upload", "/src/view/init/index.vue": "Init", "/src/view/layout/aside/asideComponent/asyncSubmenu.vue": "AsyncSubmenu", "/src/view/layout/aside/asideComponent/index.vue": "AsideComponent", "/src/view/layout/aside/asideComponent/menuItem.vue": "MenuItem", "/src/view/layout/aside/combinationMode.vue": "GvaAside", "/src/view/layout/aside/headMode.vue": "GvaAside", "/src/view/layout/aside/index.vue": "Index", "/src/view/layout/aside/normalMode.vue": "GvaAside", "/src/view/layout/aside/sidebarMode.vue": "SidebarMode", "/src/view/layout/header/index.vue": "Index", "/src/view/layout/header/tools.vue": "Tools", "/src/view/layout/iframe.vue": "GvaLayoutIframe", "/src/view/layout/index.vue": "GvaLayout", "/src/view/layout/screenfull/index.vue": "Screenfull", "/src/view/layout/search/search.vue": "BtnBox", "/src/view/layout/setting/index.vue": "GvaSetting", "/src/view/layout/setting/title.vue": "layoutSettingTitle", "/src/view/layout/tabs/index.vue": "HistoryComponent", "/src/view/login/index.vue": "<PERSON><PERSON>", "/src/view/mcp/CallLogList.vue": "CallLogList", "/src/view/mcp/ServiceList.vue": "ServiceList", "/src/view/mcp/ToolDetail.vue": "ToolDetail", "/src/view/mcp/ToolList.vue": "ToolList", "/src/view/mcp/ToolTester.vue": "ToolTester", "/src/view/person/person.vue": "Person", "/src/view/queue/QueueDashboard.vue": "QueueDashboard", "/src/view/queue/QueueList.vue": "QueueList", "/src/view/routerHolder.vue": "RouterHolder", "/src/view/superAdmin/api/api.vue": "Api", "/src/view/superAdmin/authority/authority.vue": "Authority", "/src/view/superAdmin/authority/components/apis.vue": "Apis", "/src/view/superAdmin/authority/components/datas.vue": "Datas", "/src/view/superAdmin/authority/components/menus.vue": "Menus", "/src/view/superAdmin/dictionary/sysDictionary.vue": "SysDictionary", "/src/view/superAdmin/dictionary/sysDictionaryDetail.vue": "SysDictionaryDetail", "/src/view/superAdmin/index.vue": "SuperAdmin", "/src/view/superAdmin/menu/components/components-cascader.vue": "ComponentsCascader", "/src/view/superAdmin/menu/icon.vue": "Icon", "/src/view/superAdmin/menu/menu.vue": "Menus", "/src/view/superAdmin/operation/sysOperationRecord.vue": "SysOperationRecord", "/src/view/superAdmin/params/sysParams.vue": "SysParams", "/src/view/superAdmin/user/user.vue": "User", "/src/view/system/state.vue": "State", "/src/view/systemTools/autoCode/component/fieldDialog.vue": "FieldDialog", "/src/view/systemTools/autoCode/component/previewCodeDialog.vue": "PreviewCodeDialog", "/src/view/systemTools/autoCode/index.vue": "AutoCode", "/src/view/systemTools/autoCode/mcp.vue": "Mcp", "/src/view/systemTools/autoCode/mcpTest.vue": "McpTest", "/src/view/systemTools/autoCode/picture.vue": "Picture", "/src/view/systemTools/autoCodeAdmin/index.vue": "AutoCodeAdmin", "/src/view/systemTools/autoPkg/autoPkg.vue": "AutoPkg", "/src/view/systemTools/exportTemplate/exportTemplate.vue": "ExportTemplate", "/src/view/systemTools/formCreate/index.vue": "FormGenerator", "/src/view/systemTools/index.vue": "System", "/src/view/systemTools/installPlugin/index.vue": "Index", "/src/view/systemTools/pubPlug/pubPlug.vue": "PubPlug", "/src/view/systemTools/system/system.vue": "Config", "/src/plugin/announcement/form/info.vue": "InfoForm", "/src/plugin/announcement/view/info.vue": "Info", "/src/plugin/email/view/index.vue": "Email", "/src/plugin/tenants/form/config.vue": "Config", "/src/plugin/tenants/form/database.vue": "DatabaseForm", "/src/plugin/tenants/form/renewalLease.vue": "RenewalLeaseForm", "/src/plugin/tenants/form/user.vue": "UserForm", "/src/plugin/tenants/view/config.vue": "Config", "/src/plugin/tenants/view/database.vue": "Database", "/src/plugin/tenants/view/renewalLease.vue": "RenewalLease", "/src/plugin/tenants/view/user.vue": "User"}