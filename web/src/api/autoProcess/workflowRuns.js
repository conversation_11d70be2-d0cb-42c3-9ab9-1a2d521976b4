import service from '@/utils/request'

// @Tags WorkflowRuns
// @Summary 删除工作流执行记录
export const deleteWorkflowRuns = (params) => {
  return service({
    url: '/workflowRuns/deleteWorkflowRuns',
    method: 'delete',
    params
  })
}

// @Tags WorkflowRuns
// @Summary 批量删除工作流执行记录
export const deleteWorkflowRunsByIds = (data) => {
  return service({
    url: '/workflowRuns/deleteWorkflowRunsByIds',
    method: 'delete',
    data
  })
}

// @Tags WorkflowRuns
// @Summary 用id查询工作流执行记录
export const findWorkflowRuns = (params) => {
  return service({
    url: '/workflowRuns/findWorkflowRuns',
    method: 'get',
    params
  })
}

// @Tags WorkflowRuns
// @Summary 分页获取工作流执行记录列表
export const getWorkflowRunsList = (params) => {
  return service({
    url: '/workflowRuns/getWorkflowRunsList',
    method: 'get',
    params
  })
}

// @Tags Workflow
// @Summary 批量执行工作流 (用于重试功能)
export const batchRunWorkflow = (data) => {
  return service({
    url: '/workflows/batch_run',
    method: 'post',
    data
  })
}

// @Tags WorkflowNodeExecutions
// @Summary 根据工作流执行ID获取节点执行记录
export const getWorkflowNodeExecutionsByRunID = (params) => {
  return service({
    url: '/workflowNodeExecutions/getByRunID',
    method: 'get',
    params
  })
} 